// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../core/params/service_from_acc_detail_params.dart';
import '../../entities/service_type_purchase_detail.dart';
import '../../repositories/consultation_customer_repository.dart';

@injectable
class GetServiceTypePurchaseDetailUseCase
    implements
        UseCase<
          DataState<ServiceTypePurchaseDetail?>,
          ServiceFromAccDetailParams
        > {
  GetServiceTypePurchaseDetailUseCase(this._consultationCustomerRepository);

  final ConsultationCustomerRepository _consultationCustomerRepository;

  @override
  Future<DataState<ServiceTypePurchaseDetail?>> call({
    required final ServiceFromAccDetailParams params,
  }) {
    return _consultationCustomerRepository.getServiceTypePurchaseDetail(params);
  }
}
