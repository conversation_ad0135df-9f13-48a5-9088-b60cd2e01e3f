// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../core/params/service_combo_request_params.dart';
import '../../entities/service_type_purchase.dart';
import '../../repositories/consultation_customer_repository.dart';

@injectable
class GetComboServiceUseCase
    implements
        UseCase<DataState<List<ComboPurchase>?>, ComboServiceRequestParams> {
  GetComboServiceUseCase(this._consultationCustomerRepository);

  final ConsultationCustomerRepository _consultationCustomerRepository;

  @override
  Future<DataState<List<ComboPurchase>?>> call({
    required final ComboServiceRequestParams params,
  }) {
    return _consultationCustomerRepository.getComboService(params);
  }
}
