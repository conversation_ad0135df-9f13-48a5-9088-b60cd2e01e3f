// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../core/params/service_acc_params.dart';
import '../../entities/service_from_acc.dart';
import '../../repositories/consultation_customer_repository.dart';

@injectable
class GetServiceFromAccUseCase
    implements UseCase<DataState<ServiceFromAcc?>, ServiceAccParams> {
  GetServiceFromAccUseCase(this._consultationCustomerRepository);

  final ConsultationCustomerRepository _consultationCustomerRepository;

  @override
  Future<DataState<ServiceFromAcc?>> call({
    required final ServiceAccParams params,
  }) {
    return _consultationCustomerRepository.getServiceFromAcc(params);
  }
}
