// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../core/params/create_combo_body.dart';
import '../../repositories/consultation_customer_repository.dart';

@injectable
class CreateComboServiceUseCase
    implements UseCase<DataState<int?>, CreateComboBody> {
  CreateComboServiceUseCase(this._consultationCustomerRepository);

  final ConsultationCustomerRepository _consultationCustomerRepository;

  @override
  Future<DataState<int?>> call({required final CreateComboBody params}) {
    return _consultationCustomerRepository.createCombo(params);
  }
}
