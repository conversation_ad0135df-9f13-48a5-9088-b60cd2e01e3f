// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../core/params/service_deal_request_params.dart';
import '../../entities/service_type_purchase.dart';
import '../../repositories/consultation_customer_repository.dart';

@injectable
class GetDealServiceUseCase
    implements
        UseCase<DataState<List<DealPurchase>?>, DealServiceRequestParams> {
  GetDealServiceUseCase(this._consultationCustomerRepository);

  final ConsultationCustomerRepository _consultationCustomerRepository;

  @override
  Future<DataState<List<DealPurchase>?>> call({
    required final DealServiceRequestParams params,
  }) {
    return _consultationCustomerRepository.getDealService(params);
  }
}
