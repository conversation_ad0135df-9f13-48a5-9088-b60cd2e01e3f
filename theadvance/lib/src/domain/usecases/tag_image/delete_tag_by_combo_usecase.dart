// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../core/params/image_by_combo_delete_params.dart';
import '../../repositories/tag_image_repository.dart';

@injectable
class DeleteTagByComboTagUsecase
    implements UseCase<DataState<List<String>?>, ImageByComboTagDeleteParams> {
  DeleteTagByComboTagUsecase(this._repository);

  final TagImageRepository _repository;

  @override
  Future<DataState<List<String>?>> call({
    required final ImageByComboTagDeleteParams params,
  }) {
    return _repository.deleteTagByComboTag(params);
  }
}
