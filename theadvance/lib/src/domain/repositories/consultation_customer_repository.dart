// Package imports:
import 'package:ez_core/ez_core.dart';

// Project imports:
import '../../core/params/consultation_skin_ts_params.dart';
import '../../core/params/create_combo_body.dart';
import '../../core/params/customer_booking_treatment_om_details_request_params.dart';
import '../../core/params/fit_customer_info_body_request_params.dart';
import '../../core/params/fit_customer_info_request_params.dart';
import '../../core/params/get_consultation_ttbd_request_params.dart';
import '../../core/params/request_params.dart';
import '../../core/params/result_of_fit_body_request_params.dart';
import '../../core/params/result_of_fit_query_request_params.dart';
import '../../core/params/result_of_fit_request_params.dart';
import '../../core/params/service_acc_params.dart';
import '../../core/params/service_combo_request_params.dart';
import '../../core/params/service_deal_request_params.dart';
import '../../core/params/service_from_acc_detail_params.dart';
import '../../core/params/service_inside_ticket_params.dart';
import '../../core/params/taking_care_customer_om_treatment_part_params.dart';
import '../../core/params/treatment_note_part_params.dart';
import '../entities/customer_booking_info_service_om_details.dart';
import '../entities/entities.dart';
import '../entities/fit_customer_info.dart';
import '../entities/get_consultation_ttbd.dart';
import '../entities/result_of_fit.dart';
import '../entities/service_from_acc.dart';
import '../entities/service_inside_ticket.dart';
import '../entities/service_type_purchase.dart';
import '../entities/service_type_purchase_detail.dart';
import '../entities/skin_customer_info.dart';
import '../entities/treatment_note.dart';

abstract class ConsultationCustomerRepository {
  // API methods
  Future<DataState<NoData?>> updateConsultationTTBD(
    final GetConsultationTTBDRequestParams params,
  );
  Future<DataState<GetConsultationTTBDParentGroup?>> getConsultationNDTV(
    final GetConsultationTTBDRequestParams params,
  );
  Future<DataState<ConsultationCustomer?>> getConsultationCustomer(
    final ConsultationCustomerRequestParams params,
  );

  Future<DataState<ConsultationCustomerGetService?>>
  getServiceConsultationCustomer(
    final ConsultationCustomerGetConsultationService params,
  );

  Future<DataState<ConsultationCustomerGetAction?>>
  getActionConsultationCustomer(
    final ConsultationCustomerGetActionRequestParams params,
  );

  Future<DataState<ConsultationCustomerComplete?>> completeConsultationCustomer(
    final ConsultationCustomerCompleteRequestParams params,
  );

  Future<DataState<ConsultationCustomerProductLoad?>>
  productLoadConsultationCustomer(
    final ConsultationCustomerProductLoadRequestParams params,
  );
  Future<DataState<ConsultationTreatmentDetal?>>
  consultationCreateTreatmentDetail(
    final ConsultationTreatmentDetailsRequestParamsV2 params,
  );

  Future<DataState<ConsultationTreatmentDetal?>>
  consultationUpdateTreatmentDetail(
    final ConsultationTreatmentDetailsRequestParamsV2 params,
  );
  Future<DataState<CustomerBookingInfoServiceDetailsLoadItems?>>
  consultationGetTreatmentDetail(
    final CustomerBookingInfoServiceDetailsLoadRequestParams params,
  );
  Future<DataState<SkinCustomerInfo?>> updateSkinCustomerInfo(
    final ConsultationSkinTSParams params,
  );
  Future<DataState<SkinCustomerInfo?>> getSkinCustomerInfo(final String params);

  // Database methods
  Future<ConsultationCustomer?> getSavedConsultationCustomer();

  Future<void> saveConsultationCustomer(
    final ConsultationCustomer consultationCustomer,
  );

  Future<void> removeConsultationCustomer();

  Future<DataState<ServiceDetailEmployeeFetch?>> employeeFetchServiceDetail(
    final ServiceDetailEmployeeFetchRequestParams params,
  );
  Future<DataState<ServiceDetailDoctorFetch?>> doctorFetchServiceDetail(
    final ServiceDetailDoctorFetchRequestParams params,
  );
  Future<DataState<CustomerBookingInfoServiceOmDetailsItems?>>
  consultationCreateTreatmentOMDetail(
    final TakingCareCustomerOMTreatMentPartParams params,
  );
  Future<DataState<CustomerBookingInfoServiceOmDetailsItems?>>
  consultationGetTreatmentOMDetail(
    final CustomerBookingTreatmentOmDetailsRequestParams params,
  );
  Future<DataState<TreatmentNoteItems?>> consultationGetTreatmentNote(
    final CustomerBookingTreatmentOmDetailsRequestParams params,
  );
  Future<DataState<TreatmentNoteItems?>> consultationUpdateTreatmentNote(
    final TreatmentNotePartParams params,
  );
  Future<DataState<FitCustomerInfoItems?>> getFitCustomerInfo(
    final FitCustomerInfoRequestParams params,
  );
  Future<DataState<FitCustomerInfoItems?>> updateFitCustomerInfo(
    final FitCustomerInfoBodyRequestParams params,
  );
  Future<DataState<List<ResultOfFitItems>?>> getResultListOfFit(
    final ResultOfFitRequestParams params,
  );
  Future<DataState<List<ResultOfFitItems>?>> getResultOfFit(
    final ResultOfFitRequestParams params,
  );
  Future<DataState<ResultOfFitItems?>> updateResultOfFit(
    final ResultOfFitBodyRequestParams params,
  );
  Future<DataState<List<ResultOfFitItems>?>> deleteResultOfFit(
    final ResultOfFitQueryRequestParams params,
  );

  Future<DataState<ConsultationCustomerGetServiceUsage?>>
  getServiceUsageConsultationCustomer(
    final ConsultationCustomerGetServiceUsageRequestParams params,
  );

  Future<DataState<ServiceInsideTicketList?>> getServiceInsideTicket(
    final ServiceInsideTicketParams params,
  );

  Future<DataState<ConsultationCustomerEditService?>>
  editServiceConsultationCustomer(
    final ConsultationCustomerEditServiceRequestParams params,
  );

  Future<DataState<ConsultationCustomerRemoveService?>>
  removeServiceConsultationCustomer(
    final ConsultationCustomerRemoveServiceRequestParams params,
  );
  Future<DataState<List<ComboPurchase>?>> getComboService(
    final ComboServiceRequestParams params,
  );
  Future<DataState<List<DealPurchase>?>> getDealService(
    final DealServiceRequestParams params,
  );
  Future<DataState<int?>> createCombo(final CreateComboBody params);
  Future<DataState<int?>> updateCombo(final CreateComboBody params);

  Future<DataState<ServiceFromAcc?>> getServiceFromAcc(
    final ServiceAccParams params,
  );
  Future<DataState<ServiceTypePurchaseDetail?>> getServiceTypePurchaseDetail(
    final ServiceFromAccDetailParams params,
  );
}
