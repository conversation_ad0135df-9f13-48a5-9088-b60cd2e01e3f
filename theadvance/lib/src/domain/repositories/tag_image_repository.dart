// Package imports:
import 'package:ez_core/ez_core.dart';

// Project imports:
import '../../core/params/combo_tag_request_params.dart';
import '../../core/params/image_by_combo_delete_params.dart';
import '../../core/params/image_by_combo_tag_params.dart';
import '../entities/combo_tag.dart';
import '../entities/entities.dart';
import '../entities/image_by_combo_tag.dart';

abstract class TagImageRepository {
  // API methods
  Future<DataState<List<ComboTag>?>> getComboTag(
    final ComboTagRequestParams param,
  );

  Future<DataState<List<ImageByComboTag>?>> getImageByComboTag(
    final ImageByComboTagQueryParams param,
  );
  Future<DataState<NoData?>> deleteImageByComboTag(
    final ImageByComboTagDeleteParams param,
  );
  Future<DataState<List<String>?>> deleteTagByComboTag(
    final ImageByComboTagDeleteParams param,
  );
}
