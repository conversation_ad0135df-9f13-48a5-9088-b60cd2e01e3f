// ignore_for_file: public_member_api_docs, sort_constructors_first

// Dart imports:
import 'dart:convert';

class ServiceTypePurchaseItems {
  const ServiceTypePurchaseItems({
    this.id,
    this.name,
    this.volume,
    this.action,
    this.price,
    this.discount,
    this.note,
    this.totalPrice,
    this.servicePurchase,
  });
  final String? id;
  final String? name;
  final int? volume;
  final String? action;
  final double? price;
  final double? discount;
  final String? note;
  final double? totalPrice;
  final List<ServicePurchase>? servicePurchase;

  ServiceTypePurchaseItems copyWith({
    final String? id,
    final String? name,
    final int? volume,
    final String? action,
    final double? price,
    final double? discount,
    final String? note,
    final double? totalPrice,
    final List<ServicePurchase>? servicePurchase,
  }) {
    return ServiceTypePurchaseItems(
      id: id ?? this.id,
      name: name ?? this.name,
      volume: volume ?? this.volume,
      action: action ?? this.action,
      price: price ?? this.price,
      discount: discount ?? this.discount,
      note: note ?? this.note,
      totalPrice: totalPrice ?? this.totalPrice,
      servicePurchase: servicePurchase ?? this.servicePurchase,
    );
  }
}

class ServicePurchase {
  ServicePurchase({
    this.comboId,
    this.serviceCode,
    this.serviceName,
    this.count,
  });

  final String? serviceCode;

  final String? serviceName;

  final int? count;

  final String? comboId;

  ServicePurchase copyWith({
    final String? serviceCode,
    final String? serviceName,
    final int? count,
    final String? comboId,
  }) {
    return ServicePurchase(
      serviceCode: serviceCode ?? this.serviceCode,
      serviceName: serviceName ?? this.serviceName,
      count: count ?? this.count,
      comboId: comboId ?? this.comboId,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'ServiceCode': serviceCode,
      'ServiceName': serviceName,
      if (comboId != null) 'ComboID': comboId,
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

class ComboPurchase {
  ComboPurchase({
    this.comboID,
    this.comboName,
    this.comboTypeID,
    this.comboTypeName,
    this.items,
  });

  final String? comboID;

  final String? comboName;

  final String? comboTypeID;

  final String? comboTypeName;

  final List<ComboPurchaseItems>? items;

  ComboPurchase copyWith({
    final String? comboID,
    final String? comboName,
    final String? comboTypeID,
    final String? comboTypeName,
    final List<ComboPurchaseItems>? items,
  }) {
    return ComboPurchase(
      comboID: comboID ?? this.comboID,
      comboName: comboName ?? this.comboName,
      comboTypeID: comboTypeID ?? this.comboTypeID,
      comboTypeName: comboTypeName ?? this.comboTypeName,
      items: items ?? this.items,
    );
  }
}

class ComboPurchaseItemGroups {
  ComboPurchaseItemGroups({this.itemGroupID, this.itemGroupName});

  final String? itemGroupID;

  final String? itemGroupName;
}

class ComboPurchaseItems {
  ComboPurchaseItems({this.itemID, this.itemName, this.quantity});

  final String? itemID;

  final String? itemName;

  final int? quantity;
}

class DealPurchase {
  const DealPurchase({
    this.dealID,
    this.dealName,
    this.dealTypeID,
    this.dealTypeName,
  });
  final String? dealID;

  final String? dealName;

  final String? dealTypeID;

  final String? dealTypeName;
}
