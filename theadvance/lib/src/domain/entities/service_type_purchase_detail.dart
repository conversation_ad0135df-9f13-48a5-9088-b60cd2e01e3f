import 'service_from_acc.dart';

class ServiceTypePurchaseDetail {
  ServiceTypePurchaseDetail({
    this.rowKey,
    this.partnerID,
    this.transactionID,
    this.transactionDate,
    this.itemID,
    this.itemName,
    this.volume,
    this.totalAmount,
    this.itemIDParent,
    this.itemNameParent,
    this.itemIDChildrens,
    this.description,
    this.createdBy,
    this.createdByName,
    this.createdDate,
    this.updatedBy,
    this.updatedByName,
    this.updatedDate,
  });

  final int? rowKey;

  final String? partnerID;

  final String? transactionID;

  final String? transactionDate;

  final String? itemID;

  final String? itemName;

  final int? volume;

  final int? totalAmount;

  final String? itemIDParent;

  final String? itemNameParent;

  final List<ServiceTypePurchaseDetailChildren>? itemIDChildrens;

  final String? description;

  final String? createdBy;

  final String? createdByName;

  final String? createdDate;

  final String? updatedBy;

  final String? updatedByName;

  final String? updatedDate;
}

class ServiceTypePurchaseDetailChildren {
  ServiceTypePurchaseDetailChildren({
    this.itemID,
    this.itemName,
    this.totalRow,
  });

  final String? itemID;

  final String? itemName;

  final int? totalRow;
}
