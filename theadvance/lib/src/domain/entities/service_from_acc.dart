class ServiceFromAcc {
  ServiceFromAcc({
    required this.items,
    this.pageIndex,
    this.pageSize,
    this.totalPages,
    this.totalCount,
    this.hasPreviousPage,
    this.hasNextPage,
  });

  final List<ServiceFromAccItems?> items;

  final int? pageIndex;

  final int? pageSize;

  final int? totalPages;

  final int? totalCount;

  final bool? hasPreviousPage;

  final bool? hasNextPage;
}

class ServiceFromAccItems {
  ServiceFromAccItems({
    this.itemID,
    this.itemName,
    this.itemGroupID,
    this.itemGroupName,
    this.suggestedRetailPrice,
    this.totalRow,
  });

  final String? itemID;

  final String? itemName;

  final String? itemGroupID;

  final String? itemGroupName;

  final int? suggestedRetailPrice;

  final int? totalRow;
}
