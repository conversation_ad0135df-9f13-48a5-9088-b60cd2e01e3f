// Dart imports:
import 'dart:io';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../core/params/combo_tag_request_params.dart';
import '../../core/params/image_by_combo_delete_params.dart';
import '../../core/params/image_by_combo_tag_params.dart';
import '../../core/utils/mappers.dart';
import '../../domain/entities/combo_tag.dart';
import '../../domain/entities/image_by_combo_tag.dart';
import '../../domain/entities/no_data.dart';
import '../../domain/repositories/tag_image_repository.dart';
import '../../injector/injector.dart';
import '../datasources/remote/tag_image_service.dart';
import '../models/combo_tag_model.dart';
import '../models/image_by_combo_tag_model.dart';

@LazySingleton(as: TagImageRepository)
class TagImageRepositoryImpl implements TagImageRepository {
  TagImageRepositoryImpl(this._tagImageApiService);

  final TagImageApiService _tagImageApiService;

  @override
  Future<DataState<List<ComboTag>?>> getComboTag(
    final ComboTagRequestParams param,
  ) async {
    try {
      final httpResponse = await _tagImageApiService.getComboTag(
        param,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <ComboTagModel>[],
          ),
        );
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<ImageByComboTag>?>> getImageByComboTag(
    final ImageByComboTagQueryParams param,
  ) async {
    try {
      final httpResponse = await _tagImageApiService.getImageByComboTag(
        param,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <ImageByComboTagModel>[],
          ),
        );
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<NoData?>> deleteImageByComboTag(
    final ImageByComboTagDeleteParams param,
  ) async {
    try {
      final httpResponse = await _tagImageApiService.deleteImageByComboTag(
        param,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<String>?>> deleteTagByComboTag(
    final ImageByComboTagDeleteParams param,
  ) async {
    try {
      final httpResponse = await _tagImageApiService.deleteTagByComboTag(
        param,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(httpResponse.data?.data);
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }
}
