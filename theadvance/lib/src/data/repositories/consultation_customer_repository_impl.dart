// Dart imports:
import 'dart:io';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../core/params/consultation_skin_ts_params.dart';
import '../../core/params/create_combo_body.dart';
import '../../core/params/customer_booking_treatment_om_details_request_params.dart';
import '../../core/params/fit_customer_info_body_request_params.dart';
import '../../core/params/fit_customer_info_request_params.dart';
import '../../core/params/get_consultation_ttbd_request_params.dart';
import '../../core/params/request_params.dart';
import '../../core/params/result_of_fit_body_request_params.dart';
import '../../core/params/result_of_fit_query_request_params.dart';
import '../../core/params/result_of_fit_request_params.dart';
import '../../core/params/service_acc_params.dart';
import '../../core/params/service_combo_request_params.dart';
import '../../core/params/service_deal_request_params.dart';
import '../../core/params/service_from_acc_detail_params.dart';
import '../../core/params/service_inside_ticket_params.dart';
import '../../core/params/taking_care_customer_om_treatment_part_params.dart';
import '../../core/params/treatment_note_part_params.dart';
import '../../core/utils/mappers.dart';
import '../../domain/entities/customer_booking_info_service_om_details.dart';
import '../../domain/entities/entities.dart';
import '../../domain/entities/fit_customer_info.dart';
import '../../domain/entities/get_consultation_ttbd.dart';
import '../../domain/entities/result_of_fit.dart';
import '../../domain/entities/service_from_acc.dart';
import '../../domain/entities/service_inside_ticket.dart';
import '../../domain/entities/service_type_purchase.dart';
import '../../domain/entities/service_type_purchase_detail.dart';
import '../../domain/entities/skin_customer_info.dart';
import '../../domain/entities/treatment_note.dart';
import '../../domain/repositories/consultation_customer_repository.dart';
import '../../injector/injector.dart';
import '../datasources/local/cache/hive/ez_cache.dart';
import '../datasources/remote/api_services.dart';
import '../models/customer_booking_info_service_om_details_model.dart';
import '../models/fit_customer_info_model.dart';
import '../models/result_of_fit_model.dart';
import '../models/service_type_purchase_model.dart';
import '../models/treatment_note_model.dart';

@LazySingleton(as: ConsultationCustomerRepository)
class ConsultationCustomerRepositoryImpl
    implements ConsultationCustomerRepository {
  ConsultationCustomerRepositoryImpl(
    this._consultationCustomerApiService,
    this._ezCache,
  );

  final ConsultationCustomerApiService _consultationCustomerApiService;
  final EZCache _ezCache;

  @override
  Future<DataState<ConsultationCustomer?>> getConsultationCustomer(
    final ConsultationCustomerRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(httpResponse.data?.data);
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerGetService?>>
  getServiceConsultationCustomer(
    final ConsultationCustomerGetConsultationService params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getServiceConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(httpResponse.data?.data);
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerGetAction?>>
  getActionConsultationCustomer(
    final ConsultationCustomerGetActionRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getActionConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(httpResponse.data?.data);
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerComplete?>> completeConsultationCustomer(
    final ConsultationCustomerCompleteRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .completeConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(httpResponse.data?.data);
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerProductLoad?>>
  productLoadConsultationCustomer(
    final ConsultationCustomerProductLoadRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .productLoadConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<ConsultationCustomer?> getSavedConsultationCustomer() async {
    return _ezCache.consultationCustomerDao.getSavedConsultationCustomer();
  }

  @override
  Future<void> removeConsultationCustomer() async {
    return _ezCache.consultationCustomerDao.removeConsultationCustomer();
  }

  @override
  Future<void> saveConsultationCustomer(
    final ConsultationCustomer consultationCustomer,
  ) async {
    return _ezCache.consultationCustomerDao.saveConsultationCustomer(
      consultationCustomer,
    );
  }

  @override
  Future<DataState<ConsultationTreatmentDetal?>>
  consultationCreateTreatmentDetail(
    final ConsultationTreatmentDetailsRequestParamsV2 params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationCreateTreatmentDetail(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(ConsultationTreatmentDetal(items: []));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationTreatmentDetal?>>
  consultationUpdateTreatmentDetail(
    final ConsultationTreatmentDetailsRequestParamsV2 params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationUpdateTreatmentDetail(
            params.serviceInfo?.usageId,
            params,
            isMockUp: false,
          );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(ConsultationTreatmentDetal(items: []));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<CustomerBookingInfoServiceDetailsLoadItems?>>
  consultationGetTreatmentDetail(
    final CustomerBookingInfoServiceDetailsLoadRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationGetTreatmentDetail(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ServiceDetailEmployeeFetch?>> employeeFetchServiceDetail(
    final ServiceDetailEmployeeFetchRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .employeeFetchServiceDetail(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ServiceDetailDoctorFetch?>> doctorFetchServiceDetail(
    final ServiceDetailDoctorFetchRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .doctorFetchServiceDetail(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<CustomerBookingInfoServiceOmDetailsItems?>>
  consultationCreateTreatmentOMDetail(
    final TakingCareCustomerOMTreatMentPartParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationCreateTreatmentOMDetail(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ??
            CustomerBookingInfoServiceOmDetailsItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<CustomerBookingInfoServiceOmDetailsItems?>>
  consultationGetTreatmentOMDetail(
    final CustomerBookingTreatmentOmDetailsRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationGetTreatmentOMDetail(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ??
            CustomerBookingInfoServiceOmDetailsItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<TreatmentNoteItems?>> consultationGetTreatmentNote(
    final CustomerBookingTreatmentOmDetailsRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationGetTreatmentNote(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ?? TreatmentNoteItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<TreatmentNoteItems?>> consultationUpdateTreatmentNote(
    final TreatmentNotePartParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .consultationUpdateTreatmentNote(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ?? TreatmentNoteItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<FitCustomerInfoItems?>> getFitCustomerInfo(
    final FitCustomerInfoRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getFitCustomerInfo(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ?? FitCustomerInfoItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<FitCustomerInfoItems?>> updateFitCustomerInfo(
    final FitCustomerInfoBodyRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .updateFitCustomerInfo(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ?? FitCustomerInfoItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<ResultOfFitItems>?>> getResultListOfFit(
    final ResultOfFitRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getResultListOfFit(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <ResultOfFitItemsModel>[],
          ),
        );
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ResultOfFitItems?>> updateResultOfFit(
    final ResultOfFitBodyRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .updateResultOfFit(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        final item =
            httpResponse.data?.data?.firstOrNull ?? ResultOfFitItemsModel();
        return DataSuccess(getIt<Mapper>().tryConvert(item));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<ResultOfFitItems>?>> getResultOfFit(
    final ResultOfFitRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService.getResultOfFit(
        params,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <ResultOfFitItemsModel>[],
          ),
        );
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<ResultOfFitItems>?>> deleteResultOfFit(
    final ResultOfFitQueryRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .deleteResultOfFit(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <ResultOfFitItemsModel>[],
          ),
        );
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerGetServiceUsage?>>
  getServiceUsageConsultationCustomer(
    final ConsultationCustomerGetServiceUsageRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getServiceUsageConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ServiceInsideTicketList?>> getServiceInsideTicket(
    final ServiceInsideTicketParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getServiceInsideTicket(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<SkinCustomerInfo?>> updateSkinCustomerInfo(
    final ConsultationSkinTSParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .updateInfoOfSkin(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<SkinCustomerInfo?>> getSkinCustomerInfo(
    final String params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService.getInfoOfSkin({
        'customerID': params,
      }, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerEditService?>>
  editServiceConsultationCustomer(
    final ConsultationCustomerEditServiceRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .editServiceConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ConsultationCustomerRemoveService?>>
  removeServiceConsultationCustomer(
    final ConsultationCustomerRemoveServiceRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .removeServiceConsultationCustomer(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<NoData?>> updateConsultationTTBD(
    final GetConsultationTTBDRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .updateConsultationTTBD(
            params.topicId,
            params.partnerId,
            params.body,
            isMockUp: false,
          );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<GetConsultationTTBDParentGroup?>> getConsultationNDTV(
    final GetConsultationTTBDRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getConsultationNDTV(
            params.topicId,
            params.partnerId,
            params.query,
            isMockUp: false,
          );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }

      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<ComboPurchase>?>> getComboService(
    final ComboServiceRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getComboService(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <ComboPurchaseModel>[],
          ),
        );
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<List<DealPurchase>?>> getDealService(
    final DealServiceRequestParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService.getDealService(
        params,
        isMockUp: true,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(
          getIt<Mapper>().convertList(
            httpResponse.data?.data ?? <DealPurchaseModel>[],
          ),
        );
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<int?>> createCombo(final CreateComboBody params) async {
    try {
      final httpResponse = await _consultationCustomerApiService.createCombo(
        params,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<int?>> updateCombo(final CreateComboBody params) async {
    try {
      final httpResponse = await _consultationCustomerApiService.updateCombo(
        params,
        isMockUp: false,
      );
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }

  @override
  Future<DataState<ServiceFromAcc?>> getServiceFromAcc(
    final ServiceAccParams params,
  ) async {
    try {
      final httpResponse = await _consultationCustomerApiService
          .getServiceFromAcc(params, isMockUp: false);
      if (httpResponse.response.statusCode != HttpStatus.ok) {
        return DataFailure(
          ApiError(message: httpResponse.response.statusMessage),
        );
      }
      if (httpResponse.data?.errorCode == ErrorCodes.success) {
        return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
      }
      return DataFailure(
        ApiError(
          code: httpResponse.data?.errorCode,
          message: httpResponse.data?.errorMessage,
        ),
      );
    } on DioException catch (error) {
      return DataFailure(error.response?.apiError);
    } catch (_) {
      return const DataFailure(null);
    }
  }
  
  @override
  Future<DataState<ServiceTypePurchaseDetail?>> getServiceTypePurchaseDetail(ServiceFromAccDetailParams params) {
    // TODO: implement getServiceTypePurchaseDetail
    throw UnimplementedError();
  }

  // @override
  // Future<DataState<ServiceTypePurchaseDetail?>> getServiceTypePurchaseDetail(
  //   final ServiceFromAccDetailParams params,
  // ) async {
  //   try {
  //     final httpResponse = await _consultationCustomerApiService
  //         .getServiceTypePurchaseDetail(params, isMockUp: false);
  //     if (httpResponse.response.statusCode != HttpStatus.ok) {
  //       return DataFailure(
  //         ApiError(message: httpResponse.response.statusMessage),
  //       );
  //     }
  //     if (httpResponse.data?.errorCode == ErrorCodes.success) {
  //       return DataSuccess(getIt<Mapper>().tryConvert(httpResponse.data?.data));
  //     }
  //     return DataFailure(
  //       ApiError(
  //         code: httpResponse.data?.errorCode,
  //         message: httpResponse.data?.errorMessage,
  //       ),
  //     );
  //   } on DioException catch (error) {
  //     return DataFailure(error.response?.apiError);
  //   } catch (_) {
  //     return const DataFailure(null);
  //   }
  // }
}
