// Package imports:
import 'package:ez_core/ez_core.dart';

// Project imports:
import '../../../core/network/end_points.dart';
import '../../../core/params/consultation_skin_ts_params.dart';
import '../../../core/params/create_combo_body.dart';
import '../../../core/params/customer_booking_treatment_om_details_request_params.dart';
import '../../../core/params/fit_customer_info_body_request_params.dart';
import '../../../core/params/fit_customer_info_request_params.dart';
import '../../../core/params/get_consultation_ttbd_request_params.dart';
import '../../../core/params/request_params.dart';
import '../../../core/params/result_of_fit_body_request_params.dart';
import '../../../core/params/result_of_fit_query_request_params.dart';
import '../../../core/params/result_of_fit_request_params.dart';
import '../../../core/params/service_acc_params.dart';
import '../../../core/params/service_combo_request_params.dart';
import '../../../core/params/service_deal_request_params.dart';
import '../../../core/params/service_inside_ticket_params.dart';
import '../../../core/params/service_type_purchase_detail_request_params.dart';
import '../../../core/params/taking_care_customer_om_treatment_part_params.dart';
import '../../../core/params/treatment_note_part_params.dart';
import '../../../core/params/update_consultation_request_params.dart';
import '../../models/customer_booking_info_service_om_details_model.dart';
import '../../models/fit_customer_info_model.dart';
import '../../models/get_consultation_ttbd_model.dart';
import '../../models/models.dart';
import '../../models/result_of_fit_model.dart';
import '../../models/service_from_acc_model.dart';
import '../../models/service_inside_ticket_model.dart';
import '../../models/service_type_purchase_detail_model.dart';
import '../../models/service_type_purchase_model.dart';
import '../../models/skin_customer_info_model.dart';
import '../../models/treatment_note_model.dart';

part 'consultation_customer_api_service.g.dart';

@RestApi()
abstract class ConsultationCustomerApiService {
  factory ConsultationCustomerApiService(
    final Dio dio, {
    final String baseUrl,
  }) = _ConsultationCustomerApiService;

  @GET(EndPoints.consultationCustomer)
  Future<HttpResponse<ConsultationCustomerResponseModel?>>
  getConsultationCustomer(
    @Queries()
    final ConsultationCustomerRequestParams consultationCustomerRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.consultationCustomerGetService)
  Future<HttpResponse<ConsultationCustomerGetServiceResponseModel?>>
  getServiceConsultationCustomer(
    @Queries()
    final ConsultationCustomerGetConsultationService
    consultationCustomerGetConsultationService, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.consultationCustomerGetAction)
  Future<HttpResponse<ConsultationCustomerGetActionResponseModel?>>
  getActionConsultationCustomer(
    @Queries()
    final ConsultationCustomerGetActionRequestParams
    consultationCustomerGetActionRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.consultationCustomerComplete)
  Future<HttpResponse<ConsultationCustomerCompleteResponseModel?>>
  completeConsultationCustomer(
    @Body()
    final ConsultationCustomerCompleteRequestParams
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.consultationCustomerProductLoad)
  Future<HttpResponse<ConsultationCustomerProductLoadResponseModel?>>
  productLoadConsultationCustomer(
    @Queries()
    final ConsultationCustomerProductLoadRequestParams
    consultationCustomerProductLoadRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.consultationCreateTreatmentDetailsV2)
  Future<HttpResponse<GeneralResponseModel?>> consultationCreateTreatmentDetail(
    @Body()
    final ConsultationTreatmentDetailsRequestParamsV2
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @PUT(EndPoints.consultationUpdateTreatmentDetailsV2)
  Future<HttpResponse<GeneralResponseModel?>> consultationUpdateTreatmentDetail(
    @Path() final String? id,
    @Body()
    final ConsultationTreatmentDetailsRequestParamsV2
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.consultationCreateTreatmentDetails)
  Future<
    HttpResponse<
      GenericResponseModel<CustomerBookingInfoServiceDetailsLoadItemsModel>?
    >
  >
  consultationGetTreatmentDetail(
    @Queries()
    final CustomerBookingInfoServiceDetailsLoadRequestParams
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.serviceDetailEmployeeFetch)
  Future<HttpResponse<ServiceDetailEmployeeFetchResponseModel?>>
  employeeFetchServiceDetail(
    @Queries()
    final ServiceDetailEmployeeFetchRequestParams
    serviceDetailEmployeeFetchRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.serviceDetailDoctorFetch)
  Future<HttpResponse<ServiceDetailDoctorFetchResponseModel?>>
  doctorFetchServiceDetail(
    @Queries()
    final ServiceDetailDoctorFetchRequestParams
    serviceDetailDoctorFetchRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.createTreatmentOM)
  Future<
    HttpResponse<
      GenericResponseModel<List<CustomerBookingInfoServiceOmDetailsItemsModel>>?
    >
  >
  consultationCreateTreatmentOMDetail(
    @Body() final TakingCareCustomerOMTreatMentPartParams bodyParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getTreatmentOM)
  Future<
    HttpResponse<
      GenericResponseModel<List<CustomerBookingInfoServiceOmDetailsItemsModel>>?
    >
  >
  consultationGetTreatmentOMDetail(
    @Queries()
    final CustomerBookingTreatmentOmDetailsRequestParams
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.updateTreatNote)
  Future<HttpResponse<GenericResponseModel<List<TreatmentNoteItemsModel>>?>>
  consultationUpdateTreatmentNote(
    @Body() final TreatmentNotePartParams bodyParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getTreatNote)
  Future<HttpResponse<GenericResponseModel<List<TreatmentNoteItemsModel>>?>>
  consultationGetTreatmentNote(
    @Queries()
    final CustomerBookingTreatmentOmDetailsRequestParams
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @POST(EndPoints.updateFitCustomerInfo)
  Future<HttpResponse<GenericResponseModel<List<FitCustomerInfoItemsModel>>?>>
  updateFitCustomerInfo(
    @Body() final FitCustomerInfoBodyRequestParams bodyParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getFitCustomerInfo)
  Future<HttpResponse<GenericResponseModel<List<FitCustomerInfoItemsModel>>?>>
  getFitCustomerInfo(
    @Queries()
    final FitCustomerInfoRequestParams
    consultationCustomerCompleteRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.updateResultOfFit)
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
  updateResultOfFit(
    @Body() final ResultOfFitBodyRequestParams bodyParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getResultListOfFit)
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
  getResultListOfFit(
    @Queries() final ResultOfFitRequestParams params, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getResultOfFit)
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
  getResultOfFit(
    @Queries() final ResultOfFitRequestParams params, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @PUT(EndPoints.deleteResultOfFit)
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
  deleteResultOfFit(
    @Body() final ResultOfFitQueryRequestParams params, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getCustomerService)
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
  getCustomerServices(
    @Body() final ResultOfFitQueryRequestParams params, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @GET(EndPoints.consultationCustomerGetServiceUsage)
  Future<HttpResponse<ConsultationCustomerGetServiceUsageResponseModel?>>
  getServiceUsageConsultationCustomer(
    @Queries()
    final ConsultationCustomerGetServiceUsageRequestParams
    consultationCustomerGetServiceUsageRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @GET(EndPoints.serviceInsideTicket)
  Future<HttpResponse<GenericResponseModel<ServiceInsideTicketListModel>?>>
  getServiceInsideTicket(
    @Queries() final ServiceInsideTicketParams params, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getSkinInfo)
  Future<HttpResponse<GenericResponseModel<SkinCustomerInfoModel>?>>
  getInfoOfSkin(
    @Queries() final Map<String, String> params, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @PUT(EndPoints.updateSkinInfo)
  Future<HttpResponse<GenericResponseModel<SkinCustomerInfoModel>?>>
  updateInfoOfSkin(
    @Body() final ConsultationSkinTSParams body, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.consultationCustomerEditService)
  Future<HttpResponse<ConsultationCustomerEditServiceResponseModel?>>
  editServiceConsultationCustomer(
    @Body()
    final ConsultationCustomerEditServiceRequestParams
    consultationCustomerEditServiceRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @PUT(EndPoints.consultationCustomerRemoveService)
  Future<HttpResponse<ConsultationCustomerRemoveServiceResponseModel?>>
  removeServiceConsultationCustomer(
    @Body()
    final ConsultationCustomerRemoveServiceRequestParams
    consultationCustomerRemoveServiceRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @PUT(EndPoints.getConsultationNDTV)
  Future<HttpResponse<GenericResponseModel<NoDataModel>?>>
  updateConsultationTTBD(
    @Path() final String topicId,
    @Path() final String partnerId,
    @Body() final UpdateConsultationRequestParams? body, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @GET(EndPoints.getConsultationNDTV)
  Future<
    HttpResponse<GenericResponseModel<GetConsultationTTBDParentGroupModel>?>
  >
  getConsultationNDTV(
    @Path() final String topicId,
    @Path() final String partnerId,
    @Queries() final GetConsultationQueryRequestParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @GET(EndPoints.getComboService)
  Future<HttpResponse<GenericResponseModel<List<ComboPurchaseModel>>?>>
  getComboService(
    @Queries() final ComboServiceRequestParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @GET(EndPoints.getDealService)
  Future<HttpResponse<GenericResponseModel<List<DealPurchaseModel>>?>>
  getDealService(
    @Queries() final DealServiceRequestParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @POST(EndPoints.createCombo)
  Future<HttpResponse<GenericResponseModel<int>?>> createCombo(
    @Body() final CreateComboBody body, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @PUT(EndPoints.updateCombo)
  Future<HttpResponse<GenericResponseModel<int>?>> updateCombo(
    @Body() final CreateComboBody body, {
    @Header('isMockUp') final bool? isMockUp,
  });
  @GET(EndPoints.getAllService)
  Future<HttpResponse<GenericResponseModel<ServiceFromAccModel>?>>
  getServiceFromAcc(
    @Queries() final ServiceAccParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });

    @GET(EndPoints.getServiceTypePurchaseDetail)
  Future<HttpResponse<GenericResponseModel<ServiceTypePurchaseDetailModel>?>>
  getServiceFromAccDetail(
    @Queries() final ServiceTypePurchaseDetailRequestParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });
}
