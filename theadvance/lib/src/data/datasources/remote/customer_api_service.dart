// Package imports:
import 'package:ez_core/ez_core.dart';

// Project imports:
import '../../../core/network/ez_network.dart';
import '../../../core/params/customer_checkin_request_params.dart';
import '../../../core/params/customer_get_room_list_request_params.dart';
import '../../../core/params/customer_print_request_params.dart';
import '../../models/nd_models.dart';
import '../../models/service_type_purchase_detail_model.dart';

part 'customer_api_service.g.dart';

@RestApi()
abstract class CustomerApiService {
  factory CustomerApiService(final Dio dio, {final String baseUrl}) =
      _CustomerApiService;

  @GET(EndPoints.getCustomer)
  Future<HttpResponse<CustomerResponseModel?>> getCustomerInfoByQrCode(
    @Path() final String? customerQrCode, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getCustomerInfo)
  Future<HttpResponse<CustomerInfoResponseModel?>> getCustomerInfo(
    @Path() final String? id, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.customerCheckin)
  Future<HttpResponse<CustomerCheckinResponseModel?>> checkinCustomer(
    @Body() final CustomerCheckinRequestParams customerCheckinRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.customerGetRoomList)
  Future<HttpResponse<CustomerGetRoomListResponseModel?>> getRoomListCustomer(
    @Queries()
    final CustomerGetRoomListRequestParams customerGetRoomListRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @POST(EndPoints.customerPrint)
  Future<HttpResponse<CustomerPrintResponseModel?>> printCustomer(
    @Body() final CustomerPrintRequestParams customerPrintRequestParams, {
    @Header('isMockUp') final bool? isMockUp,
  });
}
