// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consultation_customer_api_service.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element

class _ConsultationCustomerApiService
    implements ConsultationCustomerApiService {
  _ConsultationCustomerApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<HttpResponse<ConsultationCustomerResponseModel?>>
      getConsultationCustomer(
    ConsultationCustomerRequestParams consultationCustomerRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<ConsultationCustomerResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/advisory/screen',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerGetServiceResponseModel?>>
      getServiceConsultationCustomer(
    ConsultationCustomerGetConsultationService
        consultationCustomerGetConsultationService, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerGetConsultationService.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<ConsultationCustomerGetServiceResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/advisory/service',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerGetServiceResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerGetServiceResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerGetActionResponseModel?>>
      getActionConsultationCustomer(
    ConsultationCustomerGetActionRequestParams
        consultationCustomerGetActionRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerGetActionRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<ConsultationCustomerGetActionResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/advisory/action',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerGetActionResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerGetActionResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerCompleteResponseModel?>>
      completeConsultationCustomer(
    ConsultationCustomerCompleteRequestParams
        consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(consultationCustomerCompleteRequestParams.toJson());
    final _options =
        _setStreamType<HttpResponse<ConsultationCustomerCompleteResponseModel>>(
            Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/erp/advisory/complete',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerCompleteResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerCompleteResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerProductLoadResponseModel?>>
      productLoadConsultationCustomer(
    ConsultationCustomerProductLoadRequestParams
        consultationCustomerProductLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters
        .addAll(consultationCustomerProductLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<ConsultationCustomerProductLoadResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/advisory/product',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerProductLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerProductLoadResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GeneralResponseModel?>> consultationCreateTreatmentDetail(
    ConsultationTreatmentDetailsRequestParamsV2
        consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = consultationCustomerCompleteRequestParams;
    final _options = _setStreamType<HttpResponse<GeneralResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/acc/services/create-service-usage',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GeneralResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GeneralResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GeneralResponseModel?>> consultationUpdateTreatmentDetail(
    String? id,
    ConsultationTreatmentDetailsRequestParamsV2
        consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = consultationCustomerCompleteRequestParams;
    final _options = _setStreamType<HttpResponse<GeneralResponseModel>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/acc/services/update-service-usage/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GeneralResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GeneralResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<
          HttpResponse<
              GenericResponseModel<
                  CustomerBookingInfoServiceDetailsLoadItemsModel>?>>
      consultationGetTreatmentDetail(
    CustomerBookingInfoServiceDetailsLoadRequestParams
        consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerCompleteRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<
            GenericResponseModel<
                CustomerBookingInfoServiceDetailsLoadItemsModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/treatment',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<CustomerBookingInfoServiceDetailsLoadItemsModel>?
        _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<
              CustomerBookingInfoServiceDetailsLoadItemsModel>.fromJson(
              _result.data!,
              (json) =>
                  CustomerBookingInfoServiceDetailsLoadItemsModel.fromJson(
                      json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ServiceDetailEmployeeFetchResponseModel?>>
      employeeFetchServiceDetail(
    ServiceDetailEmployeeFetchRequestParams
        serviceDetailEmployeeFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(serviceDetailEmployeeFetchRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<ServiceDetailEmployeeFetchResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/department/employees/search',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ServiceDetailEmployeeFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ServiceDetailEmployeeFetchResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ServiceDetailDoctorFetchResponseModel?>>
      doctorFetchServiceDetail(
    ServiceDetailDoctorFetchRequestParams
        serviceDetailDoctorFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(serviceDetailDoctorFetchRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<ServiceDetailDoctorFetchResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/erp/employee/doctor',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ServiceDetailDoctorFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ServiceDetailDoctorFetchResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<
          HttpResponse<
              GenericResponseModel<
                  List<CustomerBookingInfoServiceOmDetailsItemsModel>>?>>
      consultationCreateTreatmentOMDetail(
    TakingCareCustomerOMTreatMentPartParams bodyParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyParams.toJson());
    final _options = _setStreamType<
        HttpResponse<
            GenericResponseModel<
                List<CustomerBookingInfoServiceOmDetailsItemsModel>>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/add-update-treatment-fat',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<
        List<CustomerBookingInfoServiceOmDetailsItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<
              List<CustomerBookingInfoServiceOmDetailsItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<CustomerBookingInfoServiceOmDetailsItemsModel>((i) =>
                          CustomerBookingInfoServiceOmDetailsItemsModel
                              .fromJson(i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<
          HttpResponse<
              GenericResponseModel<
                  List<CustomerBookingInfoServiceOmDetailsItemsModel>>?>>
      consultationGetTreatmentOMDetail(
    CustomerBookingTreatmentOmDetailsRequestParams
        consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerCompleteRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<
            GenericResponseModel<
                List<CustomerBookingInfoServiceOmDetailsItemsModel>>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/fat-treatment-by-customer-service',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<
        List<CustomerBookingInfoServiceOmDetailsItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<
              List<CustomerBookingInfoServiceOmDetailsItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<CustomerBookingInfoServiceOmDetailsItemsModel>((i) =>
                          CustomerBookingInfoServiceOmDetailsItemsModel
                              .fromJson(i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<TreatmentNoteItemsModel>>?>>
      consultationUpdateTreatmentNote(
    TreatmentNotePartParams bodyParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyParams.toJson());
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<TreatmentNoteItemsModel>>>>(
        Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/treatment/update-treatment-note',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<TreatmentNoteItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<TreatmentNoteItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<TreatmentNoteItemsModel>((i) =>
                          TreatmentNoteItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<TreatmentNoteItemsModel>>?>>
      consultationGetTreatmentNote(
    CustomerBookingTreatmentOmDetailsRequestParams
        consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerCompleteRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<TreatmentNoteItemsModel>>>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/treatment/get-treatment-note',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<TreatmentNoteItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<TreatmentNoteItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<TreatmentNoteItemsModel>((i) =>
                          TreatmentNoteItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<FitCustomerInfoItemsModel>>?>>
      updateFitCustomerInfo(
    FitCustomerInfoBodyRequestParams bodyParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyParams.toJson());
    final _options = _setStreamType<
        HttpResponse<
            GenericResponseModel<List<FitCustomerInfoItemsModel>>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/add-update-fit-record-by-customer-id',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<FitCustomerInfoItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<FitCustomerInfoItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<FitCustomerInfoItemsModel>((i) =>
                          FitCustomerInfoItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<FitCustomerInfoItemsModel>>?>>
      getFitCustomerInfo(
    FitCustomerInfoRequestParams consultationCustomerCompleteRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(consultationCustomerCompleteRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<
            GenericResponseModel<List<FitCustomerInfoItemsModel>>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/fit-record-by-customer-id',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<FitCustomerInfoItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<FitCustomerInfoItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<FitCustomerInfoItemsModel>((i) =>
                          FitCustomerInfoItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
      updateResultOfFit(
    ResultOfFitBodyRequestParams bodyParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(bodyParams.toJson());
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>>>(
        Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/add-update-result-of-fit',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<ResultOfFitItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<ResultOfFitItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ResultOfFitItemsModel>((i) =>
                          ResultOfFitItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
      getResultListOfFit(
    ResultOfFitRequestParams params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(params.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/fit/get-result-list-of-fit-by-customer-id',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<ResultOfFitItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<ResultOfFitItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ResultOfFitItemsModel>((i) =>
                          ResultOfFitItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
      getResultOfFit(
    ResultOfFitRequestParams params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(params.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/get-result-of-fit',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<ResultOfFitItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<ResultOfFitItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ResultOfFitItemsModel>((i) =>
                          ResultOfFitItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
      deleteResultOfFit(
    ResultOfFitQueryRequestParams params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(params.toJson());
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>>>(
        Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/fit/delete-result-of-fit-by-row-id',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<ResultOfFitItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<ResultOfFitItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ResultOfFitItemsModel>((i) =>
                          ResultOfFitItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>?>>
      getCustomerServices(
    ResultOfFitQueryRequestParams params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(params.toJson());
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<List<ResultOfFitItemsModel>>>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              'get-customer-service',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<ResultOfFitItemsModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<ResultOfFitItemsModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ResultOfFitItemsModel>((i) =>
                          ResultOfFitItemsModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerGetServiceUsageResponseModel?>>
      getServiceUsageConsultationCustomer(
    ConsultationCustomerGetServiceUsageRequestParams
        consultationCustomerGetServiceUsageRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters
        .addAll(consultationCustomerGetServiceUsageRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<ConsultationCustomerGetServiceUsageResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/acc/services/get-service-usage',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerGetServiceUsageResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerGetServiceUsageResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<ServiceInsideTicketListModel>?>>
      getServiceInsideTicket(
    ServiceInsideTicketParams params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(params.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<ServiceInsideTicketListModel>>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/customer/service-list-of-customer',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<ServiceInsideTicketListModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<ServiceInsideTicketListModel>.fromJson(
              _result.data!,
              (json) => ServiceInsideTicketListModel.fromJson(
                  json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<SkinCustomerInfoModel>?>>
      getInfoOfSkin(
    Map<String, String> params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(params);
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<GenericResponseModel<SkinCustomerInfoModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/customer/skin-info',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<SkinCustomerInfoModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<SkinCustomerInfoModel>.fromJson(
              _result.data!,
              (json) =>
                  SkinCustomerInfoModel.fromJson(json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<SkinCustomerInfoModel>?>>
      updateInfoOfSkin(
    ConsultationSkinTSParams body, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _options = _setStreamType<
        HttpResponse<GenericResponseModel<SkinCustomerInfoModel>>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/customer/update-skin-info',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<SkinCustomerInfoModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<SkinCustomerInfoModel>.fromJson(
              _result.data!,
              (json) =>
                  SkinCustomerInfoModel.fromJson(json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerEditServiceResponseModel?>>
      editServiceConsultationCustomer(
    ConsultationCustomerEditServiceRequestParams
        consultationCustomerEditServiceRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = consultationCustomerEditServiceRequestParams;
    final _options = _setStreamType<
        HttpResponse<ConsultationCustomerEditServiceResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/advisory/edit-customer-service',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerEditServiceResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerEditServiceResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<ConsultationCustomerRemoveServiceResponseModel?>>
      removeServiceConsultationCustomer(
    ConsultationCustomerRemoveServiceRequestParams
        consultationCustomerRemoveServiceRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = consultationCustomerRemoveServiceRequestParams;
    final _options = _setStreamType<
        HttpResponse<ConsultationCustomerRemoveServiceResponseModel>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/advisory/delete-customer-deal',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ConsultationCustomerRemoveServiceResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : ConsultationCustomerRemoveServiceResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<NoDataModel>?>>
      updateConsultationTTBD(
    String topicId,
    String partnerId,
    UpdateConsultationRequestParams? body, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body?.toJson() ?? <String, dynamic>{});
    final _options =
        _setStreamType<HttpResponse<GenericResponseModel<NoDataModel>>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/acc/partner-detail/${topicId}/${partnerId}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<NoDataModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<NoDataModel>.fromJson(
              _result.data!,
              (json) => NoDataModel.fromJson(json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<
          HttpResponse<
              GenericResponseModel<GetConsultationTTBDParentGroupModel>?>>
      getConsultationNDTV(
    String topicId,
    String partnerId,
    GetConsultationQueryRequestParams query, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(query.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<
            GenericResponseModel<GetConsultationTTBDParentGroupModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/acc/partner-detail/${topicId}/${partnerId}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<GetConsultationTTBDParentGroupModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<GetConsultationTTBDParentGroupModel>.fromJson(
              _result.data!,
              (json) => GetConsultationTTBDParentGroupModel.fromJson(
                  json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<ComboPurchaseModel>>?>>
      getComboService(
    ComboServiceRequestParams query, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(query.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<GenericResponseModel<List<ComboPurchaseModel>>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/acc/services/get-combo-service',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<ComboPurchaseModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<ComboPurchaseModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ComboPurchaseModel>((i) =>
                          ComboPurchaseModel.fromJson(
                              i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<List<DealPurchaseModel>>?>>
      getDealService(
    DealServiceRequestParams query, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(query.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<GenericResponseModel<List<DealPurchaseModel>>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/get-deal-service',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<List<DealPurchaseModel>>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<List<DealPurchaseModel>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<DealPurchaseModel>((i) =>
                          DealPurchaseModel.fromJson(i as Map<String, dynamic>))
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<int>?>> createCombo(
    CreateComboBody body, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _options =
        _setStreamType<HttpResponse<GenericResponseModel<int>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/acc/item-bom-warehouse-event/create',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<int>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<int>.fromJson(
              _result.data!,
              (json) => json as int,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<int>?>> updateCombo(
    CreateComboBody body, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(body.toJson());
    final _options =
        _setStreamType<HttpResponse<GenericResponseModel<int>>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/acc/item-bom-warehouse-event/update/{rowKey}',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<int>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<int>.fromJson(
              _result.data!,
              (json) => json as int,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<ServiceFromAccModel>?>>
      getServiceFromAcc(
    ServiceAccParams query, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(query.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GenericResponseModel<ServiceFromAccModel>>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/acc/services/getallservices',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<ServiceFromAccModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<ServiceFromAccModel>.fromJson(
              _result.data!,
              (json) =>
                  ServiceFromAccModel.fromJson(json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GenericResponseModel<ServiceTypePurchaseDetailModel>?>>
      getServiceFromAccDetail(
    ServiceTypePurchaseDetailRequestParams query, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(query.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
            HttpResponse<GenericResponseModel<ServiceTypePurchaseDetailModel>>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/acc/item-bom-warehouse-event/get-history-details',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<ServiceTypePurchaseDetailModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<ServiceTypePurchaseDetailModel>.fromJson(
              _result.data!,
              (json) => ServiceTypePurchaseDetailModel.fromJson(
                  json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
