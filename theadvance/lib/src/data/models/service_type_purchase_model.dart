// Package imports:
import 'package:json_annotation/json_annotation.dart';

part 'service_type_purchase_model.g.dart';

@JsonSerializable(createToJson: false)
class ServiceTypePurchaseItemsModel {
  ServiceTypePurchaseItemsModel({
    this.id,
    this.name,
    this.volume,
    this.action,
    this.price,
    this.note,
    this.totalPrice,
    this.comboPurchase,
    this.dealPurchase,
  });

  factory ServiceTypePurchaseItemsModel.fromJson(
    final Map<String, dynamic> json,
  ) => _$ServiceTypePurchaseItemsModelFromJson(json);

  final String? id;

  final String? name;

  final int? volume;

  final String? action;

  final double? price;

  final String? note;

  final double? totalPrice;

  final List<ComboPurchaseModel>? comboPurchase;

  final List<DealPurchaseModel>? dealPurchase;
}

@JsonSerializable(createToJson: false)
class ServicePurchaseModel {
  ServicePurchaseModel({
    this.comboId,
    this.serviceCode,
    this.serviceName,
    this.count,
  });

  factory ServicePurchaseModel.fromJson(final Map<String, dynamic> json) =>
      _$ServicePurchaseModelFromJson(json);

  @JsonKey(name: 'ServiceCode')
  final String? serviceCode;

  @JsonKey(name: 'ServiceName')
  final String? serviceName;

  @JsonKey(name: 'Volume')
  final int? count;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? comboId;
}

@JsonSerializable(createToJson: false)
class ComboPurchaseModel {
  ComboPurchaseModel({
    this.comboID,
    this.comboName,
    this.comboTypeID,
    this.comboTypeName,
    this.items,
    this.count = 1,
  });

  factory ComboPurchaseModel.fromJson(final Map<String, dynamic> json) =>
      _$ComboPurchaseModelFromJson(json);

  @JsonKey(name: 'ComboID')
  final String? comboID;

  @JsonKey(name: 'ComboName')
  final String? comboName;

  @JsonKey(name: 'ComboTypeID')
  final String? comboTypeID;

  @JsonKey(name: 'ComboTypeName')
  final String? comboTypeName;

  @JsonKey(name: 'Items')
  final List<ComboPurchaseItemsModel>? items;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final int count;
}

@JsonSerializable(createToJson: false)
class ComboPurchaseItemGroupsModel {
  ComboPurchaseItemGroupsModel({this.itemGroupID, this.itemGroupName});

  factory ComboPurchaseItemGroupsModel.fromJson(
    final Map<String, dynamic> json,
  ) => _$ComboPurchaseItemGroupsModelFromJson(json);

  @JsonKey(name: 'ItemGroupID')
  final String? itemGroupID;

  @JsonKey(name: 'ItemGroupName')
  final String? itemGroupName;
}

@JsonSerializable(createToJson: false)
class ComboPurchaseItemsModel {
  ComboPurchaseItemsModel({this.itemID, this.itemName, this.quantity});

  factory ComboPurchaseItemsModel.fromJson(final Map<String, dynamic> json) =>
      _$ComboPurchaseItemsModelFromJson(json);

  @JsonKey(name: 'ItemID')
  final String? itemID;

  @JsonKey(name: 'ItemName')
  final String? itemName;

  @JsonKey(name: 'Quantity')
  final int? quantity;
}

@JsonSerializable(createToJson: false)
class DealPurchaseModel {
  DealPurchaseModel({
    this.dealID,
    this.dealName,
    this.dealTypeID,
    this.dealTypeName,
  });

  factory DealPurchaseModel.fromJson(final Map<String, dynamic> json) =>
      _$DealPurchaseModelFromJson(json);

  @JsonKey(name: 'DealID')
  final String? dealID;

  @JsonKey(name: 'DealName')
  final String? dealName;

  @JsonKey(name: 'DealTypeID')
  final String? dealTypeID;

  @JsonKey(name: 'DealTypeName')
  final String? dealTypeName;
}
