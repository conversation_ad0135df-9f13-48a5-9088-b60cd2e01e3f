import 'package:json_annotation/json_annotation.dart';

part 'service_type_purchase_detail_model.g.dart';

@JsonSerializable(createToJson: false, includeIfNull: false)
class ServiceTypePurchaseDetailModel {
  ServiceTypePurchaseDetailModel({
    this.rowKey,
    this.partnerID,
    this.transactionID,
    this.transactionDate,
    this.itemID,
    this.itemName,
    this.volume,
    this.totalAmount,
    this.itemIDParent,
    this.itemNameParent,
    this.itemIDChildrens,
    this.description,
    this.createdBy,
    this.createdByName,
    this.createdDate,
    this.updatedBy,
    this.updatedByName,
    this.updatedDate,
  });

  factory ServiceTypePurchaseDetailModel.fromJson(
    final Map<String, String?> json,
  ) => _$ServiceTypePurchaseDetailModelFromJson(json);

  @<PERSON>son<PERSON><PERSON>(name: 'RowKey')
  final int? rowKey;

  @Json<PERSON>ey(name: 'PartnerID')
  final String? partnerID;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'TransactionID')
  final String? transactionID;

  @J<PERSON><PERSON><PERSON>(name: 'TransactionDate')
  final String? transactionDate;

  @J<PERSON><PERSON><PERSON>(name: 'ItemID')
  final String? itemID;

  @Json<PERSON><PERSON>(name: 'ItemName')
  final String? itemName;

  @JsonKey(name: 'Volume')
  final int? volume;

  @JsonKey(name: 'TotalAmount')
  final int? totalAmount;

  @JsonKey(name: 'ItemIDParent')
  final String? itemIDParent;

  @JsonKey(name: 'ItemNameParent')
  final String? itemNameParent;

  @JsonKey(name: 'ItemIDChildrens')
  final List<ServiceTypePurchaseDetailChildrenModel>? itemIDChildrens;

  @JsonKey(name: 'Description')
  final String? description;

  @JsonKey(name: 'CreatedBy')
  final String? createdBy;

  @JsonKey(name: 'CreatedByName')
  final String? createdByName;

  @JsonKey(name: 'CreatedDate')
  final String? createdDate;

  @JsonKey(name: 'UpdatedBy')
  final String? updatedBy;

  @JsonKey(name: 'UpdatedByName')
  final String? updatedByName;

  @JsonKey(name: 'UpdatedDate')
  final String? updatedDate;
}

@JsonSerializable(createToJson: false, includeIfNull: false)
class ServiceTypePurchaseDetailChildrenModel {
  ServiceTypePurchaseDetailChildrenModel({
    this.abc,
    this.name,
    this.totalRow,
  });

  factory ServiceTypePurchaseDetailChildrenModel.fromJson(
    final Map<String, String?> json,
  ) => _$ServiceTypePurchaseDetailChildrenModelFromJson(json);

  @JsonKey(name: 'abc')
  final String? abc;

  @JsonKey(name: 'name')
  final String? name;

  @JsonKey(name: 'TotalRow')
  final int? totalRow;
}
