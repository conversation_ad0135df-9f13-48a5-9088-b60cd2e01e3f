// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_from_acc_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceFromAccModel _$ServiceFromAccModelFromJson(Map<String, dynamic> json) =>
    ServiceFromAccModel(
      items: (json['Items'] is List)
          ? (json['Items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ServiceFromAccItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
      pageIndex: double.tryParse(json['PageIndex'].toString())?.toInt(),
      pageSize: double.tryParse(json['PageSize'].toString())?.toInt(),
      totalPages: double.tryParse(json['TotalPages'].toString())?.toInt(),
      totalCount: double.tryParse(json['TotalCount'].toString())?.toInt(),
      hasPreviousPage: bool.tryParse(json['HasPreviousPage'].toString()),
      hasNextPage: bool.tryParse(json['HasNextPage'].toString()),
    );

ServiceFromAccItemsModel _$ServiceFromAccItemsModelFromJson(
  Map<String, dynamic> json,
) => ServiceFromAccItemsModel(
  itemID: json['ItemID']?.toString(),
  itemName: json['ItemName']?.toString(),
  itemGroupID: json['ItemGroupID']?.toString(),
  itemGroupName: json['ItemGroupName']?.toString(),
  suggestedRetailPrice: double.tryParse(
    json['SuggestedRetailPrice'].toString(),
  )?.toInt(),
  totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
);
