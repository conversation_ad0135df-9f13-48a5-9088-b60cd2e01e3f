// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_type_purchase_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceTypePurchaseDetailModel _$ServiceTypePurchaseDetailModelFromJson(
        Map<String, dynamic> json) =>
    ServiceTypePurchaseDetailModel(
      rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
      partnerID: json['PartnerID']?.toString(),
      transactionID: json['TransactionID']?.toString(),
      transactionDate: json['TransactionDate']?.toString(),
      itemID: json['ItemID']?.toString(),
      itemName: json['ItemName']?.toString(),
      volume: double.tryParse(json['Volume'].toString())?.toInt(),
      totalAmount: double.tryParse(json['TotalAmount'].toString())?.toInt(),
      itemIDParent: json['ItemIDParent']?.toString(),
      itemNameParent: json['ItemNameParent']?.toString(),
      itemIDChildrens: (json['ItemIDChildrens'] is List)
          ? (json['ItemIDChildrens'] as List<dynamic>?)
              ?.map((e) => ServiceTypePurchaseDetailChildrenModel.fromJson(
                  Map<String, String?>.from(e as Map)))
              .toList()
          : [],
      description: json['Description']?.toString(),
      createdBy: json['CreatedBy']?.toString(),
      createdByName: json['CreatedByName']?.toString(),
      createdDate: json['CreatedDate']?.toString(),
      updatedBy: json['UpdatedBy']?.toString(),
      updatedByName: json['UpdatedByName']?.toString(),
      updatedDate: json['UpdatedDate']?.toString(),
    );

ServiceTypePurchaseDetailChildrenModel
    _$ServiceTypePurchaseDetailChildrenModelFromJson(
            Map<String, dynamic> json) =>
        ServiceTypePurchaseDetailChildrenModel(
          abc: json['abc']?.toString(),
          name: json['name']?.toString(),
          totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
        );
