// Package imports:
import 'package:json_annotation/json_annotation.dart';

part 'service_from_acc_model.g.dart';

@JsonSerializable(createToJson: false)
class ServiceFromAccModel {
  ServiceFromAccModel({
    required this.items,
    this.pageIndex,
    this.pageSize,
    this.totalPages,
    this.totalCount,
    this.hasPreviousPage,
    this.hasNextPage,
  });

  factory ServiceFromAccModel.fromJson(final Map<String, dynamic> json) =>
      _$ServiceFromAccModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'Items')
  final List<ServiceFromAccItemsModel?> items;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'PageIndex')
  final int? pageIndex;

  @J<PERSON><PERSON><PERSON>(name: 'PageSize')
  final int? pageSize;

  @Json<PERSON>ey(name: 'TotalPages')
  final int? totalPages;

  @Json<PERSON>ey(name: 'TotalCount')
  final int? totalCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'HasPreviousPage')
  final bool? hasPreviousPage;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'HasNextPage')
  final bool? hasNextPage;
}

@JsonSerializable(createToJson: false)
class ServiceFromAccItemsModel {
  ServiceFromAccItemsModel({
    this.itemID,
    this.itemName,
    this.itemGroupID,
    this.itemGroupName,
    this.suggestedRetailPrice,
    this.totalRow,
  });

  factory ServiceFromAccItemsModel.fromJson(final Map<String, dynamic> json) =>
      _$ServiceFromAccItemsModelFromJson(json);

  @JsonKey(name: 'ItemID')
  final String? itemID;

  @JsonKey(name: 'ItemName')
  final String? itemName;

  @JsonKey(name: 'ItemGroupID')
  final String? itemGroupID;

  @JsonKey(name: 'ItemGroupName')
  final String? itemGroupName;

  @JsonKey(name: 'SuggestedRetailPrice')
  final int? suggestedRetailPrice;

  @JsonKey(name: 'TotalRow')
  final int? totalRow;
}
