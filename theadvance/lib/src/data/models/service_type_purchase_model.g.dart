// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_type_purchase_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceTypePurchaseItemsModel _$ServiceTypePurchaseItemsModelFromJson(
  Map<String, dynamic> json,
) => ServiceTypePurchaseItemsModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  volume: double.tryParse(json['volume'].toString())?.toInt(),
  action: json['action']?.toString(),
  price: double.tryParse(json['price'].toString()),
  note: json['note']?.toString(),
  totalPrice: double.tryParse(json['totalPrice'].toString()),
  comboPurchase: (json['comboPurchase'] is List)
      ? (json['comboPurchase'] as List<dynamic>?)
            ?.map((e) => ComboPurchaseModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  dealPurchase: (json['dealPurchase'] is List)
      ? (json['dealPurchase'] as List<dynamic>?)
            ?.map((e) => DealPurchaseModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

ServicePurchaseModel _$ServicePurchaseModelFromJson(
  Map<String, dynamic> json,
) => ServicePurchaseModel(
  serviceCode: json['ServiceCode']?.toString(),
  serviceName: json['ServiceName']?.toString(),
  count: double.tryParse(json['Volume'].toString())?.toInt(),
);

ComboPurchaseModel _$ComboPurchaseModelFromJson(Map<String, dynamic> json) =>
    ComboPurchaseModel(
      comboID: json['ComboID']?.toString(),
      comboName: json['ComboName']?.toString(),
      comboTypeID: json['ComboTypeID']?.toString(),
      comboTypeName: json['ComboTypeName']?.toString(),
      items: (json['Items'] is List)
          ? (json['Items'] as List<dynamic>?)
                ?.map(
                  (e) => ComboPurchaseItemsModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList()
          : [],
    );

ComboPurchaseItemGroupsModel _$ComboPurchaseItemGroupsModelFromJson(
  Map<String, dynamic> json,
) => ComboPurchaseItemGroupsModel(
  itemGroupID: json['ItemGroupID']?.toString(),
  itemGroupName: json['ItemGroupName']?.toString(),
);

ComboPurchaseItemsModel _$ComboPurchaseItemsModelFromJson(
  Map<String, dynamic> json,
) => ComboPurchaseItemsModel(
  itemID: json['ItemID']?.toString(),
  itemName: json['ItemName']?.toString(),
  quantity: double.tryParse(json['Quantity'].toString())?.toInt(),
);

DealPurchaseModel _$DealPurchaseModelFromJson(Map<String, dynamic> json) =>
    DealPurchaseModel(
      dealID: json['DealID']?.toString(),
      dealName: json['DealName']?.toString(),
      dealTypeID: json['DealTypeID']?.toString(),
      dealTypeName: json['DealTypeName']?.toString(),
    );
