class EndPoints {
  EndPoints._();

  static const String notificationRead = '/app/notification/read';
  static const String getNotifications = '/app/notification';
  static const String searchNotifications = '/app/notification/search';
  static const String notificationDetails = '/app/notification/detail/{id}';
  static const String searchNews = '/app/news/search';
  static const String getNews = '/app/news';
  static const String getNewsDetail = '/app/news/detail/{id}';
  static const String readAllNotifications = '/notification/read/all';
  static const String sendSupportRequest = '/app/support';
  static const String getListSupportRequest = '/app/support';
  static const String getDetailFeedback = '/rating?id=';
  static const String sendFeedback = '/rating';
  static const String appointments = '/appointments/tickets';
  static const String appointmentCancel = '/appointments/tickets/';
  static const String logout = '/app/user/logout';
  static const String navigationInfo = '/app/notification/unread-count';
  static const String collaborator = '/collaborator';
  static const String collaboratorHistory = '/collaborator/history';
  static const String confirmOTP = '/app/user/validation-otp';
  static const String getOTP = '/app/user/send-otp';
  static const String home = '/home_page';
  static const String news = '/news';
  static const String newsAction = '/news/action';
  static const String referral = '/collaborator/referral';
  static const String login = '/app/user/login';
  static const String services = '/app/home';
  static const String configurations = '/app/configurations';
  static const String listNews = '/news/hot';
  static const String newsDetails = '/news/content/';
  static const String transactionHistory = '/transactionHistory';
  static const String detailsVoucher = '/gift/detail';
  static const String vouchersRewards = '/vouchers/rewards';
  static const String events = '/events';
  static const String detailsEvent = '/events/detail';
  static const String registerEvent = '/events/register';
  static const String getDetailSubscribedEvents = '/events/register';
  static const String getHistory = '/history';
  static const String homeMenu = '/home/<USER>';
  static const String liveStream = '/livestream';
  static const String popupVoucher = '/popup/voucher';
  static const String vouchersRecommendation = '/gift/recommend';
  static const String giftRewards = '/gift/reward';
  static const String vouchersHistory = '/gift/history';
  static const String giveVoucher = '/vouchers/give';
  static const String province = '/branch/provinces';
  static const String branch = '/app/checkin/department';
  static const String appointmentDays = '/appointments/days';
  static const String timeSlot = '/appointments/time-slot';
  static const String appointmentServices = '/appointments/services';
  static const String ticket = '/appointments/tickets';
  static const String createTicket = '$ticket/create';
  static const String updateTicket = '$ticket/update';
  static const String cancelTicket = '$ticket/cancel';
  static const String importantNotesGetNoteCategory =
      '/app/workflow/important-note/category/list';
  static const String popupHome = '/popup/home';
  static const String tracking = '/app/tracking';
  static const String branchSelection = '/app/workflow/branch/list';
  static const String provinceGet = '/app/workflow/branch/province';
  static const String branchSelectionGetFloor = '/app/workflow/branch/floor';
  static const String getRoom = '/app/workflow/branch/room';
  static const String getBed = '/app/workflow/branch/bed';
  static const String selectBed = '/app/workflow/customer/bed';
  static const String customerProfileGetConsultationHistory =
      '/app/workflow/consultation-profile/list';
  static const String customerProfileUpdateConsultation =
      '/app/workflow/consultation-profile';
  static const String medicalDepartmentList =
      '/app/workflow/medical-profile/room/list';
  static const String medicalServiceList =
      '/app/workflow/medical-profile/room/detail';
  static const String medicalServiceLogList =
      '/app/workflow/medical-profile/treatment/list';
  static const String medicalLogDetail =
      '/app/workflow/medical-profile/treatment/detail';
  static const String medicalLogDetailUpdateLog =
      '/app/workflow/medical-profile/treatment';
  static const String medicineDetail = '/medicine-detail';
  static const String medicineDetailUpdateMedicine =
      '/app/workflow/medical-profile/treatment/prescription';
  static const String medicineDetailGetUnit =
      '/app/workflow/medical-profile/treatment/prescription/unit';
  static const String branchSelectionEmployeeGet =
      '/app/workflow/employee/list';
  static const String branchSelectionEstimateTimeGet =
      '/app/workflow/estimate-time/list';
  static const String medicalLogDetailMedicineListGet =
      '/app/workflow/catalog/ha-medicine/list';
  static const String medicalLogDetailDosageListGet =
      '/app/workflow/catalog/ha-dosage/list';
  static const String medicalLogDetailHaPointListGet =
      '/app/workflow/catalog/ha-point/list';
  static const String medicalLogDetailKhacnhoListGet =
      '/app/workflow/catalog/ha-kn/list';
  static const String medicalLogDetailDoctorListGet =
      '/app/workflow/doctor/list';
  static const String mason = 'mason';
  static const String medicalLogDetailGetSkinMachine =
      '/app/workflow/catalog/skin-machine/list';
  static const String medicalLogDetailGetTattooTime =
      '/app/workflow/catalog/tattoo-time/list';
  static const String medicalLogDetailGetOriginStatus =
      '/app/workflow/catalog/origin-status/list';
  static const String medicalLogDetailGetTattooColor =
      '/app/workflow/catalog/tattoo-color/list';
  static const String medicalTemplateList =
      '/app/workflow/medical-template/list';
  static const String medicalTemplateListMedicalTemplateDetailGet =
      '/app/workflow/medical-template/detail/{id}';

  static const String serviceAndProductServicesGet =
      '/app/workflow/service/customer/{customerId}';
  static const String serviceAndProductProductsGet =
      '/app/workflow/product/customer/{customerId}';

  static const String medicalProductCreation = '/app/workflow/product';
  static const String medicalProductCreationProducts = '/app/workflow/product';
  static const String medicalServiceAndProductActions = '/app/workflow/action';
  static const String medicalServiceCreation = '/app/workflow/service';
  static const String medicalServiceCreationServices = '/app/workflow/service';
  static const String medicalServiceCreationMethods = '/app/workflow/method';

  static const String customerBookingInfo =
      '/app/customer-booking/customer/info';
  static const String customerBookingInfoBookedServicesFetch =
      '/app/customer-booking/booking/service';
  static const String customerBookingInfoUsedServiceFetch =
      '/app/customer-booking/customer/used-service';
  static const String customerBookingInfoSuggestServicesFetch =
      '/app/customer-booking/customer/suggest-service';

  static const String orderFood = '/app/booking-meal';
  static const String homeCheckLeader =
      '/app/assign-working/employee/check-leader';

  // acc endpoints
  static const String listInventory = '/app/inventory/asset/list';
  static const String getAssetInventoryByID = '/app/inventory/asset/detail';
  static const String saveAssetInventory = '/app/inventory/asset';
  static const String createInventoryTicket = '/app/inventory/asset';
  static const String getInventoryTickedDepartment =
      '/app/inventory/department';
  static const String findEmployeeByID = '/app/inventory/employee';
  static const String getListOrganization = '/app/inventory/organization';
  static const String loginAcc = '/app/inventory/login';
  static const String functionID = '9220';
  static const String getTransferHistory =
      '/app/inventory/asset/transfer-history';
  static const String getMaintenanceHistory =
      '/app/inventory/asset/maintenance-history';
  static const String getFeedbackHistory = '/app/inventory/feedback';

  static const String staff = '/staff';
  static const String staffSearch = '/app/conversation/search-screen';
  static const String staffDetail = '/staff/detail';

  static const String eventEvents = '/events/all';
  static const String eventActions = '/events/actions';
  static const String tickets = '/tickets';
  static const String eventsCheckIn = '/events/checkin';
  static const String checkOut = '/events/checkout';
  static const String rewards = '/events/rewards';
  static const String unmap = '/events/unmap_voucher';
  static const String history = '/events/tickets/history';
  static const String approval = '/approval';
  static const String approval2 = '/approval2';
  static const String jobScheduler = '/app/tasks/aggregate/query';
  static const String submitFeedBack = '/app/tasks/{taskId}/comment';
  static const String submitJobScheduler = '/app/tasks/{id}/status';
  static const String detailJobScheduler = '/app/tasks/{id}';
  static const String creatingTask = '/app/tasks';
  static const String importantNotes = '/app/workflow/important-note/list';

  static const String request = '/request';
  static const String detailEform = '/app/e-form/detail/{id}';
  static const String generalTask = '/app/tasks/aggregate/count';
  static const String eform = '/app/e-form';
  static const String eformRequestType = '/app/e-form/request-type';
  static const String homeCheckIn = '/home/<USER>';
  static const String homeCheckout = '/home/<USER>';
  static const String timekeeping = '/timekeeping';
  static const String salaryDetail = '/salary/detail';
  static const String checkinType = '/app/checkin/type';
  static const String sendUpdateWorkRequest = '/update_work';
  static const String getRequests = '/requests';
  static const String getDaysOff = '/days_off';
  static const String sendRequestDaysOff = '/request/days_off';
  static const String sendRequestBusiness = '/request/business';
  static const String getMember = '/app/department/employees';
  static const String checkPhoneNumber = '/app/user/login-before';
  static const String setPassword = '/app/user/change-password';
  static const String resetPassword = '/app/user/reset-password';
  static const String userCheckinPermissionCheck = '/app/checkin/permission';
  static const String checkin = '/app/checkin';
  static const String historyCheckin = '/app/checkin?month=';
  static const String monthlyHistoryCheckin = '/app/checkin/general';
  static const String profile = '/app/user/profile';
  static const String updateProfile = '/app/user/update-avatar';
  static const String rejectRequest = '/app/e-form/reject/{requestId}';
  static const String approvingRequest = '/app/e-form/approve/{requestId}';
  static const String createEform = '/app/e-form/create';
  static const String department = '/app/department';
  static const String repeatTask = '/app/tasks/aggregate/period';
  static const String functionRoom = '/functionRoom';
  static const String getCustomer = '/getCustomer/{customerQrCode}';
  static const String noteDetails = '/app/workflow/important-note';
  static const String getCustomerInfo = '/customerInfo/{id}';
  static const String getConversation = '/app/conversation/detail';
  static const String getChatList = '/app/conversation';
  static const String createGroupChat = '/app/conversation';
  static const String checkEmployee = '/app/user/check-employee/{phone}';
  static const String user = '/app/user';
  static const String customerProfile =
      '/app/workflow/customer/info/{customer_id}';
  static const String customerInfoDetails =
      '/app/workflow/customer/detail/{customer_id}/{branch_id}/{check_in_id}';
  static const String customerCheckout = '/app/workflow/customer/checkout';
  static const String listCustomer = '/app/workflow/customer/list';
  static const String listCustomerGetStatus = '/app/workflow/customer/status';
  static const String listCustomerSearch = '/app/workflow/customer/search';
  static const String serviceAndProduct =
      '/app/workflow/service-and-product/list';
  static const String serviceAndProductGetCategory = '/app/workflow/data-type';
  static const String medicalLogDetailGetPostSai =
      '/app/workflow/catalog/skin-post-sai/list';
  static const String scheduleDetails =
      '/app/workflow/customer/booking/{customer_id}';
  static const String customerSchedule = '/app/customer-booking/booking';
  static const String assignTask = '/app/assign-working/working';
  static const String assignTaskGetStaff = '/app/assign-working/employee/team';
  static const String customerCheckin =
      '/app/customer-booking/customer/check-in';
  static const String chatSelectBranch = '/app/branch';
  static const String branchChatList = '/app/chat/conversation/customer-care';
  static const String pxList = '/app/erp/customer/assign-work/count';
  static const String pxTaskList = '/app/erp/work/assign/employee';
  static const String takingCareCustomer =
      '/app/erp/work/assign/employee/detail';
  static const String pxUnasigned =
      '/app/acc/services-assign/get-list-service-assign';
  static const String consultationManagerGetCustomer = '/app/erp/customer/room';
  static const String pxUnasignedUpdate = '/app/erp/customer/service';
  static const String pxUnasignedUpdateWorksFetch = '/app/erp/work/services';
  static const String pxUnasignedUpdateEmployeesFetch =
      '/app/erp/employee/room';
  static const String consultationManagerUpdateAssign = '/app/erp/work/assign';
  static const String pxUnasignedUpdateAssign =
      '/app/acc/services-assign/create-service-assign';
  static const String pxRecheck = '/px-recheck';
  static const String pxRecheckAssignsFetch =
      '/app/acc/services-assign/get-service-assign/{assignId}';
  static const String takingCareCustomerFinishTask = '/app/erp/work/assign';
  static const String takingCareCustomerUploadImages = '/app/erp/work/images';
  static const String customerGetRoomList = '/app/erp/service/room';
  static const String pxRecheckNoteFinish =
      '/app/acc/services-assign/finish-service-assign';
  static const String customerPrint = '/app/erp/customer/check-in/print';
  static const String takingCareCustomerRemoveImage = '/app/erp/work/images';
  static const String homeWorkLeaderCheck = '/app/erp/employee/check-leader';
  static const String uploadRecord = '/file/record/upload/';
  static const String createCustomer = '/app/erp/customer/profile';
  static const String createCustomerGetProvince =
      '/app/configurations/province';
  static const String createCustomerGetDistrict =
      '/app/configurations/district';
  static const String createCustomerGetWard = '/app/configurations/ward';
  static const String createCustomerGetJob = '/app/configurations/job';
  static const String notificationReadAll = '/app/notification/read/all';
  static const String takingCareCustomerGetSection =
      '/app/acc/services/get-services-info';
  static const String takingCareCustomerCreateTreatmentDetails =
      '/app/erp/work/treatment/detail';
  static const String selectPxRoom = '/select-px-room';
  static const String selectPxRoomRoomChange = '/app/erp/customer/room';
  static const String customerList = '/app/erp/customer';
  static const String createCustomerUpdate = '/app/erp/customer/profile/{id}';

  static const String consultationManager = '/consultation-manager';
  static const String consultationManagerRoomFetch = '/app/erp/advisory/room';
  static const String consultationManagerBedFetch = '/app/erp/advisory/bed';
  static const String consultationManagerBedAssign =
      '/app/erp/advisory/bed/change';
  static const String consultationCustomer = '/app/erp/advisory/screen';
  static const String consultationCustomerGetService =
      '/app/erp/advisory/service';
  static const String consultationCustomerGetAction =
      '/app/erp/advisory/action';
  static const String consultationCustomerComplete =
      '/app/erp/advisory/complete';
  static const String consultationManagerListFetchByStaff =
      '/app/erp/work/assign/employee';
  static const String takingCareCustomerCreateSupport =
      '/app/erp/advisory/support';
  static const String takingCareCustomerCheckEmployeeInRoom =
      '/app/erp/employee/check-staff';

  static const String staffEvaluationPeriods = '/app/acc/hr360/period';
  static const String detailStaffEvaluationPeriod = '/app/acc/hr360/result';
  static const String detailStaffEvaluationPeriodEmployeeFetch =
      '/app/acc/hr360/employee';
  static const String ratingHumanQuestion = '/app/acc/hr360/question';
  static const String ratingDetailQuestion = '/app/acc/hr360/answer';
  static const String saveQuestion = '/app/acc/hr360/appraisal';

  static const String detailCrmCustomer = '/app/crm/customer/info';
  static const String detailCrmCustomerAdviceFetch =
      '/app/crm/customer/consultant';
  static const String detailCrmCustomerServiceFetch =
      '/app/crm/customer/service';
  static const String detailCrmCustomerCallLogFetch =
      '/app/crm/customer/call-log';
  static const String detailCrmCustomerMessageLogFetch =
      '/app/crm/customer/message';
  static const String detailCrmCustomerBookingLogFetch =
      '/app/crm/customer/booking';
  static const String detailCrmCustomerAdviceTypeFetch =
      '/advice-type-fetch-detail-crm-customer';
  static const String detailCrmCustomerAdviceUpdate =
      '/app/crm/customer/consultant';
  static const String detailCrmCustomerDetailServiceFetch =
      '/detail-service-fetch-detail-crm-customer';
  static const String userTicket = '/app/crm/user/ticket';
  static const String userInfo = '/app/crm/user/info';
  static const String listStatus = '/app/crm/work-status';
  static const String detailCrmCustomerBranchLoad = '/app/crm/branch';
  static const String detailCrmCustomerRoomLoad = '/app/crm/department';
  static const String detailCrmCustomerTimeLoad = '/app/crm/booking-time';
  static const String detailCrmCustomerServiceLoad = '/app/crm/service';
  static const String detailCrmCustomerPromotionLoad = '/app/crm/promotion';
  static const String detailCrmCustomerBookingDetailLoad =
      '/app/crm/customer/booking/detail';
  static const String detailCrmCustomerNumberBookingLoad =
      '/app/crm/number-booking';
  static const String detailCrmCustomerBook = '/app/crm/customer/booking';
  static const String detailCrmCustomerBookingLoad =
      '/app/crm/customer/booking/info';
  static const String userStringeeTokenFetch = '/app/mobile-call/token';
  static const String hrOrganization = '/app/acc/hr360/org';
  static const String consultationCustomerProductLoad =
      '/app/erp/advisory/product';
  static const String takingCareCustomerBotTypeLoad =
      '/app/erp/config/bot-type';
  static const String customerBookingInfoServiceDetailsLoad =
      '/app/erp/treatment/list';
  static const String serviceDetailEmployeeFetch =
      '/app/department/employees/search';
  static const String serviceDetailDoctorFetch = '/app/erp/employee/doctor';
  static const String createCustomerSurveyLoad =
      '/app/erp/customer/visit-reason';
  static const String createCustomerCustomerSearch =
      '/app/erp/customer/introducer';
  static const String consultationCreateTreatmentDetails = '/app/erp/treatment';
  static const String consultationCreateTreatmentDetailsV2 =
      '/app/acc/services/create-service-usage';
  static const String consultationUpdateTreatmentDetailsV2 =
      '/app/acc/services/update-service-usage/{id}';
  static const String takingCareCustomerNotiBotTypePost =
      '/app/erp/treatment/notify';
  static const String uploadCheckInImage = '/theadvance/check-in/upload';
  static const String uploadUserAvatar = '/theadvance/avatar/upload';
  static const String customerRecord = '/customer-record';
  static const String checkinPhoto = '/checkin-photo';
  static const String uploadFeedback = '/theadvance/report/upload';
  static const String uploadKYC = '/theadvance/kyc/upload';
  static const String feedback = '/app/report-bug';
  static const String loginSocketAccessTokenGet = '/app/chat/login';
  static const String chatUploadFile = '/chat/upload';
  static const String createChatGroupUserLoad = '/user-load-create-chat-group';
  static const String createChatGroup = '/create-chat-group';
  static const String storyList = '/app/post/newsfeed';
  static const String storyRule = '/story-rule';
  static const String commentList = '/app/comment/post/{postId}';
  static const String postComment = '/app/comment';
  static const String likeList = '/like-list';
  static const String storyPersonList = '/app/post/user/{username}';
  static const String groupChatDetail = '/group-chat-detail';
  static const String groupChatDetailUpdate = '/update-group-chat-detail';
  static const String groupChatDetailMediaLoad =
      '/media-load-group-chat-detail';
  static const String groupChatDetailFileLoad = '/file-load-group-chat-detail';
  static const String groupChatDetailLinkLoad = '/link-load-group-chat-detail';
  static const String groupChatDetailAvatarUpload = '/theadvance/avatar/upload';
  static const String userList = '/user-list';
  static const String chatConversationDetailsUpdate =
      '/conversation-details-update-chat';
  static const String chatReact = '/react-chat';
  static const String uploadFileSocial = '/social/upload';
  static const String createStory = '/app/post';
  static const String reaction = '$createStory/{id}/reaction';
  static const String updateOrDeleteStory = '$createStory/{id}';
  static const String commentReaction = '$postComment/{id}/reaction';
  static const String updateOrDeleteComment = '$postComment/{id}';
  static const String chatListSearch = '/search-chat-list';
  static const String locationGoogle = '/maps/api/place/findplacefromtext/json';
  static const String addressLocationGoogle = '/maps/api/geocode/json';
  static const String nearBySearch = '/maps/api/place/nearbysearch/json';
  static const String autoComplete = '/maps/api/place/autocomplete/json';
  static const String chatMessageRemove = '/message-remove-chat';
  static const String tagList = '/app/user';
  static const String chatMessageEdit = '/message-edit-chat';
  static const String chatListSearchMessage = '/search-message-chat-list';
  static const String storyPersonUser = '/app/user/detail/{username}';
  static const String chatListPinConversation = '/pin-conversation-chat-list';
  static const String chatListGetTotalUnread = '/get-total-unread-chat-list';
  static const String emojiList = '/app/post/emoji';
  static const String chatGetConversation = '/get-conversation-chat';
  static const String historyCheckinFetch = '/app/check-in';
  static const String storyDetail = '$createStory/{id}/detail';
  static const String notificationList = '/app/notification';
  static const String checkNotification = '$notificationList/unread-count';
  static const String readAllSocial = '$notificationList/read';
  static const String chatListGetConversationByInviteId =
      '/get-conversation-by-invite-id-chat-list';
  static const String chatListJoinGroup = '/join-group-chat-list';
  static const loginSocial = '/app/social/user/login';
  static const deleteNoti = '$notificationList/{id}';
  static const String createChatFolder = '/create-chat-folder';
  static const String createChatFolderLoad = '/load-create-chat-folder';
  static const String createChatFolderRemoveFolder =
      '/remove-folder-create-chat-folder';
  static const String createChatFolderConversationLoad =
      '/conversation-load-create-chat-folder';
  static const String createChatFolderUpdate = '/update-create-chat-folder';
  static const String chatPinMessage = '/pin-message-chat';
  static const String chatUnpinMessage = '/unpin-message-chat';
  static const String chatGetPinList = '/get-pin-list-chat';
  static const String vote = '/app/post/{id}/vote';
  static const String voteUsers = '/app/post/{id}/vote-users';
  static const String chatVotePoll = '/vote-poll-chat';
  static const String chatUpdatePoll = '/update-poll-chat';
  static const String takingCareCustomerGetTreatmentPhoto =
      '/app/erp/treatment/get-treatment-photo-by-customer-service-id';
  static const String updateServiceDetail =
      '/app/acc/services-assign/update-service-detail';

  static const String pxRecheckWorkStatusUpdate =
      '/app/acc/services-assign/pause-continue-service-detail';
  static const String pxRecheckAssignUpdate =
      '/app/acc/services-assign/update-employee-service-assign';
  static const String settingSendKycPhotos = '/app/kyc-face/identify';
  static const String ticketV2 = '/app/ticket-api/tickets/group-tickets';
  static const String myTicketV2 = '/app/ticket-api/tickets';
  static const String ticketGroupType = '/app/ticket-api/groups/user';
  static const String ticketAllGroup = '/app/ticket-api/groups';
  static const String ticketCreatedType = '/app/ticket-api/ticket-types/user';
  static const String ticketAllType = '/app/ticket-api/ticket-types';
  static const String ticketDetail = '$myTicketV2/{ticketId}';
  static const String ticketActive = '$updateTicketV2/ticket-details';
  static const String ticketReason = '/app/ticket-api/groups/{groupId}/reasons';
  static const String ticketConfirm = '/ticket-confirm';
  static const String uploadTicket = '/theadvance/ticket/upload';
  static const String uploadFoodReport = '/theadvance/food/upload';
  static const String createTicketV2 = '/app/ticket-api/tickets';
  static const String orderFoodRegulations = '/order-food-regulations';
  static const String orderFoodReport = '/app/booking-meal/feedback';
  static const String orderFoodQrCode = '/order-food-qr-code';
  static const String orderFoodQrScan = '/app/qr-code/scan';
  static const String foodSetDefaultAddress =
      '/app/booking-meal/booking-address';
  static const String createTreatmentOM = '/app/erp/add-update-treatment-fat';
  static const String getTreatmentOM =
      '/app/erp/fat-treatment-by-customer-service';
  static const String getTreatNote = '/app/erp/treatment/get-treatment-note';
  static const String updateTreatNote =
      '/app/erp/treatment/update-treatment-note';
  static const String updateFitCustomerInfo =
      '/app/erp/add-update-fit-record-by-customer-id';
  static const String getFitCustomerInfo = '/app/erp/fit-record-by-customer-id';
  static const String getResultListOfFit =
      '/app/erp/fit/get-result-list-of-fit-by-customer-id';
  static const String getResultOfFit = '/app/erp/get-result-of-fit';
  static const String deleteResultOfFit =
      '/app/erp/fit/delete-result-of-fit-by-row-id';
  static const String updateResultOfFit = '/app/erp/add-update-result-of-fit';
  static const String getCustomerService = 'get-customer-service';
  static const String consultationCustomerGetServiceUsage =
      '/app/acc/services/get-service-usage';
  static const String updateTicketV2 = '$createTicketV2/{ticketId}';
  static const String serviceInsideTicket =
      '/app/erp/customer/service-list-of-customer';
  static const String dev = '/dev';
  static const String devMiniApp = '/app/mini-app';
  static const String userCheckPermission = '/app/user/permission';
  static const String chatListGetRecentContacts =
      '/get-recent-contacts-chat-list';
  static const String getSkinInfo = '/app/erp/customer/skin-info';
  static const String updateSkinInfo = '/app/erp/customer/update-skin-info';
  static const String chatListUpdatePinConversation =
      '/update-pin-conversation-chat-list';
  static const String chatGetUserSeen = '/get-user-seen-chat';
  static const String kpiEmployee =
      '/app/acc/services-employee-revenue/get-employee-revenue-in-month';
  static const String kpiEmployeeDetail =
      '/app/acc/services-employee-revenue/get-revenue-detail';
  static const String chatSearch = '/search-chat';
  static const String chatListSortFolder = '/sort-folder-chat-list';
  static const String chatTranscribe = '/transcribe-chat';
  static const String chatReplyBotMessage = '/reply-bot-message-chat';
  static const String chatGetUserSticker = '/user-sticker-set';
  static const String stickerSocial = '/app/sticker';
  static const String stickerAll = '/app/set-stickers';
  static const String createSticker = '/app/sticker/create';
  static const String onlySet = '/app/sets';
  static const String removeSet = '/app/sets/{setId}';
  static const String removeSticker = '$stickerSocial/{setId}/{stickerId}';
  static const String uploadFileAi = 'remove-background';
  static const String stickerRecent = '/app/sticker/recent';
  static const String groupChatDetailMemberInfoLoad =
      '/member-info-load-group-chat-detail';
  static const String groupChatDetailGetRuleByRole =
      '/get-rule-by-role-group-chat-detail';
  static const String groupChatDetailGetUserRules =
      '/get-user-rules-group-chat-detail';
  static const String groupChatDetailUpdateAdminRule =
      '/update-admin-rule-group-chat-detail';
  static const String groupChatDetailUpdateMemberRule =
      '/update-member-rule-group-chat-detail';
  static const String groupChatDetailChangeOwner =
      '/change-owner-group-chat-detail';
  static const String pxDeleteService =
      '/app/acc/services-assign/delete-service-assign';
  static const String pxDeleteCustomer =
      '/app/acc/services-assign/remove-customer-from-room';
  static const String consultationCustomerEditService =
      '/app/erp/advisory/edit-customer-service';
  static const String consultationCustomerRemoveService =
      '/app/erp/advisory/delete-customer-deal';
  static const String groupChatDetailGetUserException =
      '/get-user-exception-group-chat-detail';
  static const String getConsultationTTBD = 'get-consultation-ttbd';
  static const String getConsultationNDTV =
      '/app/acc/partner-detail/{topicId}/{partnerId}';
  static const String customerRelationShip =
      '/app/erp/customer/relationship-of-customer';
  static const String productConfirm =
      '/app/acc/purchase-contract/get-list-purchase-contract';
  static const String productDetailConfirm =
      '/app/acc/purchase-contract/get-purchase-contract-detail/{rowKey}';
  static const String productConfirmBranch =
      '/app/acc/purchase-contract/get-list-branch';
  static const String productDetailConfirmReject =
      '/app/acc/purchase-contract/reject-purchase-contract';
  static const String productDetailConfirmApproval =
      '/app/acc/purchase-contract/approve-purchase-contract';
  static const String getComboTag = '/app/acc/image-tag/get-combo-tag';
  static const String getTagImageByComboTag =
      '/app/acc/image-tag/get-image-by-tag';
  static const String addTagsImage = '/add-tags-image';
  static const String addTagsImageGetImageList =
      '/app/acc/image-tag/get-image-by-customer';
  static const String addTagsImageGetRoomList = '/get-room-list-add-tags-image';
  static const String addTagsImageUserSearch = '/user-search-add-tags-image';
  static const String addTagsImageGetTagList =
      '/app/acc/image-tag/get-combo-tag';
  static const String addTagsImageCreateImageTag =
      '/app/acc/image-tag/create-image-tag';
  static const String addTagsImageCreateMergeImage =
      '/app/acc/image-tag/merge-image';
  static const String deleteImageByComboTag = '/app/acc/image-tag/delete-image';
  static const String deleteTagByComboTag = '/app/acc/image-tag/delete-tag';
  static const String getServiceTypePurchaseDetail =
      '/app/acc/item-bom-warehouse-event/get-history-details';
  static const String getComboService = '/app/acc/services/get-combo-service';
  static const String getDealService = '/get-deal-service';
  static const String createCombo = '/app/acc/item-bom-warehouse-event/create';
  static const String updateCombo =
      '/app/acc/item-bom-warehouse-event/update/{rowKey}';
  static const String getAllService = '/app/acc/services/getallservices';
}

class StaticEndPoints {
  static const String uploadProductKey = 'COLLABORATOR_APP';
  static const String uploadToken = '5QTj8mOMVdgwZ2cctdShW1';
  static const String baseUrl = 'https://upload.ngocdunggroup.com.vn';
  static const String mediaUploadBaseUrl = 'https://media.theadvance.com';

  static const String uploadImages = '/upload/images';
  static const String uploadFiles = '/upload/files';
  static const String uploadAudio = '/upload/audios';
}

class EventEndPoints {
  EventEndPoints._();

  static const String configurations = '/app/event/config';
  static const String login = '/app/event/send-otp';
  static const String confirmOTP = '/app/event/verify-otp';
  static const String events = '/app/event/list';
  static const String eventActions = '/app/event/actions';
  static const String tickets = '/app/event/tickets';

  static const String checkIn = '/app/event/checkin';
  static const String checkOut = '/app/event/checkout';
  static const String rewards = '/app/event/reward';
  static const String unmap = '/app/event/unmap';
  static const String history = '/app/event/tickets/history';
}
