// Package imports:
import 'package:path_to_regexp/path_to_regexp.dart';

// Project imports:
import '../end_points.dart';

final Map<String, String> mapMockApiForGetRequest = <String, String>{
  EndPoints.homeMenu: 'acc_home_menu',
  EndPoints.getCustomer: 'customer',
  EndPoints.branch: 'branch',
  EndPoints.profile: 'profile',
  EndPoints.branchSelection: 'branch_selection',
  EndPoints.provinceGet: 'get_province',
  EndPoints.branchSelectionGetFloor: 'get_floor',
  EndPoints.getRoom: 'get_room_list',
  EndPoints.getBed: 'get_bed_list',
  EndPoints.customerProfileGetConsultationHistory:
      'customer_consultation_history_list',
  EndPoints.medicalDepartmentList: 'medical_department_list',
  EndPoints.medicalServiceLogList: 'medical_service_log_list',
  EndPoints.medicineDetailGetUnit: 'get_unit_medicine_detail',
  EndPoints.importantNotes: 'important_notes',
  EndPoints.medicalServiceList: 'medical_service_list',
  EndPoints.branchSelectionEmployeeGet: 'branch_selection_employee_get',
  EndPoints.branchSelectionEstimateTimeGet:
      'branch_selection_estimate_time_get',
  EndPoints.listCustomer: 'list_customer',
  EndPoints.medicalLogDetailMedicineListGet: 'medicine_list_get',
  EndPoints.medicalLogDetailDosageListGet: 'dosage_list_get',
  EndPoints.medicalLogDetailHaPointListGet: 'ha_point_list_get',
  EndPoints.medicalLogDetailKhacnhoListGet: 'khacnho_list_get',
  EndPoints.medicalLogDetailDoctorListGet: 'doctor_list_get',
  EndPoints.medicalLogDetailGetSkinMachine:
      'get_skin_machine_medical_log_detail',
  EndPoints.medicalLogDetailGetPostSai: 'get_post_sai_medical_log_detail',
  EndPoints.medicalLogDetailGetTattooTime:
      'get-tattoo-time-medical-log-detailmedical-log-detail',
  EndPoints.medicalLogDetailGetOriginStatus:
      'get-origin-status-medical-log-detailmedical-log-detail',
  EndPoints.medicalLogDetailGetTattooColor:
      'get-tattoo-color-medical-log-detailmedical-log-detail',
  EndPoints.medicalTemplateList: 'medical_template_list',
  EndPoints.medicalTemplateListMedicalTemplateDetailGet:
      'medical_template_detail',
  EndPoints.medicalProductCreationProducts: 'medical_products',
  EndPoints.medicalServiceAndProductActions: 'medical_product_actions',
  EndPoints.medicalServiceCreationServices:
      'medical_service_create_service_list',
  EndPoints.medicalServiceCreationMethods: 'medical_service_create_method_list',
  EndPoints.serviceAndProductServicesGet: 'services_get_service_and_product',
  EndPoints.serviceAndProductProductsGet: 'products_get_service_and_product',
  EndPoints.scheduleDetails: 'schedule_details',
  EndPoints.orderFood: 'order_food',
  EndPoints.customerSchedule: 'customer_schedule',
  EndPoints.customerBookingInfo: 'customer_booking_info',
  EndPoints.customerBookingInfoBookedServicesFetch:
      'customer_booking_info_booked_services_fetch',
  EndPoints.customerBookingInfoUsedServiceFetch:
      'customer_booking_info_used_service_fetch',
  EndPoints.customerBookingInfoSuggestServicesFetch:
      'customer_booking_info_suggest_services_fetch',
  EndPoints.assignTask: 'assign_task',
  EndPoints.assignTaskGetStaff: 'assign_task_get_staff',
  EndPoints.homeCheckLeader: 'home_check_leader',
  EndPoints.chatSelectBranch: 'chat_select_branch',
  EndPoints.branchChatList: 'branch_chat_list',
  EndPoints.pxList: 'px_list',
  EndPoints.pxUnasigned: 'px_unasigned',
  EndPoints.pxTaskList: 'px_task_list',
  EndPoints.pxUnasignedUpdate: 'px_unasigned_update',
  EndPoints.pxUnasignedUpdateWorksFetch: 'px_unasigned_update_works_fetch',
  EndPoints.pxUnasignedUpdateEmployeesFetch:
      'px_unasigned_update_employees_fetch',
  EndPoints.takingCareCustomer: 'taking_care_customer',
  EndPoints.pxRecheck: 'px_recheck',
  EndPoints.pxRecheckAssignsFetch: 'px_recheck_assigns_fetch',
  EndPoints.customerGetRoomList: 'customer_get_room_list',
  EndPoints.homeWorkLeaderCheck: 'home_work_leader_check',
  EndPoints.createCustomerGetProvince: 'create_customer_get_province',
  EndPoints.createCustomerGetDistrict: 'create_customer_get_district',
  EndPoints.createCustomerGetWard: 'create_customer_get_ward',
  EndPoints.createCustomerGetJob: 'create_customer_get_job',
  EndPoints.takingCareCustomerGetSection: 'taking_care_customer_get_section',
  EndPoints.selectPxRoom: 'select_px_room',
  EndPoints.customerList: 'customer_list',
  EndPoints.consultationManager: 'consultation_manager',
  EndPoints.consultationManagerRoomFetch: 'consultation_manager_room_fetch',
  EndPoints.consultationManagerBedFetch: 'consultation_manager_bed_fetch',
  EndPoints.consultationCustomer: 'consultation_customer',
  EndPoints.consultationCustomerGetService: 'consultation_customer_get_service',
  EndPoints.consultationCustomerGetAction: 'consultation_customer_get_action',
  EndPoints.takingCareCustomerCheckEmployeeInRoom:
      'taking_care_customer_check_employee_in_room',
  EndPoints.staffEvaluationPeriods: 'staff_evaluation_periods',
  EndPoints.detailStaffEvaluationPeriod: 'detail_staff_evaluation_period',
  EndPoints.ratingHumanQuestion: 'rating_human_question',
  EndPoints.ratingDetailQuestion: 'rating_human_answer',
  EndPoints.detailCrmCustomer: 'detail_crm_customer',
  EndPoints.detailCrmCustomerAdviceFetch: 'detail_crm_customer_advice_fetch',
  EndPoints.detailCrmCustomerServiceFetch: 'detail_crm_customer_service_fetch',
  EndPoints.detailCrmCustomerCallLogFetch: 'detail_crm_customer_call_log_fetch',
  EndPoints.detailCrmCustomerMessageLogFetch:
      'detail_crm_customer_message_log_fetch',
  EndPoints.detailCrmCustomerBookingLogFetch:
      'detail_crm_customer_booking_log_fetch',
  EndPoints.detailCrmCustomerAdviceTypeFetch:
      'detail_crm_customer_advice_type_fetch',
  EndPoints.detailCrmCustomerDetailServiceFetch:
      'detail_crm_customer_detail_service_fetch',
  EndPoints.detailStaffEvaluationPeriodEmployeeFetch:
      'detail_staff_evaluation_period_employee_fetch',
  EndPoints.userTicket: 'user_ticket',
  EndPoints.userInfo: 'user_info',
  EndPoints.listStatus: 'status_new_tab',
  EndPoints.userCheckinPermissionCheck: 'user_checkin_permission_check',
  EndPoints.detailCrmCustomerBranchLoad: 'detail_crm_customer_branch_load',
  EndPoints.detailCrmCustomerRoomLoad: 'detail_crm_customer_room_load',
  EndPoints.detailCrmCustomerTimeLoad: 'detail_crm_customer_time_load',
  EndPoints.detailCrmCustomerServiceLoad: 'detail_crm_customer_service_load',
  EndPoints.detailCrmCustomerPromotionLoad:
      'detail_crm_customer_promotion_load',
  EndPoints.detailCrmCustomerBookingDetailLoad:
      'detail_crm_customer_booking_detail_load',
  EndPoints.detailCrmCustomerNumberBookingLoad:
      'detail_crm_customer_number_booking_load',
  EndPoints.detailCrmCustomerBookingLoad: 'detail_crm_customer_booking_load',
  EndPoints.userStringeeTokenFetch: 'user_stringee_token_fetch',
  EndPoints.takingCareCustomerBotTypeLoad: 'taking_care_customer_bot_type_load',
  EndPoints.customerBookingInfoServiceDetailsLoad:
      'customer_booking_info_service_details_load',
  EndPoints.serviceDetailEmployeeFetch: 'service_detail_employee_fetch',
  EndPoints.serviceDetailDoctorFetch: 'service_detail_doctor_fetch',
  EndPoints.createCustomerSurveyLoad: 'create_customer_survey_load',
  EndPoints.createCustomerCustomerSearch: 'create_customer_customer_search',
  EndPoints.customerRecord: 'customer_record',
  EndPoints.checkinPhoto: 'checkin_photo',
  EndPoints.feedback: 'feedback',
  EndPoints.createChatGroupUserLoad: 'create_chat_group_user_load',
  EndPoints.storyList: 'story_list',
  EndPoints.storyRule: 'story_rule',
  EndPoints.commentList: 'comment_list',
  EndPoints.likeList: 'like_list',
  EndPoints.storyPersonList: 'story_person_list',
  EndPoints.userList: 'user_list',
  EndPoints.chatListSearch: 'chat_list_search',
  EndPoints.locationGoogle: 'location_google',
  EndPoints.nearBySearch: 'address_near_location_google',
  EndPoints.tagList: 'tag_list',
  EndPoints.chatListSearchMessage: 'chat_list_search_message',
  EndPoints.chatListGetTotalUnread: 'chat_list_get_total_unread',
  EndPoints.chatGetConversation: 'chat_get_conversation',
  EndPoints.historyCheckinFetch: 'history_checkin_fetch',
  EndPoints.storyDetail: 'story_detail',
  EndPoints.notificationList: 'notification_list',
  EndPoints.createChatFolder: 'create_chat_folder',
  EndPoints.createChatFolderLoad: 'create_chat_folder_load',
  EndPoints.createChatFolderConversationLoad:
      'create_chat_folder_conversation_load',
  EndPoints.chatGetPinList: 'chat_get_pin_list',
  EndPoints.configurations: 'app_configurations',
  EndPoints.loginSocial: 'loginsocialuserlogin',
  EndPoints.takingCareCustomerGetTreatmentPhoto:
      'taking_care_customer_get_treatment_photo',
  EndPoints.ticketGroupType: 'ticket_group_type',
  EndPoints.ticketCreatedType: 'ticket_created_type',
  EndPoints.ticketReason: 'ticket_reason',
  //EndPoints.ticketDetail: 'ticket_detail',
  EndPoints.orderFoodRegulations: 'order_food_regulations',
  EndPoints.orderFoodQrCode: 'order_food_qr_code',
  EndPoints.foodSetDefaultAddress: 'food_set_default_address',
  EndPoints.getTreatmentOM: 'customer_booking_info_service_om_details',
  EndPoints.consultationCustomerGetServiceUsage:
      'consultation_customer_get_service_usage',
  EndPoints.dev: 'dev',
  EndPoints.devMiniApp: 'dev_mini_app',
  EndPoints.userCheckPermission: 'user_check_permission',
  EndPoints.chatListGetRecentContacts: 'chat_list_get_recent_contacts',
  EndPoints.chatGetUserSeen: 'chat_get_user_seen',
  EndPoints.kpiEmployee: 'kpi_employee',
  EndPoints.kpiEmployeeDetail: 'kpi_employee_detail',
  EndPoints.chatSearch: 'chat_search',
  EndPoints.chatGetUserSticker: 'chat_get_user_sticker',
  EndPoints.groupChatDetailMemberInfoLoad: 'group_chat_detail_member_info_load',
  EndPoints.groupChatDetailGetRuleByRole: 'group_chat_detail_get_rule_by_role',
  EndPoints.groupChatDetailGetUserRules: 'group_chat_detail_get_user_rules',
  EndPoints.groupChatDetailGetUserException:
      'group_chat_detail_get_user_exception',
  EndPoints.productConfirm: 'product_confirm_id',
  EndPoints.productDetailConfirm: 'product_detail_confirm_id',
  EndPoints.getComboTag: 'combo_tag',
  EndPoints.getTagImageByComboTag: 'tag_image_get_by_combo_tag',
  EndPoints.addTagsImageGetImageList: 'add_tags_image_get_image_list',
  EndPoints.addTagsImageGetRoomList: 'add_tags_image_get_room_list',
  EndPoints.addTagsImageUserSearch: 'add_tags_image_user_search',
};

final Map<String, String> mapMockApiForPostRequest = <String, String>{
  EndPoints.selectBed: 'success_response',
  EndPoints.medicalProductCreation: 'medical-product-create',
  EndPoints.assignTask: 'assign_task_create',
  EndPoints.customerCheckin: 'customer_checkin',
  EndPoints.pxUnasignedUpdateAssign: 'px_unasigned_update_assign',
  EndPoints.takingCareCustomerUploadImages:
      'taking_care_customer_upload_images',
  EndPoints.pxRecheckNoteFinish: 'px_recheck_note_finish',
  EndPoints.customerPrint: 'customer_print',
  EndPoints.createCustomer: 'create_customer',
  EndPoints.notificationReadAll: 'notification_read_all',
  EndPoints.takingCareCustomerCreateTreatmentDetails:
      'taking_care_customer_create_treatment_details',
  EndPoints.selectPxRoomRoomChange: 'select_px_room_room_change',
  EndPoints.createCustomerUpdate: 'create_customer_update',
  EndPoints.consultationManagerBedAssign: 'consultation_manager_bed_assign',
  EndPoints.consultationCustomerComplete: 'consultation_customer_complete',
  EndPoints.takingCareCustomerCreateSupport:
      'taking_care_customer_create_support',
  EndPoints.detailCrmCustomerAdviceUpdate: 'detail_crm_customer_advice_update',
  EndPoints.checkin: 'app_checkin',
  EndPoints.detailCrmCustomerBook: 'detail_crm_customer_book',
  EndPoints.hrOrganization: 'hr_organization',
  EndPoints.consultationCustomerProductLoad:
      'consultation_customer_product_load',
  EndPoints.takingCareCustomerNotiBotTypePost:
      'taking_care_customer_noti_bot_type_load',
  EndPoints.loginSocketAccessTokenGet: 'login_socket_access_token_get',
  EndPoints.chatUploadFile: 'chat_upload_file',
  EndPoints.groupChatDetailUpdate: 'group_chat_detail_update',
  EndPoints.groupChatDetailMediaLoad: 'group_chat_detail_media_load',
  EndPoints.groupChatDetailFileLoad: 'group_chat_detail_file_load',
  EndPoints.groupChatDetailLinkLoad: 'group_chat_detail_link_load',
  EndPoints.groupChatDetailAvatarUpload: 'group_chat_detail_avatar_upload',
  EndPoints.chatConversationDetailsUpdate: 'chat_conversation_details_update',
  EndPoints.chatReact: 'chat_react',
  EndPoints.chatMessageRemove: 'chat_message_remove',
  EndPoints.chatMessageEdit: 'chat_message_edit',
  EndPoints.chatListPinConversation: 'chat_list_pin_conversation',
  EndPoints.chatListGetConversationByInviteId:
      'chat_list_get_conversation_by_invite_id',
  EndPoints.chatListJoinGroup: 'chat_list_join_group',
  EndPoints.createChatFolderRemoveFolder: 'create_chat_folder_remove_folder',
  EndPoints.createChatFolderUpdate: 'create_chat_folder_update',
  EndPoints.chatPinMessage: 'chat_pin_message',
  EndPoints.chatUnpinMessage: 'chat_unpin_message',
  EndPoints.checkPhoneNumber: 'check_phone_number',
  EndPoints.login: 'login',
  EndPoints.chatVotePoll: 'chat_vote_poll',
  EndPoints.chatUpdatePoll: 'chat_update_poll',
  EndPoints.pxRecheckWorkStatusUpdate: 'px_recheck_work_status_update',
  EndPoints.settingSendKycPhotos: 'setting_send_kyc_photos',
  EndPoints.orderFood: 'order_food_post',
  EndPoints.createTicketV2: 'create_ticket_v2',
  EndPoints.updateTicketV2: 'ticket_detail_update',
  EndPoints.orderFoodQrScan: 'order_food_qr_scan',
  EndPoints.chatListUpdatePinConversation: 'chat_list_update_pin_conversation',
  EndPoints.chatListSortFolder: 'chat_list_sort_folder',
  EndPoints.chatTranscribe: 'chat_transcribe',
  EndPoints.chatReplyBotMessage: 'chat_reply_bot_message',
  EndPoints.groupChatDetailUpdateAdminRule:
      'group_chat_detail_update_admin_rule',
  EndPoints.groupChatDetailUpdateMemberRule:
      'group_chat_detail_update_member_rule',
  EndPoints.groupChatDetailChangeOwner: 'group_chat_detail_change_owner',
  EndPoints.consultationCustomerEditService:
      'consultation_customer_edit_service',
  EndPoints.consultationCustomerRemoveService:
      'consultation_customer_remove_service',
  EndPoints.getConsultationTTBD: 'get_consultation_ndtv',
  EndPoints.getConsultationNDTV: 'get_consultation_ndtv',
  EndPoints.customerRelationShip: 'customer_relationship',
  EndPoints.addTagsImage: 'add_tags_image',
  EndPoints.addTagsImageGetTagList: 'add_tags_image_get_tag_list',
  EndPoints.addTagsImageCreateImageTag: 'add_tags_image_create_image_tag',
  EndPoints.addTagsImageCreateMergeImage: 'add_tags_image_create_merge_image',
  EndPoints.getServiceTypePurchaseDetail: 'get_service_type_purchase',
  EndPoints.getDealService: 'get_deal_service',
};

String? getJsonNameForGetRequest(
  final String endpoint, {
  final Map<String, dynamic>? queryParameters,
}) {
  if (endpoint.contains('/news') && queryParameters != null) {
    return 'news_services';
  }

  if (endpoint.contains('/branch/provinces') &&
      queryParameters?.containsKey('version') == true) {
    return 'province_v2';
  }

  for (final String key in mapMockApiForGetRequest.keys) {
    if (hasMatch(endpoint, key)) {
      return mapMockApiForGetRequest[key];
    }
  }

  return endpoint.replaceAll('/', '').replaceAll('-', '_');
}

String? getJsonNameForPostRequest(
  final String endpoint, {
  final dynamic data,
  final Map<String, dynamic>? queryParameters,
}) {
  for (final String key in mapMockApiForPostRequest.keys) {
    if (hasMatch(endpoint, key)) {
      return mapMockApiForPostRequest[key];
    }
  }

  return endpoint.replaceAll('/', '').replaceAll('-', '_');
}

bool hasMatch(final String path, final String endPoints) {
  final String endPoint = endPoints.replaceAll('{', ':').replaceAll('}', '');
  final RegExp regExp = pathToRegExp(endPoint);

  return regExp.hasMatch(path);
}
