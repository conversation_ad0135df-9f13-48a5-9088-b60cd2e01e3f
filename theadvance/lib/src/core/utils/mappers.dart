// Package imports:
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../data/models/address_location_google_model.dart';
import '../../data/models/address_near_location_google_model.dart';
import '../../data/models/address_search_location_google_model.dart';
import '../../data/models/api_models/checkin_type_model.dart';
import '../../data/models/attachment_model.dart';
import '../../data/models/branch_model.dart';
import '../../data/models/combo_tag_model.dart';
import '../../data/models/comment_list_item_updated_model.dart';
import '../../data/models/comment_list_model.dart';
import '../../data/models/common_part_model.dart';
import '../../data/models/consultation_manager_customer_load_model.dart';
import '../../data/models/created_by_info_model.dart';
import '../../data/models/customer_booking_info_service_om_details_model.dart';
import '../../data/models/customer_relationship_list_model.dart';
import '../../data/models/emoji_list_model.dart';
import '../../data/models/empty_model.dart';
import '../../data/models/fit_customer_info_model.dart';
import '../../data/models/get_consultation_ttbd_model.dart';
import '../../data/models/image_by_combo_tag_model.dart';
import '../../data/models/kpi_employee_model.dart';
import '../../data/models/lat_lng_location_google_model.dart';
import '../../data/models/like_comment_success_model.dart';
import '../../data/models/like_list_model.dart';
import '../../data/models/like_story_success_model.dart';
import '../../data/models/login_social_model.dart';
import '../../data/models/media_upload_model.dart';
import '../../data/models/mention_model.dart';
import '../../data/models/models.dart';
import '../../data/models/order_food_items_model.dart';
import '../../data/models/order_food_model.dart';
import '../../data/models/order_food_qr_code_model.dart';
import '../../data/models/order_food_report_model.dart';
import '../../data/models/poll_info_model.dart';
import '../../data/models/poll_model.dart';
import '../../data/models/product_confirm_branch_model.dart';
import '../../data/models/react_info_model.dart';
import '../../data/models/result_of_fit_model.dart';
import '../../data/models/service_from_acc_model.dart';
import '../../data/models/service_inside_ticket_model.dart';
import '../../data/models/service_type_purchase_detail_model.dart';
import '../../data/models/service_type_purchase_model.dart';
import '../../data/models/skin_customer_info_model.dart';
import '../../data/models/sticker_model.dart';
import '../../data/models/sticker_set_created_success_model.dart';
import '../../data/models/sticker_set_model.dart';
import '../../data/models/sticker_social_model.dart';
import '../../data/models/story_person_list_model.dart';
import '../../data/models/story_rule_model.dart';
import '../../data/models/story_vote_response_model.dart';
import '../../data/models/story_vote_users_model.dart';
import '../../data/models/tags_info_model.dart';
import '../../data/models/ticket_active_model.dart';
import '../../data/models/ticket_created_type_model.dart';
import '../../data/models/ticket_detail_reason_model.dart';
import '../../data/models/ticket_group_type_model.dart';
import '../../data/models/ticket_type_model.dart';
import '../../data/models/ticket_v2_model.dart';
import '../../data/models/total_notification_model.dart';
import '../../data/models/treatment_note_model.dart';
import '../../domain/entities/address_location_google.dart';
import '../../domain/entities/address_near_location_google.dart';
import '../../domain/entities/address_search_location_google.dart';
import '../../domain/entities/branch.dart';
import '../../domain/entities/checkin_type.dart';
import '../../domain/entities/combo_tag.dart';
import '../../domain/entities/comment_list.dart';
import '../../domain/entities/comment_list_item_updated.dart';
import '../../domain/entities/common_part.dart';
import '../../domain/entities/consultation_manager_customer_load.dart';
import '../../domain/entities/consultation_service.dart';
import '../../domain/entities/created_by_info.dart';
import '../../domain/entities/customer_booking_info_service_om_details.dart';
import '../../domain/entities/customer_relationship_list.dart';
import '../../domain/entities/emoji_list.dart';
import '../../domain/entities/empty_entities.dart';
import '../../domain/entities/entities.dart';
import '../../domain/entities/fit_customer_info.dart';
import '../../domain/entities/get_consultation_ttbd.dart';
import '../../domain/entities/image_by_combo_tag.dart';
import '../../domain/entities/kpi_employee.dart';
import '../../domain/entities/lat_lng_google.dart';
import '../../domain/entities/like_comment_success.dart';
import '../../domain/entities/like_list.dart';
import '../../domain/entities/like_story_success.dart';
import '../../domain/entities/login_social.dart';
import '../../domain/entities/media_upload_record.dart';
import '../../domain/entities/mention_entity.dart';
import '../../domain/entities/order_food.dart';
import '../../domain/entities/order_food_items.dart';
import '../../domain/entities/order_food_qr_code.dart';
import '../../domain/entities/order_food_report.dart';
import '../../domain/entities/poll.dart';
import '../../domain/entities/poll_info.dart';
import '../../domain/entities/product_confirm_branch.dart';
import '../../domain/entities/react_info.dart';
import '../../domain/entities/result_of_fit.dart';
import '../../domain/entities/service_from_acc.dart';
import '../../domain/entities/service_inside_ticket.dart';
import '../../domain/entities/service_type_purchase.dart';
import '../../domain/entities/service_type_purchase_detail.dart';
import '../../domain/entities/skin_customer_info.dart';
import '../../domain/entities/social_upload_file.dart';
import '../../domain/entities/sticker.dart';
import '../../domain/entities/sticker_set.dart';
import '../../domain/entities/sticker_set_created_success.dart';
import '../../domain/entities/sticker_social.dart';
import '../../domain/entities/story_list.dart';
import '../../domain/entities/story_person_list.dart';
import '../../domain/entities/story_rule.dart';
import '../../domain/entities/story_vote_response.dart';
import '../../domain/entities/story_vote_users.dart';
import '../../domain/entities/tags_info.dart';
import '../../domain/entities/ticket_active.dart';
import '../../domain/entities/ticket_detail_reason.dart';
import '../../domain/entities/ticket_group_type.dart';
import '../../domain/entities/ticket_type.dart';
import '../../domain/entities/ticket_v2.dart';
import '../../domain/entities/total_notification.dart';
import '../../domain/entities/treatment_note.dart';
import '../params/group_chat_detail_update_admin_rule_request_params.dart';
import '../params/story_write_info_request_params.dart';
import 'mappers.auto_mappr.dart';

@AutoMappr([
  MapType<StaffEvaluationPeriodsModel, StaffEvaluationPeriods>(),
  MapType<StaffEvaluationPeriodModel, StaffEvaluationPeriod>(),
  MapType<DetailStaffEvaluationPeriodModel, DetailStaffEvaluationPeriod>(),
  MapType<
    DetailStaffEvaluationPeriodGeneralDetailsModel,
    DetailStaffEvaluationPeriodGeneralDetails
  >(),
  MapType<
    DetailStaffEvaluationPeriodChartsModel,
    DetailStaffEvaluationPeriodCharts
  >(),
  MapType<
    DetailStaffEvaluationPeriodDetailsModel,
    DetailStaffEvaluationPeriodDetails
  >(),
  MapType<StaffEvaluationGeneralDetailModel, StaffEvaluationGeneralDetail>(),
  MapType<
    DetailStaffEvaluationPeriodChartsItemsModel,
    DetailStaffEvaluationPeriodChartsItems
  >(),
  MapType<
    StaffEvaluationGeneralDetailItemModel,
    StaffEvaluationGeneralDetailItem
  >(),
  MapType<
    DetailStaffEvaluationPeriodEmployeeFetchModel,
    DetailStaffEvaluationPeriodEmployeeFetch
  >(),
  MapType<StaffModel, Staff>(),
  MapType<QuestionListModel, QuestionList>(),
  MapType<QuestionItemModel, QuestionItem>(),
  MapType<QuestionDetailModel, QuestionDetail>(),
  MapType<QuestionDetailItemModel, QuestionDetailItem>(),
  MapType<TicketUserInfoModel, UserInfo>(),
  MapType<NoDataModel, NoData>(),
  MapType<DetailCrmCustomerModel, DetailCrmCustomer>(),
  MapType<DetailCrmCustomerAdviceFetchModel, DetailCrmCustomerAdviceFetch>(),
  MapType<DetailCrmAdviceItemModel, DetailCrmAdviceItem>(),
  MapType<DetailCrmCustomerServiceFetchModel, DetailCrmCustomerServiceFetch>(),
  MapType<DetailCrmServiceItemModel, DetailCrmServiceItem>(),
  MapType<CrmServiceDealModel, CrmServiceHistory>(),
  MapType<DetailCrmCustomerCallLogFetchModel, DetailCrmCustomerCallLogFetch>(),
  MapType<DetailCrmCallLogItemModel, DetailCrmCallLogItem>(),
  MapType<
    DetailCrmCustomerMessageLogFetchModel,
    DetailCrmCustomerMessageLogFetch
  >(),
  MapType<DetailCrmMessageLogItemModel, DetailCrmMessageLogItem>(),
  MapType<
    DetailCrmCustomerBookingLogFetchModel,
    DetailCrmCustomerBookingLogFetch
  >(),
  MapType<DetailCrmBookingLogItemModel, DetailCrmBookingLogItem>(),
  MapType<DetailCrmBookingLogServiceArrModel, DetailCrmBookingLogServiceArr>(),
  MapType<
    DetailCrmCustomerAdviceTypeFetchModel,
    DetailCrmCustomerAdviceTypeFetch
  >(),
  MapType<CrmAdviceTypeModel, CrmAdviceType>(),
  MapType<DetailCrmCustomerAdviceUpdateModel, DetailCrmCustomerAdviceUpdate>(),
  MapType<NoteWriteModel, NoteWriteItem>(),
  MapType<NoteListResponseModel, NoteWriteList>(),
  MapType<StatusUserTicketModel, StatusUserTicket>(),
  MapType<StatusListResponseModel, StatusList>(),
  MapType<DetailCrmCustomerServiceLoadModel, DetailCrmCustomerServiceLoad>(),
  MapType<
    DetailCrmCustomerServiceLoadItemsModel,
    DetailCrmCustomerServiceLoadItems
  >(),
  MapType<DetailCrmCustomerTimeLoadModel, DetailCrmCustomerTimeLoad>(),
  MapType<
    DetailCrmCustomerTimeLoadItemsModel,
    DetailCrmCustomerTimeLoadItems
  >(),
  MapType<DetailCrmCustomerBranchLoadModel, DetailCrmCustomerBranchLoad>(),
  MapType<
    DetailCrmCustomerBranchLoadItemsModel,
    DetailCrmCustomerBranchLoadItems
  >(),
  MapType<DetailCrmCustomerRoomLoadModel, DetailCrmCustomerRoomLoad>(),
  MapType<
    DetailCrmCustomerRoomLoadItemsModel,
    DetailCrmCustomerRoomLoadItems
  >(),
  MapType<
    DetailCrmCustomerPromotionLoadModel,
    DetailCrmCustomerPromotionLoad
  >(),
  MapType<
    DetailCrmCustomerPromotionLoadItemsModel,
    DetailCrmCustomerPromotionLoadItems
  >(),
  MapType<
    DetailCrmCustomerNumberBookingLoadModel,
    DetailCrmCustomerNumberBookingLoad
  >(),
  MapType<
    DetailCrmCustomerBookingDetailLoadModel,
    DetailCrmCustomerBookingDetailLoad
  >(),
  MapType<
    DetailCrmCustomerBookingDetailLoadItemsModel,
    DetailCrmCustomerBookingDetailLoadItems
  >(),
  MapType<DetailCrmCustomerBookModel, DetailCrmCustomerBook>(),
  MapType<DetailCrmCustomerBookingLoadModel, DetailCrmCustomerBookingLoad>(),
  MapType<
    DetailCrmCustomerBookingLoadBookingModel,
    DetailCrmCustomerBookingLoadBooking
  >(),
  MapType<UserCheckinPermissionCheckModel, UserCheckinPermissionCheck>(),
  MapType<StringeeTokenModel, StringeeToken>(),
  MapType<HrOrganizationModel, HrOrganization>(),
  MapType<HrOrganizationItemsModel, HrOrganizationItems>(),
  MapType<
    ConsultationCustomerProductLoadModel,
    ConsultationCustomerProductLoad
  >(),
  MapType<
    ConsultationCustomerProductLoadItemsModel,
    ConsultationCustomerProductLoadItems
  >(),
  MapType<
    ConsultationCustomerProductLoadItemsUnitListModel,
    ConsultationCustomerProductLoadItemsUnitList
  >(),
  MapType<TakingCareCustomerBotTypeLoadModel, TakingCareCustomerBotTypeLoad>(),
  MapType<
    TakingCareCustomerBotTypeLoadItemsModel,
    TakingCareCustomerBotTypeLoadItems
  >(),
  MapType<ConsultationTreatmentDetalModel, ConsultationTreatmentDetal>(),
  MapType<
    ConsultationTreatmentDetalItemModel,
    ConsultationTreatmentDetalItem
  >(),
  MapType<
    CustomerBookingInfoServiceDetailsLoadModel,
    CustomerBookingInfoServiceDetailsLoad
  >(),
  MapType<
    CustomerBookingInfoServiceDetailsLoadItemsModel,
    CustomerBookingInfoServiceDetailsLoadItems
  >(),
  MapType<CreateCustomerSurveyLoadModel, CreateCustomerSurveyLoad>(),
  MapType<CreateCustomerCustomerSearchModel, CreateCustomerCustomerSearch>(),
  MapType<CreateCustomerSurveyLoadItemsModel, CreateCustomerSurveyLoadItems>(),
  MapType<
    CreateCustomerCustomerSearchItemsModel,
    CreateCustomerCustomerSearchItems
  >(),
  MapType<
    CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModel,
    CustomerBookingInfoServiceDetailsLoadItemsTakeEmp
  >(),
  MapType<CustomerBookingInfo, PxCustomer>(
    fields: [Field('customerId', from: 'customerID')],
  ),
  MapType<BookingUsedServiceItem, ConsultationService>(),
  MapType<
    TakingCareCustomerNotiBotTypeLoadModel,
    TakingCareCustomerNotiBotTypeLoad
  >(),
  MapType<MediaUploadModel, MediaUpload>(),
  MapType<
    CustomerBookingInfoServiceDetailsLoadItemsImagesModel,
    CustomerBookingInfoServiceDetailsLoadItemsImages
  >(),
  MapType<ServiceDetailEmployeeFetchModel, ServiceDetailEmployeeFetch>(),
  MapType<
    ServiceDetailEmployeeFetchItemsModel,
    ConsultationServiceDetailEmployee
  >(),
  MapType<
    ServiceDetailDoctorFetchItemsModel,
    ConsultationServiceDetailDoctor
  >(),
  MapType<ServiceDetailDoctorFetchModel, ServiceDetailDoctorFetch>(),
  MapType<CustomerRecordModel, CustomerRecord>(),
  MapType<CheckinPhotoModel, CheckinPhoto>(),
  MapType<FeedbackModel, Feedback>(),
  MapType<LoginSocketAccessTokenGetModel, LoginSocketAccessTokenGet>(),
  MapType<ChatPermissionModel, ChatPermission>(),
  MapType<RecordPermissionModel, RecordPermission>(),
  MapType<LoginSocketAccessTokenGetUserModel, LoginSocketAccessTokenGetUser>(),
  MapType<ChatListModel, ChatList>(),
  MapType<ChatListItemsModel, ChatListItems>(),
  MapType<ChatListItemsMembersInfoModel, ChatListItemsMembersInfo>(),
  MapType<ChatListItemsLastMessageInfoModel, ChatListItemsLastMessageInfo>(),
  MapType<
    ChatListItemsLastMessageInfoReplyMarkupModel,
    ChatListItemsLastMessageInfoReplyMarkup
  >(),
  MapType<
    ChatListItemsLastMessageInfoCreatedByInfoModel,
    ChatListItemsLastMessageInfoCreatedByInfo
  >(),
  MapType<ChatModel, Chat>(),
  MapType<ChatItemsModel, ChatItems>(),
  MapType<ChatItemsCreatedByInfoModel, ChatItemsCreatedByInfo>(),
  MapType<ChatItemsMessageForwardModel, ChatItemsMessageForward>(),
  MapType<ChatItemsMessageReplyInfoModel, ChatItemsMessageReplyInfo>(),
  MapType<ChatItemsReplyMarkupModel, ChatItemsReplyMarkup>(),
  MapType<ActionButtonModel, ActionButton>(),
  MapType<ChatSendModel, ChatSend>(),
  MapType<ChatUploadFileModel, ChatUploadFile>(),
  MapType<CheckinTypeModel, CheckinType>(),
  MapType<CheckinTypeItemModel, CheckinTypeItem>(),
  MapType<CreateChatGroupUserLoadModel, CreateChatGroupUserLoad>(),
  MapType<CreateChatGroupUserLoadItemsModel, CreateChatGroupUserLoadItems>(),
  MapType<CreateChatGroupModel, CreateChatGroup>(),
  MapType<CreateChatGroupItemsModel, CreateChatGroupItems>(),
  MapType<ChatItemsAttachmentModel, ChatItemsAttachment>(),
  MapType<SpeechToTextModel, SpeechToText>(),
  MapType<StoryListModel, StoryList>(),
  MapType<StoryListItemsModel, StoryListItems>(),
  MapType<AttachmentModel, Attachment>(),
  MapType<LikeListModel, LikeList>(),
  MapType<LikeListItemsModel, LikeListItems>(),
  MapType<StoryPersonListItemsModel, StoryPersonListItems>(),
  MapType<CommentListModel, CommentList>(),
  MapType<CommentListItemsModel, CommentListItems>(),
  MapType<StoryPersonListModel, StoryPersonList>(),
  MapType<
    ChatListItemsConversationDetailsModel,
    ChatListItemsConversationDetails
  >(),
  MapType<GroupChatDetailUpdateModel, GroupChatDetailUpdate>(),
  MapType<GroupChatDetailMediaLoadModel, GroupChatDetailMediaLoad>(),
  MapType<GroupChatDetailFileLoadModel, GroupChatDetailFileLoad>(),
  MapType<GroupChatDetailLinkLoadModel, GroupChatDetailLinkLoad>(),
  MapType<GroupChatDetailFileLoadItemsModel, GroupChatDetailFileLoadItems>(),
  MapType<GroupChatDetailLinkLoadItemsModel, GroupChatDetailLinkLoadItems>(),
  MapType<GroupChatDetailMediaLoadItemsModel, GroupChatDetailMediaLoadItems>(),
  MapType<MessageInfoModel, MessageInfo>(),
  MapType<GroupChatDetailAvatarUploadModel, GroupChatDetailAvatarUpload>(),
  MapType<UserListModel, UserList>(),
  MapType<StoryRuleModel, StoryRule>(),
  MapType<StoryRuleItemsModel, StoryRuleItems>(),
  MapType<ChatConversationDetailsUpdateModel, ChatConversationDetailsUpdate>(),
  MapType<GroupChatDetailModel, GroupChatDetail>(),
  MapType<ChatReactModel, ChatMessageFromSocketEvent>(),
  MapType<ChatMessageRemoveModel, ChatMessageFromSocketEvent>(),
  MapType<ChatItemsReactionsModel, ChatItemsReactions>(),
  MapType<SocialUploadFileModel, SocialUploadFile>(),
  MapType<CreatedByInfoModel, CreatedByInfo>(),
  MapType<StoryListItemsLocationModel, StoryListItemsLocation>(),
  MapType<LikeStorySuccessModel, LikeStorySuccess>(),
  MapType<LikeCommentSuccessModel, LikeCommentSuccess>(),
  MapType<ReactInfoModel, ReactInfo>(),
  MapType<ChatListSearchModel, ChatListSearch>(),
  MapType<LocationGoogleModel, LocationGoogle>(),
  MapType<LocationGoogleCandidatesModel, LocationGoogleCandidates>(),
  MapType<AddressLocationGoogleModel, AddressLocationGoogle>(),
  MapType<AddressLocationGooglePlusCodeModel, AddressLocationGooglePlusCode>(),
  MapType<AddressLocationGoogleResultsModel, AddressLocationGoogleResults>(),
  MapType<
    AddressLocationGoogleResultsAddressComponentsModel,
    AddressLocationGoogleResultsAddressComponents
  >(),
  MapType<
    AddressLocationGoogleResultsGeometryModel,
    AddressLocationGoogleResultsGeometry
  >(),
  MapType<
    AddressLocationGoogleResultsPlusCodeModel,
    AddressLocationGoogleResultsPlusCode
  >(),
  MapType<
    AddressLocationGoogleResultsGeometryLocationModel,
    AddressLocationGoogleResultsGeometryLocation
  >(),
  MapType<
    AddressLocationGoogleResultsGeometryViewportModel,
    AddressLocationGoogleResultsGeometryViewport
  >(),
  MapType<
    AddressLocationGoogleResultsGeometryViewportNortheastModel,
    AddressLocationGoogleResultsGeometryViewportNortheast
  >(),
  MapType<
    AddressLocationGoogleResultsGeometryViewportSouthwestModel,
    AddressLocationGoogleResultsGeometryViewportSouthwest
  >(),
  MapType<
    AddressLocationGoogleResultsGeometryBoundsModel,
    AddressLocationGoogleResultsGeometryBounds
  >(),
  MapType<AddressNearLocationGoogleModel, AddressNearLocationGoogle>(),
  MapType<
    AddressNearLocationGoogleResultsModel,
    AddressNearLocationGoogleResults
  >(),
  MapType<
    AddressNearLocationGoogleResultsGeometryModel,
    AddressNearLocationGoogleResultsGeometry
  >(),
  MapType<
    AddressNearLocationGoogleResultsPhotosModel,
    AddressNearLocationGoogleResultsPhotos
  >(),
  MapType<
    AddressNearLocationGoogleResultsGeometryViewportModel,
    AddressNearLocationGoogleResultsGeometryViewport
  >(),
  MapType<LatLngGoogleModel, LatLngGoogle>(),
  MapType<CommentListItemUpdatedModel, CommentListItemUpdated>(),
  MapType<TagsInfoModel, TagsInfo>(),
  MapType<AddressSearchLocationGoogleModel, AddressSearchLocationGoogle>(),
  MapType<
    AddressSearchLocationGooglePredictionsModel,
    AddressSearchLocationGooglePredictions
  >(),
  MapType<
    AddressSearchLocationGooglePredictionsStructuredModel,
    AddressSearchLocationGooglePredictionsStructured
  >(),
  MapType<AddressSearchOffsetValueModel, AddressSearchOffsetValue>(),
  MapType<AddressSearchOffsetLengthModel, AddressSearchOffsetLength>(),
  MapType<ChatItemsReplyFromModel, ChatItemsReplyFrom>(),
  MapType<
    ChatItemsReplyFromCreatedByInfoModel,
    ChatItemsReplyFromCreatedByInfo
  >(),
  MapType<ChatItemsMentionModel, ChatItemsMention>(),
  MapType<TagListModel, TagList>(),
  MapType<TagListItemsModel, TagListItems>(),
  MapType<MentionModel, MentionEntity>(),
  MapType<ChatMessageEditModel, ChatMessageFromSocketEvent>(),
  MapType<ChatListSearchItemsModel, ChatListSearchItems>(),
  MapType<ChatListSearchMessageModel, ChatListSearchMessage>(),
  MapType<ChatListSearchMessageItemsModel, ChatListSearchMessageItems>(),
  MapType<
    ChatListSearchMessageItemsCreatedByInfoModel,
    ChatListSearchMessageItemsCreatedByInfo
  >(),
  MapType<
    ChatListSearchMessageItemsConversationModel,
    ChatListSearchMessageItemsConversation
  >(),
  MapType<ChatItemsUserSeenModel, ChatItemsUserSeen>(),
  MapType<ChatListPinConversationModel, ChatListPinConversation>(),
  MapType<BranchModelV2, BranchV2>(),
  MapType<BranchItemsV2Model, BranchItemsV2>(),
  MapType<ChatListGetTotalUnreadModel, ChatListGetTotalUnread>(),
  MapType<EmojiListModel, EmojiList>(),
  MapType<EmojiListItemsModel, EmojiListItems>(),
  MapType<StoryListEmojiModel, StoryListEmoji>(),
  MapType<ChatGetConversationModel, ChatGetConversation>(),
  MapType<ChatItemsForwardFromModel, ChatItemsForwardFrom>(),
  MapType<
    ChatItemsForwardFromCreatedByInfoModel,
    ChatItemsForwardFromCreatedByInfo
  >(),
  MapType<HistoryCheckinFetchModel, HistoryCheckinFetch>(),
  MapType<HistoryCheckinDetailModel, HistoryCheckinDetail>(),
  MapType<HistoryCheckinRecordModel, HistoryCheckinRecord>(),
  MapType<StoryDetailModel, StoryDetail>(),
  MapType<NotificationListModel, NotificationList>(),
  MapType<NotificationListItemsModel, NotificationListItems>(),
  MapType<NotificationListDetailModel, NotificationListDetail>(),
  MapType<TotalNotificationModel, TotalNotification>(),
  MapType<EmptyModel, EmptyEntity>(),
  MapType<
    ChatGetConversationMembersInfoModel,
    ChatGetConversationMembersInfo
  >(),
  MapType<
    ChatGetConversationConversationDetailsModel,
    ChatGetConversationConversationDetails
  >(),
  MapType<
    ChatGetConversationLastMessageInfoReplyMarkupModel,
    ChatGetConversationLastMessageInfoReplyMarkup
  >(),
  MapType<
    ChatGetConversationLastMessageInfoForwardModel,
    ChatGetConversationLastMessageInfoForward
  >(),
  MapType<
    ChatGetConversationLastMessageInfoCreatedByInfoModel,
    ChatGetConversationLastMessageInfoCreatedByInfo
  >(),
  MapType<
    ChatListGetConversationByInviteIdModel,
    ChatListGetConversationByInviteId
  >(),
  MapType<ChatListJoinGroupModel, ChatListJoinGroup>(),
  MapType<
    ChatListGetConversationByInviteIdMembersInfoModel,
    ChatListGetConversationByInviteIdMembersInfo
  >(),
  MapType<LoginSocialModel, LoginSocial>(),
  MapType<LoginSocialUserModel, LoginSocialUser>(),
  MapType<DeleteNotificationModel, DeleteNotification>(),
  MapType<
    LoginSocketAccessTokenGetUserBotMessagePermissionModel,
    LoginSocketAccessTokenGetUserBotMessagePermission
  >(),
  MapType<CreateChatFolderModel, CreateChatFolder>(),
  MapType<CreateChatFolderLoadModel, CreateChatFolderLoad>(),
  MapType<CreateChatFolderLoadDocsModel, CreateChatFolderLoadDocs>(),
  MapType<CreateChatFolderRemoveFolderModel, CreateChatFolderRemoveFolder>(),
  MapType<
    CreateChatFolderRemoveFolderFolderModel,
    CreateChatFolderRemoveFolderFolder
  >(),
  MapType<
    CreateChatFolderConversationLoadModel,
    CreateChatFolderConversationLoad
  >(),
  MapType<
    CreateChatFolderConversationLoadFolderModel,
    CreateChatFolderConversationLoadFolder
  >(),
  MapType<
    CreateChatFolderConversationLoadConversationsModel,
    CreateChatFolderConversationLoadConversations
  >(),
  MapType<CreateChatFolderUpdateModel, CreateChatFolderUpdate>(),
  MapType<ChatPinMessageModel, ChatPinMessage>(),
  MapType<ChatUnpinMessageModel, ChatUnpinMessage>(),
  MapType<ChatGetPinListModel, ChatGetPinList>(),
  MapType<ChatUnpinMessageItemsModel, ChatUnpinMessageItems>(),
  MapType<ChatGetPinListMessagesModel, ChatGetPinListMessages>(),
  MapType<PollModel, Poll>(),
  MapType<PollOptionEntiesModel, PollOptionEnties>(),
  MapType<StoryVoteResponseModel, StoryVoteResponse>(),
  MapType<PollInfoModel, PollInfo>(),
  MapType<VoteItemModel, VoteItem>(),
  MapType<StoryVoteUsersModel, StoryVoteUsers>(),
  MapType<MemberVoteListModel, MemberVoteList>(),
  MapType<ChatItemsPollInfoModel, ChatItemsPollInfo>(),
  MapType<ChatItemsPollInfoOptionsModel, ChatItemsPollInfoOptions>(),
  MapType<ChatVotePollModel, ChatVotePoll>(),
  MapType<ChatUpdatePollModel, ChatUpdatePoll>(),
  MapType<
    TakingCareCustomerGetTreatmentPhotoModel,
    TakingCareCustomerGetTreatmentPhoto
  >(),
  MapType<
    TakingCareCustomerGetTreatmentPhotoItemsModel,
    TakingCareCustomerGetTreatmentPhotoItems
  >(),
  MapType<
    TakingCareCustomerGetTreatmentPhotoItemsDetailModel,
    TakingCareCustomerGetTreatmentPhotoItemsDetail
  >(),
  MapType<PxRecheckWorkStatusUpdateModel, PxRecheckWorkStatusUpdate>(),
  MapType<SettingSendKycPhotosModel, SettingSendKycPhotos>(),
  MapType<TicketV2Model, TicketV2>(),
  MapType<TicketV2ItemsModel, TicketV2Items>(),
  MapType<TicketTypeModel, TicketType>(),
  MapType<TicketTypeItemsModel, TicketTypeItems>(),
  MapType<TicketCreatedTypeModel, TicketCreatedType>(),
  MapType<TicketCreatedTypeItemsModel, TicketCreatedTypeItems>(),
  MapType<TicketGroupTypeModel, TicketGroupType>(),
  MapType<TicketGroupTypeItemsModel, TicketGroupTypeItems>(),
  MapType<OrderFoodModel, OrderFood>(),
  MapType<OrderFoodItemsModel, OrderFoodItems>(),
  MapType<OrderFoodItemsQrCodeModel, OrderFoodItemsQrCode>(),
  MapType<OrderFoodItemsFeedbackModel, OrderFoodItemsFeedback>(),
  MapType<OrderFoodItemsQrCode, OrderFoodItemsQrCodeModel>(),
  MapType<TicketDetailModel, TicketDetail>(),
  MapType<TicketActiveModel, TicketActive>(),
  MapType<TicketActiveItemsModel, TicketActiveItems>(),
  MapType<TicketDetailReasonModel, TicketDetailReason>(),
  MapType<TicketDetailReasonItemsModel, TicketDetailReasonItems>(),
  MapType<OrderFoodReportModel, OrderFoodReport>(),
  MapType<OrderFoodQrCodeModel, OrderFoodQrCode>(),
  MapType<OrderFoodItemsQrCodeDataModel, OrderFoodItemsQrCodeData>(),
  MapType<OrderFoodItemsQrCodeData, OrderFoodItemsQrCodeDataModel>(),
  MapType<OrderFoodQrScanModel, OrderFoodQrScan>(),
  MapType<FoodSetDefaultAddressModel, FoodSetDefaultAddress>(),
  MapType<
    CustomerBookingInfoServiceOmDetailsModel,
    CustomerBookingInfoServiceOmDetails
  >(),
  MapType<
    CustomerBookingInfoServiceOmDetailsItemsModel,
    CustomerBookingInfoServiceOmDetailsItems
  >(),
  MapType<TreatmentNoteModel, TreatmentNote>(),
  MapType<TreatmentNoteItemsModel, TreatmentNoteItems>(),
  MapType<FitCustomerInfoItemsModel, FitCustomerInfoItems>(),
  MapType<ResultOfFitItemsModel, ResultOfFitItems>(),
  MapType<TakingCareCustomerGetSectionModel, TakingCareCustomerGetSection>(),
  MapType<
    TakingCareCustomerGetSectionServiceMasterModel,
    TakingCareCustomerGetSectionServiceMaster
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceDetailModel,
    TakingCareCustomerGetSectionServiceDetail
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceStepModel,
    TakingCareCustomerGetSectionServiceStep
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceDetailInfosModel,
    TakingCareCustomerGetSectionServiceDetailInfos
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceDetailItemsModel,
    TakingCareCustomerGetSectionServiceDetailItems
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceDetailItemPropsModel,
    TakingCareCustomerGetSectionServiceDetailItemProps
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceStepMasterInfoModel,
    TakingCareCustomerGetSectionServiceStepMasterInfo
  >(),
  MapType<
    TakingCareCustomerGetSectionServiceStepDetailInfoModel,
    TakingCareCustomerGetSectionServiceStepDetailInfo
  >(),
  MapType<InitialConditionInfoModel, InitialConditionInfo>(),
  MapType<
    ConsultationCustomerGetServiceUsageModel,
    ConsultationCustomerGetServiceUsage
  >(),
  MapType<
    ConsultationCustomerGetServiceUsageServiceInfoModel,
    ConsultationCustomerGetServiceUsageServiceInfo
  >(),
  MapType<
    ConsultationCustomerGetServiceUsageServiceDetailModel,
    ConsultationCustomerGetServiceUsageServiceDetail
  >(),
  MapType<
    ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModel,
    ConsultationCustomerGetServiceUsageServiceDetailPropertyInfos
  >(),
  MapType<ServiceInsideTicketListModel, ServiceInsideTicketList>(),
  MapType<ServiceInsideTicketItemModel, ServiceInsideTicketItem>(),
  MapType<CommonPartModel, CommonPart>(),
  MapType<PxRecheckAssignsFetchModel, PxRecheckAssignsFetch>(),
  MapType<
    PxRecheckAssignsFetchMasterInfoModel,
    PxRecheckAssignsFetchMasterInfo
  >(),
  MapType<PausesTrackingModel, PausesTracking>(),
  MapType<
    PxRecheckAssignsFetchDetailInfoModel,
    PxRecheckAssignsFetchDetailInfo
  >(),
  MapType<
    PxRecheckAssignsFetchDetailInfoStepListDetailModel,
    PxRecheckAssignsFetchDetailInfoStepListDetail
  >(),
  MapType<
    PxRecheckAssignsFetchDetailInfoEmployeesModel,
    PxRecheckAssignsFetchDetailInfoEmployees
  >(),
  MapType<
    PxRecheckAssignsFetchDetailInfoImagesModel,
    PxRecheckAssignsFetchDetailInfoImages
  >(),
  MapType<
    ConsultationManagerCustomerLoadModel,
    ConsultationManagerCustomerLoad
  >(),
  MapType<
    ConsultationManagerCustomerLoadItemsModel,
    ConsultationManagerCustomerLoadItems
  >(),
  MapType<PxEmployeeModel, PxEmployee>(),
  MapType<DevModel, Dev>(),
  MapType<DevMiniAppModel, DevMiniApp>(),
  MapType<DevMiniAppDocsModel, DevMiniAppDocs>(),
  MapType<DevMiniAppDocsTagsModel, DevMiniAppDocsTags>(),
  MapType<UserCheckPermissionModel, UserCheckPermission>(),
  MapType<PermissionBySegmentModel, PermissionBySegment>(),
  MapType<UserCheckPermissionPermissionModel, UserCheckPermissionPermission>(),
  MapType<
    UserCheckPermissionPermissionCheckInModel,
    UserCheckPermissionPermissionCheckIn
  >(),
  MapType<StickerModel, Sticker>(),
  MapType<StickerItemsModel, StickerItems>(),
  MapType<ChatListGetRecentContactsModel, ChatListGetRecentContacts>(),
  MapType<
    ChatListGetRecentContactsItemsModel,
    ChatListGetRecentContactsItems
  >(),
  MapType<
    ChatListGetRecentContactsItemsLastMessageInfoModel,
    ChatListGetRecentContactsItemsLastMessageInfo
  >(),
  MapType<
    ChatListGetRecentContactsItemsConversationDetailsModel,
    ChatListGetRecentContactsItemsConversationDetails
  >(),
  MapType<
    ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModel,
    ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfo
  >(),
  MapType<
    ChatListGetRecentContactsItemsMembersInfoModel,
    ChatListGetRecentContactsItemsMembersInfo
  >(),
  MapType<SkinCustomerInfoModel, SkinCustomerInfo>(),
  MapType<SkinCustomerInfoItemsModel, SkinCustomerInfoItems>(),
  MapType<ChatListUpdatePinConversationModel, ChatListUpdatePinConversation>(),
  MapType<ChatGetUserSeenModel, ChatGetUserSeen>(),
  MapType<ChatGetUserSeenDocsModel, ChatGetUserSeenDocs>(),
  MapType<RevenueDetailModel, RevenueDetail>(),
  MapType<RevenueDetailItemsModel, RevenueDetailItems>(),
  MapType<RevenueMasterModel, RevenueMaster>(),
  MapType<KpiEmployeeDetailModel, KpiEmployeeDetail>(),
  MapType<KpiEmployeeModel, KpiEmployee>(),
  MapType<RevenueItemsModel, RevenueItems>(),
  MapType<MentionMessageModel, MentionMessage>(),
  MapType<ChatSearchModel, ChatSearch>(),
  MapType<ChatListSortFolderModel, ChatListSortFolder>(),
  MapType<ChatTranscribeModel, ChatTranscribe>(),
  MapType<ChatReplyBotMessageModel, ChatReplyBotMessage>(),
  MapType<ChatGetUserStickerModel, ChatGetUserSticker>(),
  MapType<ChatGetUserStickerItemsModel, ChatGetUserStickerItems>(),
  MapType<
    ChatGetUserStickerItemsStickersModel,
    ChatGetUserStickerItemsStickers
  >(),
  MapType<ChatStickerModel, ChatSticker>(),
  MapType<
    LoginSocketAccessTokenGetUserMessagePermissionModel,
    LoginSocketAccessTokenGetUserMessagePermission
  >(),
  MapType<StickerSocialModel, StickerSocial>(),
  MapType<StickerSocialItemsModel, StickerSocialItems>(),
  MapType<StickerSetSocialModel, StickerSetSocial>(),
  MapType<StickerSetSocialItemsModel, StickerSetSocialItems>(),
  MapType<StickerSetCreatedSuccessModel, StickerSetCreatedSuccess>(),
  MapType<GroupChatDetailMemberInfoLoadModel, GroupChatDetailMemberInfoLoad>(),
  MapType<
    GroupChatDetailMemberInfoLoadItemsModel,
    GroupChatDetailMemberInfoLoadItems
  >(),
  MapType<GroupChatDetailGetRuleByRoleModel, GroupChatDetailGetRuleByRole>(),
  MapType<GroupChatDetailGetUserRulesModel, GroupChatDetailGetUserRules>(),
  MapType<
    GroupChatDetailUpdateAdminRuleModel,
    GroupChatDetailUpdateAdminRule
  >(),
  MapType<
    GroupChatDetailUpdateMemberRuleModel,
    GroupChatDetailUpdateMemberRule
  >(),
  MapType<
    GroupChatDetailGetRuleByRoleRoleModel,
    GroupChatDetailGetRuleByRoleRole
  >(),
  MapType<
    GroupChatDetailGetRuleByRoleRoleRulesModel,
    GroupChatDetailGetRuleByRoleRoleRules
  >(),
  MapType<GroupChatDetailGetRuleByRoleRoleRules, RuleRequestParams>(),
  MapType<
    GroupChatDetailGetUserRulesRoleModel,
    GroupChatDetailGetUserRulesRole
  >(),
  MapType<
    GroupChatDetailGetUserRulesRoleRulesModel,
    GroupChatDetailGetUserRulesRoleRules
  >(),
  MapType<
    GroupChatDetailUpdateAdminRuleRulesModel,
    GroupChatDetailUpdateAdminRuleRules
  >(),
  MapType<
    GroupChatDetailUpdateMemberRuleRulesModel,
    GroupChatDetailUpdateMemberRuleRules
  >(),
  MapType<GroupChatDetailChangeOwnerModel, GroupChatDetailChangeOwner>(),
  MapType<
    ConsultationCustomerEditServiceModel,
    ConsultationCustomerEditService
  >(),
  MapType<
    ConsultationCustomerRemoveServiceModel,
    ConsultationCustomerRemoveService
  >(),
  MapType<
    GroupChatDetailGetUserExceptionModel,
    GroupChatDetailGetUserException
  >(),
  MapType<
    GroupChatDetailGetUserExceptionItemsModel,
    GroupChatDetailGetUserExceptionItems
  >(),
  MapType<
    GroupChatDetailGetUserExceptionItemsUsernameInfoModel,
    GroupChatDetailGetUserExceptionItemsUsernameInfo
  >(),
  MapType<ChatCallModel, ChatCall>(),
  MapType<
    GetConsultationTTBDParentGroupModel,
    GetConsultationTTBDParentGroup
  >(),
  MapType<TopicConsultationTTBDParentModel, TopicConsultationTTBDParent>(),
  MapType<TopicEmployeeModel, TopicEmployee>(),
  MapType<GetConsultationTTBDParentModel, GetConsultationTTBDParent>(),
  MapType<GetConsultationTTBDItemModel, GetConsultationTTBDItem>(),
  MapType<ConsultationTTBDOptionModel, ConsultationTTBDOption>(),
  MapType<ConsultationTTBDOptionEndModel, ConsultationTTBDOptionEnd>(),
  MapType<CustomerRelationShipListModel, CustomerRelationShipList>(),
  MapType<CustomerRelationShipListItemModel, CustomerRelationShipListItem>(),
  MapType<CustomerDescriptionsModel, CustomerDescriptions>(),
  MapType<ProductConfirmBranchModel, ProductConfirmBranch>(),
  MapType<ProductConfirmBranchItemsModel, ProductConfirmBranchItems>(),
  MapType<ProductConfirmModel, ProductConfirm>(),
  MapType<ProductConfirmItemsModel, ProductConfirmItems>(),
  MapType<ProductDetailConfirmModel, ProductDetailConfirm>(),
  MapType<
    ProductDetailConfirmPaymentDetailsModel,
    ProductDetailConfirmPaymentDetails
  >(),
  MapType<
    ProductDetailConfirmAttachmentFilesModel,
    ProductDetailConfirmAttachmentFiles
  >(),
  MapType<ProductDetailConfirmSugestsModel, ProductDetailConfirmSugests>(),
  MapType<
    ProductDetailConfirmSugestsItemsModel,
    ProductDetailConfirmSugestsItems
  >(),
  MapType<ProductDetailConfirmHistoriesModel, ProductDetailConfirmHistories>(),
  MapType<ComboTagModel, ComboTag>(),
  MapType<ImageByComboTagModel, ImageByComboTag>(),
  MapType<ImageByComboTagImagesModel, ImageByComboTagImages>(),
  MapType<AddTagsImageModel, AddTagsImage>(),
  MapType<AddTagsImageGetImageListModel, AddTagsImageGetImageList>(),
  MapType<AddTagsImageGetRoomListModel, AddTagsImageGetRoomList>(),
  MapType<AddTagsImageUserSearchModel, AddTagsImageUserSearch>(),
  MapType<AddTagsImageGetImageListItemsModel, AddTagsImageGetImageListItems>(),
  MapType<AddTagsImageGetRoomListItemsModel, AddTagsImageGetRoomListItems>(),
  MapType<AddTagsImageUserSearchItemsModel, AddTagsImageUserSearchItems>(),
  MapType<
    AddTagsImageGetImageListItemsImagesModel,
    AddTagsImageGetImageListItemsImages
  >(),
  MapType<
    AddTagsImageGetImageListItemsImagesTagsModel,
    AddTagsImageGetImageListItemsImagesTags
  >(),
  MapType<
    AddTagsImageGetImageListItemsImagesItemGroupsModel,
    AddTagsImageGetImageListItemsImagesItemGroups
  >(),
  MapType<AddTagsImageGetTagListModel, AddTagsImageGetTagList>(),
  MapType<AddTagsImageCreateImageTagModel, AddTagsImageCreateImageTag>(),
  MapType<AddTagsImageCreateMergeImageModel, AddTagsImageCreateMergeImage>(),
  MapType<AddTagsImageGetTagListItemsModel, AddTagsImageGetTagListItems>(),
  MapType<AddTagsImageGetTagListItems, AddTagsImageGetTagListItemsModel>(),
  MapType<ServiceTypePurchaseItemsModel, ServiceTypePurchaseItems>(),
  MapType<ComboPurchaseModel, ComboPurchase>(),
  MapType<ComboPurchaseItemsModel, ComboPurchaseItems>(),
  MapType<DealPurchaseModel, DealPurchase>(),
  MapType<ServicePurchaseModel, ServicePurchase>(),
  MapType<ServiceFromAccModel, ServiceFromAcc>(),
  MapType<ServiceFromAccItemsModel, ServiceFromAccItems>(),
  MapType<ServiceTypePurchaseDetailModel, ServiceTypePurchaseDetail>(),
  MapType<ServiceTypePurchaseDetailChildrenModel, 
  ServiceTypePurchaseDetailChildren>(),
])
@lazySingleton
class Mapper extends $Mapper {
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return true;
  }
}
