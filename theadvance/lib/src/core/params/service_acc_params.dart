// ignore_for_file: public_member_api_docs, sort_constructors_first

// Package imports:
import 'package:json_annotation/json_annotation.dart';

part 'service_acc_params.g.dart';

@JsonSerializable(createFactory: false, includeIfNull: false)
class ServiceAccParams {
  ServiceAccParams({this.pageIndex = 1, this.pageSize = 50, this.keyWord});

  final int? pageIndex;

  final int? pageSize;

  final String? keyWord;

  Map<String, dynamic> toJson() => _$ServiceAccParamsToJson(this);

  ServiceAccParams copyWith({
    final int? pageIndex,
    final int? pageSize,
    final String? keyWord,
  }) {
    return ServiceAccParams(
      pageIndex: pageIndex ?? this.pageIndex,
      pageSize: pageSize ?? this.pageSize,
      keyWord: keyWord ?? this.keyWord,
    );
  }
}
