// Package imports:
import 'package:json_annotation/json_annotation.dart';

part 'service_from_acc_detail_params.g.dart';

@JsonSerializable(createFactory: false, includeIfNull: false)
class ServiceFromAccDetailParams {
  ServiceFromAccDetailParams({ required this.itemId,  required this.partnerId});

  final String itemId;
  final String partnerId;

  Map<String, dynamic> toJson() => _$ServiceFromAccDetailParamsToJson(this);
}
