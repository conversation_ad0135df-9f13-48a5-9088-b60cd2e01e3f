// Package imports:
import 'package:json_annotation/json_annotation.dart';

part 'create_combo_body.g.dart';

@JsonSerializable(createFactory: false, includeIfNull: false)
class CreateComboBody {
  CreateComboBody({
    this.rowKey,
    this.transactionDate,
    this.partnerID,
    this.transactionID,
    this.transferFromPartnerID,
    this.transferToPartnerID,
    this.itemID,
    this.recordType,
    this.volume,
    this.unitPrice,
    this.itemIDParent,
    this.itemIDChildrens,
    this.switchFromItemID,
    this.switchToItemID,
    this.createdBy,
    this.updatedBy,
    this.totalAmount,
    this.discountAmount,
    this.notes,
  });

  @Json<PERSON><PERSON>(name: 'RowKey')
  final int? rowKey;

  @J<PERSON><PERSON><PERSON>(name: 'TransactionDate')
  final String? transactionDate;

  @Json<PERSON>ey(name: 'PartnerID')
  final String? partnerID;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'TransactionID')
  final String? transactionID;

  @<PERSON>son<PERSON><PERSON>(name: 'TransferFromPartnerID')
  final String? transferFromPartnerID;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'TransferToPartnerID')
  final String? transferToPartnerID;

  @Json<PERSON>ey(name: 'ItemID')
  final String? itemID;

  @Json<PERSON><PERSON>(name: 'RecordType')
  final String? recordType;

  @JsonKey(name: 'Volume')
  final int? volume;

  @JsonKey(name: 'UnitPrice')
  final double? unitPrice;

  @JsonKey(name: 'TotalAmount')
  final double? totalAmount;

  @JsonKey(name: 'DiscountAmount')
  final double? discountAmount;

  @JsonKey(name: 'ItemIDParent')
  final String? itemIDParent;

  @JsonKey(name: 'ItemIDChildrens')
  final List<String>? itemIDChildrens;

  @JsonKey(name: 'Notes')
  final String? notes;

  @JsonKey(name: 'SwitchFromItemID')
  final String? switchFromItemID;

  @JsonKey(name: 'SwitchToItemID')
  final String? switchToItemID;

  @JsonKey(name: 'CreatedBy')
  final String? createdBy;

  @JsonKey(name: 'UpdatedBy')
  final String? updatedBy;

  Map<String, dynamic> toJson() => _$CreateComboBodyToJson(this);
}
