// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as _i8;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;
import 'package:theadvance/src/core/network/ez_network.dart' as _i11;
import 'package:theadvance/src/core/routes/app_router.dart' as _i10;
import 'package:theadvance/src/core/utils/deeplink_helper.dart' as _i7;
import 'package:theadvance/src/core/utils/mappers.dart' as _i6;
import 'package:theadvance/src/core/utils/stringee_helper.dart' as _i5;
import 'package:theadvance/src/data/datasources/ez_datasources.dart' as _i9;
import 'package:theadvance/src/data/datasources/local/add_tags_image/add_tags_image_dao.dart'
    as _i86;
import 'package:theadvance/src/data/datasources/local/add_tags_image/add_tags_image_dao_impl.dart'
    as _i87;
import 'package:theadvance/src/data/datasources/local/assign_task/assign_task_dao.dart'
    as _i78;
import 'package:theadvance/src/data/datasources/local/assign_task/assign_task_dao_impl.dart'
    as _i79;
import 'package:theadvance/src/data/datasources/local/branch_chat_list/branch_chat_list_dao.dart'
    as _i60;
import 'package:theadvance/src/data/datasources/local/branch_chat_list/branch_chat_list_dao_impl.dart'
    as _i61;
import 'package:theadvance/src/data/datasources/local/branch_selection/branch_selection_dao.dart'
    as _i42;
import 'package:theadvance/src/data/datasources/local/branch_selection/branch_selection_dao_impl.dart'
    as _i43;
import 'package:theadvance/src/data/datasources/local/cache/hive/ez_cache.dart'
    as _i23;
import 'package:theadvance/src/data/datasources/local/chat/chat_dao.dart'
    as _i150;
import 'package:theadvance/src/data/datasources/local/chat/chat_dao_impl.dart'
    as _i151;
import 'package:theadvance/src/data/datasources/local/chat_list/chat_list_dao.dart'
    as _i94;
import 'package:theadvance/src/data/datasources/local/chat_list/chat_list_dao_impl.dart'
    as _i95;
import 'package:theadvance/src/data/datasources/local/chat_select_branch/chat_select_branch_dao.dart'
    as _i114;
import 'package:theadvance/src/data/datasources/local/chat_select_branch/chat_select_branch_dao_impl.dart'
    as _i115;
import 'package:theadvance/src/data/datasources/local/checkin_photo/checkin_photo_dao.dart'
    as _i28;
import 'package:theadvance/src/data/datasources/local/checkin_photo/checkin_photo_dao_impl.dart'
    as _i29;
import 'package:theadvance/src/data/datasources/local/consultation_customer/consultation_customer_dao.dart'
    as _i80;
import 'package:theadvance/src/data/datasources/local/consultation_customer/consultation_customer_dao_impl.dart'
    as _i81;
import 'package:theadvance/src/data/datasources/local/consultation_manager/consultation_manager_dao.dart'
    as _i96;
import 'package:theadvance/src/data/datasources/local/consultation_manager/consultation_manager_dao_impl.dart'
    as _i97;
import 'package:theadvance/src/data/datasources/local/create_chat_folder/create_chat_folder_dao.dart'
    as _i62;
import 'package:theadvance/src/data/datasources/local/create_chat_folder/create_chat_folder_dao_impl.dart'
    as _i63;
import 'package:theadvance/src/data/datasources/local/create_chat_group/create_chat_group_dao.dart'
    as _i104;
import 'package:theadvance/src/data/datasources/local/create_chat_group/create_chat_group_dao_impl.dart'
    as _i105;
import 'package:theadvance/src/data/datasources/local/create_customer/create_customer_dao.dart'
    as _i98;
import 'package:theadvance/src/data/datasources/local/create_customer/create_customer_dao_impl.dart'
    as _i99;
import 'package:theadvance/src/data/datasources/local/customer_booking_info/customer_booking_info_dao.dart'
    as _i100;
import 'package:theadvance/src/data/datasources/local/customer_booking_info/customer_booking_info_dao_impl.dart'
    as _i101;
import 'package:theadvance/src/data/datasources/local/customer_info_details/customer_info_details_dao.dart'
    as _i134;
import 'package:theadvance/src/data/datasources/local/customer_info_details/customer_info_details_dao_impl.dart'
    as _i135;
import 'package:theadvance/src/data/datasources/local/customer_list/customer_list_dao.dart'
    as _i52;
import 'package:theadvance/src/data/datasources/local/customer_list/customer_list_dao_impl.dart'
    as _i53;
import 'package:theadvance/src/data/datasources/local/customer_profile/customer_profile_dao.dart'
    as _i92;
import 'package:theadvance/src/data/datasources/local/customer_profile/customer_profile_dao_impl.dart'
    as _i93;
import 'package:theadvance/src/data/datasources/local/customer_record/customer_record_dao.dart'
    as _i110;
import 'package:theadvance/src/data/datasources/local/customer_record/customer_record_dao_impl.dart'
    as _i111;
import 'package:theadvance/src/data/datasources/local/customer_schedule/customer_schedule_dao.dart'
    as _i56;
import 'package:theadvance/src/data/datasources/local/customer_schedule/customer_schedule_dao_impl.dart'
    as _i57;
import 'package:theadvance/src/data/datasources/local/detail_crm_customer/detail_crm_customer_dao.dart'
    as _i58;
import 'package:theadvance/src/data/datasources/local/detail_crm_customer/detail_crm_customer_dao_impl.dart'
    as _i59;
import 'package:theadvance/src/data/datasources/local/detail_staff_evaluation_period/detail_staff_evaluation_period_dao.dart'
    as _i140;
import 'package:theadvance/src/data/datasources/local/detail_staff_evaluation_period/detail_staff_evaluation_period_dao_impl.dart'
    as _i141;
import 'package:theadvance/src/data/datasources/local/dev/dev_dao.dart' as _i84;
import 'package:theadvance/src/data/datasources/local/dev/dev_dao_impl.dart'
    as _i85;
import 'package:theadvance/src/data/datasources/local/feedback/feedback_dao.dart'
    as _i64;
import 'package:theadvance/src/data/datasources/local/feedback/feedback_dao_impl.dart'
    as _i65;
import 'package:theadvance/src/data/datasources/local/group_chat_detail/group_chat_detail_dao.dart'
    as _i26;
import 'package:theadvance/src/data/datasources/local/group_chat_detail/group_chat_detail_dao_impl.dart'
    as _i27;
import 'package:theadvance/src/data/datasources/local/hr_organization/hr_organization_dao.dart'
    as _i34;
import 'package:theadvance/src/data/datasources/local/hr_organization/hr_organization_dao_impl.dart'
    as _i35;
import 'package:theadvance/src/data/datasources/local/important_notes/important_notes_dao.dart'
    as _i120;
import 'package:theadvance/src/data/datasources/local/important_notes/important_notes_dao_impl.dart'
    as _i121;
import 'package:theadvance/src/data/datasources/local/list_customer/list_customer_dao.dart'
    as _i108;
import 'package:theadvance/src/data/datasources/local/list_customer/list_customer_dao_impl.dart'
    as _i109;
import 'package:theadvance/src/data/datasources/local/location_google/location_google_dao.dart'
    as _i24;
import 'package:theadvance/src/data/datasources/local/location_google/location_google_dao_impl.dart'
    as _i25;
import 'package:theadvance/src/data/datasources/local/medical_department_list/medical_department_list_dao.dart'
    as _i88;
import 'package:theadvance/src/data/datasources/local/medical_department_list/medical_department_list_dao_impl.dart'
    as _i89;
import 'package:theadvance/src/data/datasources/local/medical_log_detail/medical_log_detail_dao.dart'
    as _i30;
import 'package:theadvance/src/data/datasources/local/medical_log_detail/medical_log_detail_dao_impl.dart'
    as _i31;
import 'package:theadvance/src/data/datasources/local/medical_product_creation/medical_product_creation_dao.dart'
    as _i50;
import 'package:theadvance/src/data/datasources/local/medical_product_creation/medical_product_creation_dao_impl.dart'
    as _i51;
import 'package:theadvance/src/data/datasources/local/medical_service_creation/medical_service_creation_dao.dart'
    as _i19;
import 'package:theadvance/src/data/datasources/local/medical_service_creation/medical_service_creation_dao_impl.dart'
    as _i20;
import 'package:theadvance/src/data/datasources/local/medical_service_list/medical_service_list_dao.dart'
    as _i82;
import 'package:theadvance/src/data/datasources/local/medical_service_list/medical_service_list_dao_impl.dart'
    as _i83;
import 'package:theadvance/src/data/datasources/local/medical_service_log_list/medical_service_log_list_dao.dart'
    as _i54;
import 'package:theadvance/src/data/datasources/local/medical_service_log_list/medical_service_log_list_dao_impl.dart'
    as _i55;
import 'package:theadvance/src/data/datasources/local/medical_template_list/medical_template_list_dao.dart'
    as _i122;
import 'package:theadvance/src/data/datasources/local/medical_template_list/medical_template_list_dao_impl.dart'
    as _i123;
import 'package:theadvance/src/data/datasources/local/medicine_detail/medicine_detail_dao.dart'
    as _i112;
import 'package:theadvance/src/data/datasources/local/medicine_detail/medicine_detail_dao_impl.dart'
    as _i113;
import 'package:theadvance/src/data/datasources/local/note_details/note_details_dao.dart'
    as _i102;
import 'package:theadvance/src/data/datasources/local/note_details/note_details_dao_impl.dart'
    as _i103;
import 'package:theadvance/src/data/datasources/local/notification_list/notification_list_dao.dart'
    as _i136;
import 'package:theadvance/src/data/datasources/local/notification_list/notification_list_dao_impl.dart'
    as _i137;
import 'package:theadvance/src/data/datasources/local/product_confirm/product_confirm_dao.dart'
    as _i106;
import 'package:theadvance/src/data/datasources/local/product_confirm/product_confirm_dao_impl.dart'
    as _i107;
import 'package:theadvance/src/data/datasources/local/px_list/px_list_dao.dart'
    as _i38;
import 'package:theadvance/src/data/datasources/local/px_list/px_list_dao_impl.dart'
    as _i39;
import 'package:theadvance/src/data/datasources/local/px_recheck/px_recheck_dao.dart'
    as _i32;
import 'package:theadvance/src/data/datasources/local/px_recheck/px_recheck_dao_impl.dart'
    as _i33;
import 'package:theadvance/src/data/datasources/local/px_task_list/px_task_list_dao.dart'
    as _i21;
import 'package:theadvance/src/data/datasources/local/px_task_list/px_task_list_dao_impl.dart'
    as _i22;
import 'package:theadvance/src/data/datasources/local/px_unasigned/px_unasigned_dao.dart'
    as _i48;
import 'package:theadvance/src/data/datasources/local/px_unasigned/px_unasigned_dao_impl.dart'
    as _i49;
import 'package:theadvance/src/data/datasources/local/px_unasigned_update/px_unasigned_update_dao.dart'
    as _i76;
import 'package:theadvance/src/data/datasources/local/px_unasigned_update/px_unasigned_update_dao_impl.dart'
    as _i77;
import 'package:theadvance/src/data/datasources/local/schedule_details/schedule_details_dao.dart'
    as _i118;
import 'package:theadvance/src/data/datasources/local/schedule_details/schedule_details_dao_impl.dart'
    as _i119;
import 'package:theadvance/src/data/datasources/local/select_px_room/select_px_room_dao.dart'
    as _i132;
import 'package:theadvance/src/data/datasources/local/select_px_room/select_px_room_dao_impl.dart'
    as _i133;
import 'package:theadvance/src/data/datasources/local/service_and_product/service_and_product_dao.dart'
    as _i46;
import 'package:theadvance/src/data/datasources/local/service_and_product/service_and_product_dao_impl.dart'
    as _i47;
import 'package:theadvance/src/data/datasources/local/staff_evaluation_periods/staff_evaluation_periods_dao.dart'
    as _i138;
import 'package:theadvance/src/data/datasources/local/staff_evaluation_periods/staff_evaluation_periods_dao_impl.dart'
    as _i139;
import 'package:theadvance/src/data/datasources/local/story_detail/story_detail_dao.dart'
    as _i116;
import 'package:theadvance/src/data/datasources/local/story_detail/story_detail_dao_impl.dart'
    as _i117;
import 'package:theadvance/src/data/datasources/local/tag_list/tag_list_dao.dart'
    as _i12;
import 'package:theadvance/src/data/datasources/local/tag_list/tag_list_dao_impl.dart'
    as _i13;
import 'package:theadvance/src/data/datasources/local/taking_care_customer/taking_care_customer_dao.dart'
    as _i36;
import 'package:theadvance/src/data/datasources/local/taking_care_customer/taking_care_customer_dao_impl.dart'
    as _i37;
import 'package:theadvance/src/data/datasources/local/ticket_detail/ticket_detail_dao.dart'
    as _i148;
import 'package:theadvance/src/data/datasources/local/ticket_detail/ticket_detail_dao_impl.dart'
    as _i149;
import 'package:theadvance/src/data/datasources/local/user_list/user_list_dao.dart'
    as _i128;
import 'package:theadvance/src/data/datasources/local/user_list/user_list_dao_impl.dart'
    as _i129;
import 'package:theadvance/src/data/datasources/remote/api_services.dart'
    as _i18;
import 'package:theadvance/src/data/datasources/remote/chat_api_service.dart'
    as _i145;
import 'package:theadvance/src/data/datasources/remote/comment_list_api_service.dart'
    as _i15;
import 'package:theadvance/src/data/datasources/remote/create_chat_group_api_service.dart'
    as _i189;
import 'package:theadvance/src/data/datasources/remote/like_list_api_service.dart'
    as _i16;
import 'package:theadvance/src/data/datasources/remote/media_upload_api_service.dart'
    as _i144;
import 'package:theadvance/src/data/datasources/remote/note_list_api_service.dart'
    as _i188;
import 'package:theadvance/src/data/datasources/remote/rating_human_api_service.dart'
    as _i187;
import 'package:theadvance/src/data/datasources/remote/schedule_details_api_service.dart'
    as _i439;
import 'package:theadvance/src/data/datasources/remote/social_upload_api_service.dart'
    as _i146;
import 'package:theadvance/src/data/datasources/remote/sticker_social_api_service.dart'
    as _i147;
import 'package:theadvance/src/data/datasources/remote/story_list_api_service.dart'
    as _i14;
import 'package:theadvance/src/data/datasources/remote/story_person_list_api_service.dart'
    as _i17;
import 'package:theadvance/src/data/datasources/remote/tag_image_service.dart'
    as _i192;
import 'package:theadvance/src/data/datasources/remote/ticket_active_api_service.dart'
    as _i191;
import 'package:theadvance/src/data/datasources/remote/ticket_api_service.dart'
    as _i190;
import 'package:theadvance/src/data/repositories/add_tags_image_repository_impl.dart'
    as _i275;
import 'package:theadvance/src/data/repositories/assign_task_repository_impl.dart'
    as _i660;
import 'package:theadvance/src/data/repositories/branch_chat_list_repository_impl.dart'
    as _i428;
import 'package:theadvance/src/data/repositories/branch_selection_repository_impl.dart'
    as _i200;
import 'package:theadvance/src/data/repositories/chat_list_repository_impl.dart'
    as _i41;
import 'package:theadvance/src/data/repositories/chat_repository_impl.dart'
    as _i179;
import 'package:theadvance/src/data/repositories/chat_select_branch_repository_impl.dart'
    as _i342;
import 'package:theadvance/src/data/repositories/checkin_photo_repository_impl.dart'
    as _i254;
import 'package:theadvance/src/data/repositories/checkin_repository_impl.dart'
    as _i405;
import 'package:theadvance/src/data/repositories/comment_list_repository_impl.dart'
    as _i478;
import 'package:theadvance/src/data/repositories/consultation_customer_repository_impl.dart'
    as _i270;
import 'package:theadvance/src/data/repositories/consultation_manager_repository_impl.dart'
    as _i651;
import 'package:theadvance/src/data/repositories/create_chat_folder_repository_impl.dart'
    as _i127;
import 'package:theadvance/src/data/repositories/create_chat_group_repository_impl.dart'
    as _i45;
import 'package:theadvance/src/data/repositories/create_customer_repository_impl.dart'
    as _i403;
import 'package:theadvance/src/data/repositories/customer_booking_info_repository_impl.dart'
    as _i390;
import 'package:theadvance/src/data/repositories/customer_info_details_repository_impl.dart'
    as _i574;
import 'package:theadvance/src/data/repositories/customer_list_repository_impl.dart'
    as _i392;
import 'package:theadvance/src/data/repositories/customer_profile_repository_impl.dart'
    as _i413;
import 'package:theadvance/src/data/repositories/customer_record_repository_impl.dart'
    as _i332;
import 'package:theadvance/src/data/repositories/customer_repository_impl.dart'
    as _i294;
import 'package:theadvance/src/data/repositories/customer_schedule_repository_impl.dart'
    as _i525;
import 'package:theadvance/src/data/repositories/detail_crm_customer_repository_impl.dart'
    as _i379;
import 'package:theadvance/src/data/repositories/detail_staff_evaluation_period_repository_impl.dart'
    as _i206;
import 'package:theadvance/src/data/repositories/dev_repository_impl.dart'
    as _i358;
import 'package:theadvance/src/data/repositories/eform_repository_impl.dart'
    as _i497;
import 'package:theadvance/src/data/repositories/feedback_repository_impl.dart'
    as _i273;
import 'package:theadvance/src/data/repositories/food_repository_impl.dart'
    as _i198;
import 'package:theadvance/src/data/repositories/group_chat_detail_repository_impl.dart'
    as _i173;
import 'package:theadvance/src/data/repositories/helper_repository_impl.dart'
    as _i131;
import 'package:theadvance/src/data/repositories/home_repository_impl.dart'
    as _i464;
import 'package:theadvance/src/data/repositories/hr_organization_repository_impl.dart'
    as _i576;
import 'package:theadvance/src/data/repositories/important_notes_repository_impl.dart'
    as _i208;
import 'package:theadvance/src/data/repositories/kpi_employee_repository_impl.dart'
    as _i527;
import 'package:theadvance/src/data/repositories/like_list_repository_impl.dart'
    as _i196;
import 'package:theadvance/src/data/repositories/list_customer_repository_impl.dart'
    as _i435;
import 'package:theadvance/src/data/repositories/location_google_repository_impl.dart'
    as _i360;
import 'package:theadvance/src/data/repositories/media_upload_repository_impl.dart'
    as _i388;
import 'package:theadvance/src/data/repositories/medical_department_list_repository_impl.dart'
    as _i204;
import 'package:theadvance/src/data/repositories/medical_log_detail_repository_impl.dart'
    as _i518;
import 'package:theadvance/src/data/repositories/medical_product_create_repository_impl.dart'
    as _i462;
import 'package:theadvance/src/data/repositories/medical_service_create_repository_impl.dart'
    as _i708;
import 'package:theadvance/src/data/repositories/medical_service_list_repository_impl.dart'
    as _i374;
import 'package:theadvance/src/data/repositories/medical_service_log_list_repository_impl.dart'
    as _i662;
import 'package:theadvance/src/data/repositories/medical_template_list_repository_impl.dart'
    as _i684;
import 'package:theadvance/src/data/repositories/medicine_detail_repository_impl.dart'
    as _i212;
import 'package:theadvance/src/data/repositories/news_repository_impl.dart'
    as _i401;
import 'package:theadvance/src/data/repositories/note_details_repository_impl.dart'
    as _i425;
import 'package:theadvance/src/data/repositories/notification_list_repository_impl.dart'
    as _i91;
import 'package:theadvance/src/data/repositories/notification_repository_impl.dart'
    as _i495;
import 'package:theadvance/src/data/repositories/product_confirm_repository_impl.dart'
    as _i376;
import 'package:theadvance/src/data/repositories/px_list_repository_impl.dart'
    as _i354;
import 'package:theadvance/src/data/repositories/px_recheck_repository_impl.dart'
    as _i394;
import 'package:theadvance/src/data/repositories/px_task_list_repository_impl.dart'
    as _i256;
import 'package:theadvance/src/data/repositories/px_unasigned_repository_impl.dart'
    as _i452;
import 'package:theadvance/src/data/repositories/px_unasigned_update_repository_impl.dart'
    as _i559;
import 'package:theadvance/src/data/repositories/rating_human_repository_impl.dart'
    as _i260;
import 'package:theadvance/src/data/repositories/request_repository_impl.dart'
    as _i698;
import 'package:theadvance/src/data/repositories/schedule_details_repository_impl.dart'
    as _i438;
import 'package:theadvance/src/data/repositories/select_px_room_repository_impl.dart'
    as _i194;
import 'package:theadvance/src/data/repositories/service_and_product_repository_impl.dart'
    as _i509;
import 'package:theadvance/src/data/repositories/setting_repository_impl.dart'
    as _i143;
import 'package:theadvance/src/data/repositories/staff_evaluation_periods_repository_impl.dart'
    as _i450;
import 'package:theadvance/src/data/repositories/staff_repository_impl.dart'
    as _i456;
import 'package:theadvance/src/data/repositories/sticker_social_repository_impl.dart'
    as _i186;
import 'package:theadvance/src/data/repositories/story_detail_repository_impl.dart'
    as _i125;
import 'package:theadvance/src/data/repositories/story_list_repository_impl.dart'
    as _i171;
import 'package:theadvance/src/data/repositories/story_person_list_repository_impl.dart'
    as _i169;
import 'package:theadvance/src/data/repositories/tag_image_repository_impl.dart'
    as _i710;
import 'package:theadvance/src/data/repositories/tag_list_repository_impl.dart'
    as _i153;
import 'package:theadvance/src/data/repositories/taking_care_customer_repository_impl.dart'
    as _i382;
import 'package:theadvance/src/data/repositories/task_repository_impl.dart'
    as _i356;
import 'package:theadvance/src/data/repositories/ticket_active_repository_impl.dart'
    as _i202;
import 'package:theadvance/src/data/repositories/ticket_detail_repository_impl.dart'
    as _i396;
import 'package:theadvance/src/data/repositories/ticketv2_repository_impl.dart'
    as _i258;
import 'package:theadvance/src/data/repositories/user_list_repository_impl.dart'
    as _i561;
import 'package:theadvance/src/data/repositories/user_repository_impl.dart'
    as _i268;
import 'package:theadvance/src/data/repositories/user_ticket_repository_impl.dart'
    as _i210;
import 'package:theadvance/src/domain/repositories/add_tags_image_repository.dart'
    as _i274;
import 'package:theadvance/src/domain/repositories/assign_task_repository.dart'
    as _i659;
import 'package:theadvance/src/domain/repositories/branch_chat_list_repository.dart'
    as _i427;
import 'package:theadvance/src/domain/repositories/branch_selection_repository.dart'
    as _i199;
import 'package:theadvance/src/domain/repositories/chat_list_repository.dart'
    as _i40;
import 'package:theadvance/src/domain/repositories/chat_repository.dart'
    as _i178;
import 'package:theadvance/src/domain/repositories/chat_select_branch_repository.dart'
    as _i341;
import 'package:theadvance/src/domain/repositories/checkin_photo_repository.dart'
    as _i253;
import 'package:theadvance/src/domain/repositories/checkin_repository.dart'
    as _i404;
import 'package:theadvance/src/domain/repositories/comment_list_repository.dart'
    as _i477;
import 'package:theadvance/src/domain/repositories/consultation_customer_repository.dart'
    as _i269;
import 'package:theadvance/src/domain/repositories/consultation_manager_repository.dart'
    as _i650;
import 'package:theadvance/src/domain/repositories/create_chat_folder_repository.dart'
    as _i126;
import 'package:theadvance/src/domain/repositories/create_chat_group_repository.dart'
    as _i44;
import 'package:theadvance/src/domain/repositories/create_customer_repository.dart'
    as _i402;
import 'package:theadvance/src/domain/repositories/customer_booking_info_repository.dart'
    as _i389;
import 'package:theadvance/src/domain/repositories/customer_info_details_repository.dart'
    as _i573;
import 'package:theadvance/src/domain/repositories/customer_list_repository.dart'
    as _i391;
import 'package:theadvance/src/domain/repositories/customer_profile_repository.dart'
    as _i412;
import 'package:theadvance/src/domain/repositories/customer_record_repository.dart'
    as _i331;
import 'package:theadvance/src/domain/repositories/customer_repository.dart'
    as _i293;
import 'package:theadvance/src/domain/repositories/customer_schedule_repository.dart'
    as _i524;
import 'package:theadvance/src/domain/repositories/detail_crm_customer_repository.dart'
    as _i378;
import 'package:theadvance/src/domain/repositories/detail_staff_evaluation_period_repository.dart'
    as _i205;
import 'package:theadvance/src/domain/repositories/dev_repository.dart'
    as _i357;
import 'package:theadvance/src/domain/repositories/eform_repository.dart'
    as _i496;
import 'package:theadvance/src/domain/repositories/feedback_repository.dart'
    as _i272;
import 'package:theadvance/src/domain/repositories/food_repository.dart'
    as _i197;
import 'package:theadvance/src/domain/repositories/group_chat_detail_repository.dart'
    as _i172;
import 'package:theadvance/src/domain/repositories/helper_repository.dart'
    as _i130;
import 'package:theadvance/src/domain/repositories/home_repository.dart'
    as _i463;
import 'package:theadvance/src/domain/repositories/hr_organization_repository.dart'
    as _i575;
import 'package:theadvance/src/domain/repositories/important_notes_repository.dart'
    as _i207;
import 'package:theadvance/src/domain/repositories/kpi_employee_repository.dart'
    as _i526;
import 'package:theadvance/src/domain/repositories/like_list_repository.dart'
    as _i195;
import 'package:theadvance/src/domain/repositories/list_customer_repository.dart'
    as _i434;
import 'package:theadvance/src/domain/repositories/location_google_repository.dart'
    as _i359;
import 'package:theadvance/src/domain/repositories/media_upload_repository.dart'
    as _i387;
import 'package:theadvance/src/domain/repositories/medical_department_list_repository.dart'
    as _i203;
import 'package:theadvance/src/domain/repositories/medical_log_detail_repository.dart'
    as _i517;
import 'package:theadvance/src/domain/repositories/medical_product_create_repository.dart'
    as _i461;
import 'package:theadvance/src/domain/repositories/medical_service_create_repository.dart'
    as _i707;
import 'package:theadvance/src/domain/repositories/medical_service_list_repository.dart'
    as _i373;
import 'package:theadvance/src/domain/repositories/medical_service_log_list_repository.dart'
    as _i661;
import 'package:theadvance/src/domain/repositories/medical_template_list_repository.dart'
    as _i683;
import 'package:theadvance/src/domain/repositories/medicine_detail_repository.dart'
    as _i211;
import 'package:theadvance/src/domain/repositories/news_repository.dart'
    as _i400;
import 'package:theadvance/src/domain/repositories/note_details_repository.dart'
    as _i424;
import 'package:theadvance/src/domain/repositories/notification_list_repository.dart'
    as _i90;
import 'package:theadvance/src/domain/repositories/notification_repository.dart'
    as _i494;
import 'package:theadvance/src/domain/repositories/product_confirm_repository.dart'
    as _i375;
import 'package:theadvance/src/domain/repositories/px_list_repository.dart'
    as _i353;
import 'package:theadvance/src/domain/repositories/px_recheck_repository.dart'
    as _i393;
import 'package:theadvance/src/domain/repositories/px_task_list_repository.dart'
    as _i255;
import 'package:theadvance/src/domain/repositories/px_unasigned_repository.dart'
    as _i451;
import 'package:theadvance/src/domain/repositories/px_unasigned_update_repository.dart'
    as _i558;
import 'package:theadvance/src/domain/repositories/rating_human_repository.dart'
    as _i259;
import 'package:theadvance/src/domain/repositories/request_repository.dart'
    as _i697;
import 'package:theadvance/src/domain/repositories/schedule_details_repository.dart'
    as _i437;
import 'package:theadvance/src/domain/repositories/select_px_room_repository.dart'
    as _i193;
import 'package:theadvance/src/domain/repositories/service_and_product_repository.dart'
    as _i508;
import 'package:theadvance/src/domain/repositories/setting_repository.dart'
    as _i142;
import 'package:theadvance/src/domain/repositories/staff_evaluation_periods_repository.dart'
    as _i449;
import 'package:theadvance/src/domain/repositories/staff_repository.dart'
    as _i455;
import 'package:theadvance/src/domain/repositories/sticker_social_repository.dart'
    as _i185;
import 'package:theadvance/src/domain/repositories/story_detail_repository.dart'
    as _i124;
import 'package:theadvance/src/domain/repositories/story_list_repository.dart'
    as _i170;
import 'package:theadvance/src/domain/repositories/story_person_list_repository.dart'
    as _i168;
import 'package:theadvance/src/domain/repositories/tag_image_repository.dart'
    as _i709;
import 'package:theadvance/src/domain/repositories/tag_list_repository.dart'
    as _i152;
import 'package:theadvance/src/domain/repositories/taking_care_customer_repository.dart'
    as _i381;
import 'package:theadvance/src/domain/repositories/task_repository.dart'
    as _i355;
import 'package:theadvance/src/domain/repositories/ticket_active_repository.dart'
    as _i201;
import 'package:theadvance/src/domain/repositories/ticket_detail_repository.dart'
    as _i395;
import 'package:theadvance/src/domain/repositories/ticketv2_repository.dart'
    as _i257;
import 'package:theadvance/src/domain/repositories/tracking_repository.dart'
    as _i67;
import 'package:theadvance/src/domain/repositories/user_list_repository.dart'
    as _i560;
import 'package:theadvance/src/domain/repositories/user_repository.dart'
    as _i267;
import 'package:theadvance/src/domain/repositories/user_ticker_repository.dart'
    as _i209;
import 'package:theadvance/src/domain/usecases/add_tags_image/create_image_tag_add_tags_image_usecase.dart'
    as _i628;
import 'package:theadvance/src/domain/usecases/add_tags_image/create_merge_image_add_tags_image_usecase.dart'
    as _i629;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_add_tags_image_usecase.dart'
    as _i635;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_image_list_add_tags_image_usecase.dart'
    as _i636;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_room_list_add_tags_image_usecase.dart'
    as _i627;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_saved_add_tags_image_usecase.dart'
    as _i631;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_tag_list_add_tags_image_usecase.dart'
    as _i633;
import 'package:theadvance/src/domain/usecases/add_tags_image/remove_add_tags_image_usecase.dart'
    as _i632;
import 'package:theadvance/src/domain/usecases/add_tags_image/save_add_tags_image_usecase.dart'
    as _i630;
import 'package:theadvance/src/domain/usecases/add_tags_image/user_search_add_tags_image_usecase.dart'
    as _i634;
import 'package:theadvance/src/domain/usecases/assign_task/create_assign_task_usecase.dart'
    as _i970;
import 'package:theadvance/src/domain/usecases/assign_task/delete_assign_task_usecase.dart'
    as _i967;
import 'package:theadvance/src/domain/usecases/assign_task/get_assign_task_usecase.dart'
    as _i972;
import 'package:theadvance/src/domain/usecases/assign_task/get_saved_assign_task_usecase.dart'
    as _i969;
import 'package:theadvance/src/domain/usecases/assign_task/get_staff_assign_task_usecase.dart'
    as _i974;
import 'package:theadvance/src/domain/usecases/assign_task/remove_assign_task_usecase.dart'
    as _i973;
import 'package:theadvance/src/domain/usecases/assign_task/save_assign_task_usecase.dart'
    as _i971;
import 'package:theadvance/src/domain/usecases/assign_task/update_assign_task_usecase.dart'
    as _i968;
import 'package:theadvance/src/domain/usecases/branch_chat_list/get_branch_chat_list_usecase.dart'
    as _i693;
import 'package:theadvance/src/domain/usecases/branch_chat_list/get_saved_branch_chat_list_usecase.dart'
    as _i694;
import 'package:theadvance/src/domain/usecases/branch_chat_list/remove_branch_chat_list_usecase.dart'
    as _i695;
import 'package:theadvance/src/domain/usecases/branch_chat_list/save_branch_chat_list_usecase.dart'
    as _i696;
import 'package:theadvance/src/domain/usecases/branch_selection/bed_change_branch_selection_usecase.dart'
    as _i643;
import 'package:theadvance/src/domain/usecases/branch_selection/bed_select_branch_selection_usecase.dart'
    as _i641;
import 'package:theadvance/src/domain/usecases/branch_selection/employee_get_branch_selection_usecase.dart'
    as _i638;
import 'package:theadvance/src/domain/usecases/branch_selection/estimate_time_get_branch_selection_usecase.dart'
    as _i644;
import 'package:theadvance/src/domain/usecases/branch_selection/get_bed_branch_selection_usecase.dart'
    as _i646;
import 'package:theadvance/src/domain/usecases/branch_selection/get_branch_selection_usecase.dart'
    as _i647;
import 'package:theadvance/src/domain/usecases/branch_selection/get_floor_branch_selection_usecase.dart'
    as _i642;
import 'package:theadvance/src/domain/usecases/branch_selection/get_room_branch_selection_usecase.dart'
    as _i648;
import 'package:theadvance/src/domain/usecases/branch_selection/get_saved_branch_selection_usecase.dart'
    as _i645;
import 'package:theadvance/src/domain/usecases/branch_selection/remove_branch_selection_usecase.dart'
    as _i639;
import 'package:theadvance/src/domain/usecases/branch_selection/save_branch_selection_usecase.dart'
    as _i640;
import 'package:theadvance/src/domain/usecases/chat/conversation_details_update_chat_usecase.dart'
    as _i234;
import 'package:theadvance/src/domain/usecases/chat/get_chat_usecase.dart'
    as _i231;
import 'package:theadvance/src/domain/usecases/chat/get_conversation_by_id_usecase.dart'
    as _i227;
import 'package:theadvance/src/domain/usecases/chat/get_conversation_chat_usecase.dart'
    as _i218;
import 'package:theadvance/src/domain/usecases/chat/get_pin_list_chat_usecase.dart'
    as _i219;
import 'package:theadvance/src/domain/usecases/chat/get_saved_chat_usecase.dart'
    as _i213;
import 'package:theadvance/src/domain/usecases/chat/get_user_seen_chat_usecase.dart'
    as _i215;
import 'package:theadvance/src/domain/usecases/chat/get_user_sticker_chat_usecase.dart'
    as _i222;
import 'package:theadvance/src/domain/usecases/chat/message_edit_chat_usecase.dart'
    as _i214;
import 'package:theadvance/src/domain/usecases/chat/message_remove_chat_usecase.dart'
    as _i232;
import 'package:theadvance/src/domain/usecases/chat/pin_message_chat_usecase.dart'
    as _i229;
import 'package:theadvance/src/domain/usecases/chat/react_chat_usecase.dart'
    as _i226;
import 'package:theadvance/src/domain/usecases/chat/remove_chat_usecase.dart'
    as _i233;
import 'package:theadvance/src/domain/usecases/chat/reply_bot_message_chat_usecase.dart'
    as _i225;
import 'package:theadvance/src/domain/usecases/chat/save_chat_usecase.dart'
    as _i220;
import 'package:theadvance/src/domain/usecases/chat/search_chat_usecase.dart'
    as _i217;
import 'package:theadvance/src/domain/usecases/chat/send_chat_usecase.dart'
    as _i228;
import 'package:theadvance/src/domain/usecases/chat/transcribe_chat_usecase.dart'
    as _i216;
import 'package:theadvance/src/domain/usecases/chat/unpin_message_chat_usecase.dart'
    as _i224;
import 'package:theadvance/src/domain/usecases/chat/update_poll_chat_usecase.dart'
    as _i230;
import 'package:theadvance/src/domain/usecases/chat/upload_file_chat_usecase.dart'
    as _i223;
import 'package:theadvance/src/domain/usecases/chat/vote_poll_chat_usecase.dart'
    as _i221;
import 'package:theadvance/src/domain/usecases/chat_list/get_chat_list_usecase.dart'
    as _i164;
import 'package:theadvance/src/domain/usecases/chat_list/get_conversation_by_invite_id_chat_list_usecase.dart'
    as _i162;
import 'package:theadvance/src/domain/usecases/chat_list/get_recent_contacts_chat_list_usecase.dart'
    as _i163;
import 'package:theadvance/src/domain/usecases/chat_list/get_saved_chat_list_usecase.dart'
    as _i157;
import 'package:theadvance/src/domain/usecases/chat_list/get_total_unread_chat_list_usecase.dart'
    as _i156;
import 'package:theadvance/src/domain/usecases/chat_list/join_group_chat_list_usecase.dart'
    as _i155;
import 'package:theadvance/src/domain/usecases/chat_list/mark_as_read_chat_list_usecase.dart'
    as _i160;
import 'package:theadvance/src/domain/usecases/chat_list/pin_conversation_chat_list_usecase.dart'
    as _i158;
import 'package:theadvance/src/domain/usecases/chat_list/remove_chat_list_usecase.dart'
    as _i166;
import 'package:theadvance/src/domain/usecases/chat_list/save_chat_list_usecase.dart'
    as _i167;
import 'package:theadvance/src/domain/usecases/chat_list/search_chat_list_usecase.dart'
    as _i159;
import 'package:theadvance/src/domain/usecases/chat_list/search_message_chat_list_usecase.dart'
    as _i161;
import 'package:theadvance/src/domain/usecases/chat_list/sort_folder_chat_list_usecase.dart'
    as _i154;
import 'package:theadvance/src/domain/usecases/chat_list/update_pin_conversation_chat_list_usecase.dart'
    as _i165;
import 'package:theadvance/src/domain/usecases/chat_select_branch/get_chat_select_branch_usecase.dart'
    as _i487;
import 'package:theadvance/src/domain/usecases/chat_select_branch/get_saved_chat_select_branch_usecase.dart'
    as _i484;
import 'package:theadvance/src/domain/usecases/chat_select_branch/remove_chat_select_branch_usecase.dart'
    as _i485;
import 'package:theadvance/src/domain/usecases/chat_select_branch/save_chat_select_branch_usecase.dart'
    as _i486;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkin_hour_get_usecase.dart'
    as _i804;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkin_hour_save_usecase.dart'
    as _i802;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkout_hour_get_usecase.dart'
    as _i803;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkout_hour_save_usecase.dart'
    as _i801;
import 'package:theadvance/src/domain/usecases/checkin/get_branches_usecase.dart'
    as _i863;
import 'package:theadvance/src/domain/usecases/checkin/get_checkin_types_usecase.dart'
    as _i864;
import 'package:theadvance/src/domain/usecases/checkin/get_choices_usecase.dart'
    as _i861;
import 'package:theadvance/src/domain/usecases/checkin/get_monthly_history_checkin_usecase.dart'
    as _i865;
import 'package:theadvance/src/domain/usecases/checkin/request_update_history_checkin_usecase.dart'
    as _i862;
import 'package:theadvance/src/domain/usecases/checkin_photo/get_checkin_photo_usecase.dart'
    as _i686;
import 'package:theadvance/src/domain/usecases/checkin_photo/get_saved_checkin_photo_usecase.dart'
    as _i687;
import 'package:theadvance/src/domain/usecases/checkin_photo/remove_checkin_photo_usecase.dart'
    as _i689;
import 'package:theadvance/src/domain/usecases/checkin_photo/save_checkin_photo_usecase.dart'
    as _i688;
import 'package:theadvance/src/domain/usecases/comment/comment_upload_file_usecase.dart'
    as _i856;
import 'package:theadvance/src/domain/usecases/comment/delete_comment_usecase.dart'
    as _i855;
import 'package:theadvance/src/domain/usecases/comment/get_comment_list_usecase.dart'
    as _i853;
import 'package:theadvance/src/domain/usecases/comment/post_comment_usecase.dart'
    as _i854;
import 'package:theadvance/src/domain/usecases/comment/update_comment_usecase.dart'
    as _i685;
import 'package:theadvance/src/domain/usecases/consultation_customer/complete_consultation_customer_usecase.dart'
    as _i319;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_combo_usecase.dart'
    as _i327;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_treatment_detail_usecase.dart'
    as _i303;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_treatment_om_detail_usecase.dart'
    as _i295;
import 'package:theadvance/src/domain/usecases/consultation_customer/delete_result_of_fit_usecase.dart'
    as _i296;
import 'package:theadvance/src/domain/usecases/consultation_customer/edit_service_consultation_customer_usecase.dart'
    as _i320;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_action_consultation_customer_usecase.dart'
    as _i316;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_combo_service_usecase.dart'
    as _i324;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_customer_usecase.dart'
    as _i312;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_ndtv_usecase.dart'
    as _i298;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_ttbd_usecase.dart'
    as _i318;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_deal_service_usecase.dart'
    as _i325;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_fit_customer_info_usecase.dart'
    as _i304;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_result_list_of_fit_usecase.dart'
    as _i299;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_result_of_fit_usecase.dart'
    as _i309;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_saved_consultation_customer_usecase.dart'
    as _i310;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_consultation_customer_usecase.dart'
    as _i307;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_from_acc_usecase.dart'
    as _i328;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_inside_ticket_usecase.dart'
    as _i297;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_om_consultation_customer_usecase.dart'
    as _i308;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_type_purchase_detail_usecase.dart'
    as _i329;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_usage_consultation_customer_usecase.dart'
    as _i306;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_skin_customer_info_usecase.dart'
    as _i300;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_treatment_detail_usecase.dart'
    as _i323;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_treatment_note_usecase.dart'
    as _i305;
import 'package:theadvance/src/domain/usecases/consultation_customer/product_load_consultation_customer_usecase.dart'
    as _i302;
import 'package:theadvance/src/domain/usecases/consultation_customer/remove_consultation_customer_usecase.dart'
    as _i301;
import 'package:theadvance/src/domain/usecases/consultation_customer/remove_service_consultation_customer_usecase.dart'
    as _i322;
import 'package:theadvance/src/domain/usecases/consultation_customer/save_consultation_customer_usecase.dart'
    as _i314;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_combo_usecase.dart'
    as _i326;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_fit_customer_info_usecase.dart'
    as _i313;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_result_of_fit_usecase.dart'
    as _i317;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_skin_customer_info_usecase.dart'
    as _i311;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_treatment_detail_usecase.dart'
    as _i321;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_treatment_note_usecase.dart'
    as _i315;
import 'package:theadvance/src/domain/usecases/consultation_manager/assign_update_usecase.dart'
    as _i930;
import 'package:theadvance/src/domain/usecases/consultation_manager/bed_assign_consultation_manager_usecase.dart'
    as _i931;
import 'package:theadvance/src/domain/usecases/consultation_manager/bed_fetch_consultation_manager_usecase.dart'
    as _i933;
import 'package:theadvance/src/domain/usecases/consultation_manager/delete_service_assign_manager_usecase.dart'
    as _i929;
import 'package:theadvance/src/domain/usecases/consultation_manager/delete_service_customer_usecase.dart'
    as _i934;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_consultation_manager_usecase.dart'
    as _i926;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_customer_consultation_manager_usecase.dart'
    as _i935;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_saved_consultation_manager_usecase.dart'
    as _i932;
import 'package:theadvance/src/domain/usecases/consultation_manager/list_fetch_by_staff_consultation_manager_usecase.dart'
    as _i924;
import 'package:theadvance/src/domain/usecases/consultation_manager/remove_consultation_manager_usecase.dart'
    as _i927;
import 'package:theadvance/src/domain/usecases/consultation_manager/room_fetch_consultation_manager_usecase.dart'
    as _i925;
import 'package:theadvance/src/domain/usecases/consultation_manager/save_consultation_manager_usecase.dart'
    as _i928;
import 'package:theadvance/src/domain/usecases/create_chat_folder/conversation_load_create_chat_folder_usecase.dart'
    as _i338;
import 'package:theadvance/src/domain/usecases/create_chat_folder/get_create_chat_folder_usecase.dart'
    as _i336;
import 'package:theadvance/src/domain/usecases/create_chat_folder/get_saved_create_chat_folder_usecase.dart'
    as _i334;
import 'package:theadvance/src/domain/usecases/create_chat_folder/load_create_chat_folder_usecase.dart'
    as _i335;
import 'package:theadvance/src/domain/usecases/create_chat_folder/remove_create_chat_folder_usecase.dart'
    as _i333;
import 'package:theadvance/src/domain/usecases/create_chat_folder/remove_folder_create_chat_folder_usecase.dart'
    as _i340;
import 'package:theadvance/src/domain/usecases/create_chat_folder/save_create_chat_folder_usecase.dart'
    as _i337;
import 'package:theadvance/src/domain/usecases/create_chat_folder/update_create_chat_folder_usecase.dart'
    as _i339;
import 'package:theadvance/src/domain/usecases/create_chat_group/get_create_chat_group_usecase.dart'
    as _i183;
import 'package:theadvance/src/domain/usecases/create_chat_group/get_saved_create_chat_group_usecase.dart'
    as _i180;
import 'package:theadvance/src/domain/usecases/create_chat_group/remove_create_chat_group_usecase.dart'
    as _i184;
import 'package:theadvance/src/domain/usecases/create_chat_group/save_create_chat_group_usecase.dart'
    as _i181;
import 'package:theadvance/src/domain/usecases/create_chat_group/user_load_create_chat_group_usecase.dart'
    as _i182;
import 'package:theadvance/src/domain/usecases/create_customer/customer_search_create_customer_usecase.dart'
    as _i591;
import 'package:theadvance/src/domain/usecases/create_customer/get_create_customer_usecase.dart'
    as _i587;
import 'package:theadvance/src/domain/usecases/create_customer/get_district_create_customer_usecase.dart'
    as _i585;
import 'package:theadvance/src/domain/usecases/create_customer/get_job_create_customer_usecase.dart'
    as _i582;
import 'package:theadvance/src/domain/usecases/create_customer/get_province_create_customer_usecase.dart'
    as _i581;
import 'package:theadvance/src/domain/usecases/create_customer/get_saved_create_customer_usecase.dart'
    as _i588;
import 'package:theadvance/src/domain/usecases/create_customer/get_ward_create_customer_usecase.dart'
    as _i586;
import 'package:theadvance/src/domain/usecases/create_customer/remove_create_customer_usecase.dart'
    as _i583;
import 'package:theadvance/src/domain/usecases/create_customer/save_create_customer_usecase.dart'
    as _i584;
import 'package:theadvance/src/domain/usecases/create_customer/survey_load_create_customer_usecase.dart'
    as _i590;
import 'package:theadvance/src/domain/usecases/create_customer/update_create_customer_usecase.dart'
    as _i589;
import 'package:theadvance/src/domain/usecases/customer/checkin_customer_usecase.dart'
    as _i837;
import 'package:theadvance/src/domain/usecases/customer/get_customer_info_by_qr_usecase.dart'
    as _i514;
import 'package:theadvance/src/domain/usecases/customer/get_customer_info_usecase.dart'
    as _i515;
import 'package:theadvance/src/domain/usecases/customer/get_customer_room_code_usecase.dart'
    as _i516;
import 'package:theadvance/src/domain/usecases/customer/get_room_list_customer_usecase.dart'
    as _i838;
import 'package:theadvance/src/domain/usecases/customer/print_customer_usecase.dart'
    as _i840;
import 'package:theadvance/src/domain/usecases/customer/save_customer_room_code_usecase.dart'
    as _i839;
import 'package:theadvance/src/domain/usecases/customer_booking_info/booked_services_fetch_customer_booking_info_usecase.dart'
    as _i668;
import 'package:theadvance/src/domain/usecases/customer_booking_info/get_customer_booking_info_usecase.dart'
    as _i667;
import 'package:theadvance/src/domain/usecases/customer_booking_info/get_saved_customer_booking_info_usecase.dart'
    as _i669;
import 'package:theadvance/src/domain/usecases/customer_booking_info/remove_customer_booking_info_usecase.dart'
    as _i664;
import 'package:theadvance/src/domain/usecases/customer_booking_info/save_customer_booking_info_usecase.dart'
    as _i666;
import 'package:theadvance/src/domain/usecases/customer_booking_info/service_details_load_customer_booking_info_usecase.dart'
    as _i665;
import 'package:theadvance/src/domain/usecases/customer_booking_info/suggest_services_fetch_customer_booking_info_usecase.dart'
    as _i663;
import 'package:theadvance/src/domain/usecases/customer_booking_info/used_service_fetch_customer_booking_info_usecase.dart'
    as _i670;
import 'package:theadvance/src/domain/usecases/customer_info_details/checkout_customer_info_details_usecase.dart'
    as _i703;
import 'package:theadvance/src/domain/usecases/customer_info_details/get_customer_info_details_usecase.dart'
    as _i702;
import 'package:theadvance/src/domain/usecases/customer_info_details/get_saved_customer_info_details_usecase.dart'
    as _i704;
import 'package:theadvance/src/domain/usecases/customer_info_details/remove_customer_info_details_usecase.dart'
    as _i705;
import 'package:theadvance/src/domain/usecases/customer_info_details/save_customer_info_details_usecase.dart'
    as _i701;
import 'package:theadvance/src/domain/usecases/customer_list/get_customer_list_usecase.dart'
    as _i760;
import 'package:theadvance/src/domain/usecases/customer_list/get_customer_relationship_list_usecase.dart'
    as _i763;
import 'package:theadvance/src/domain/usecases/customer_list/get_saved_customer_list_usecase.dart'
    as _i761;
import 'package:theadvance/src/domain/usecases/customer_list/remove_customer_list_usecase.dart'
    as _i762;
import 'package:theadvance/src/domain/usecases/customer_list/save_customer_list_usecase.dart'
    as _i759;
import 'package:theadvance/src/domain/usecases/customer_profile/create_consultation_customer_profile_usecase.dart'
    as _i713;
import 'package:theadvance/src/domain/usecases/customer_profile/get_consultation_history_customer_profile_usecase.dart'
    as _i716;
import 'package:theadvance/src/domain/usecases/customer_profile/get_customer_profile_usecase.dart'
    as _i717;
import 'package:theadvance/src/domain/usecases/customer_profile/get_saved_customer_profile_usecase.dart'
    as _i718;
import 'package:theadvance/src/domain/usecases/customer_profile/remove_customer_profile_usecase.dart'
    as _i715;
import 'package:theadvance/src/domain/usecases/customer_profile/save_customer_profile_usecase.dart'
    as _i714;
import 'package:theadvance/src/domain/usecases/customer_profile/update_consultation_customer_profile_usecase.dart'
    as _i719;
import 'package:theadvance/src/domain/usecases/customer_record/get_customer_record_usecase.dart'
    as _i459;
import 'package:theadvance/src/domain/usecases/customer_record/get_saved_customer_record_usecase.dart'
    as _i458;
import 'package:theadvance/src/domain/usecases/customer_record/remove_customer_record_usecase.dart'
    as _i457;
import 'package:theadvance/src/domain/usecases/customer_record/save_customer_record_usecase.dart'
    as _i460;
import 'package:theadvance/src/domain/usecases/customer_schedule/get_customer_schedule_usecase.dart'
    as _i735;
import 'package:theadvance/src/domain/usecases/customer_schedule/get_saved_customer_schedule_usecase.dart'
    as _i733;
import 'package:theadvance/src/domain/usecases/customer_schedule/remove_customer_schedule_usecase.dart'
    as _i734;
import 'package:theadvance/src/domain/usecases/customer_schedule/save_customer_schedule_usecase.dart'
    as _i732;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_fetch_detail_crm_customer_usecase.dart'
    as _i541;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_type_fetch_detail_crm_customer_usecase.dart'
    as _i553;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_update_detail_crm_customer_usecase.dart'
    as _i552;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/book_detail_crm_customer_usecase.dart'
    as _i550;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_detail_load_detail_crm_customer_usecase.dart'
    as _i554;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_load_detail_crm_customer_usecase.dart'
    as _i557;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_log_fetch_detail_crm_customer_usecase.dart'
    as _i549;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/branch_load_detail_crm_customer_usecase.dart'
    as _i547;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/call_log_fetch_detail_crm_customer_usecase.dart'
    as _i546;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/get_detail_crm_customer_usecase.dart'
    as _i542;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/get_saved_detail_crm_customer_usecase.dart'
    as _i548;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/message_log_fetch_detail_crm_customer_usecase.dart'
    as _i555;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/number_booking_load_detail_crm_customer_usecase.dart'
    as _i556;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/promotion_load_detail_crm_customer_usecase.dart'
    as _i539;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/remove_detail_crm_customer_usecase.dart'
    as _i545;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/room_load_detail_crm_customer_usecase.dart'
    as _i538;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/save_detail_crm_customer_usecase.dart'
    as _i543;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/service_fetch_detail_crm_customer_usecase.dart'
    as _i540;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/service_load_detail_crm_customer_usecase.dart'
    as _i544;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/time_load_detail_crm_customer_usecase.dart'
    as _i551;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/employee_fetch_detail_staff_evaluation_period_usecase.dart'
    as _i489;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/get_detail_staff_evaluation_period_usecase.dart'
    as _i492;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/get_saved_detail_staff_evaluation_period_usecase.dart'
    as _i491;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/remove_detail_staff_evaluation_period_usecase.dart'
    as _i490;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/save_detail_staff_evaluation_period_usecase.dart'
    as _i488;
import 'package:theadvance/src/domain/usecases/dev/get_dev_usecase.dart'
    as _i737;
import 'package:theadvance/src/domain/usecases/dev/get_saved_dev_usecase.dart'
    as _i736;
import 'package:theadvance/src/domain/usecases/dev/mini_app_dev_usecase.dart'
    as _i739;
import 'package:theadvance/src/domain/usecases/dev/remove_dev_usecase.dart'
    as _i740;
import 'package:theadvance/src/domain/usecases/dev/save_dev_usecase.dart'
    as _i738;
import 'package:theadvance/src/domain/usecases/eform/approving_eform_usecase.dart'
    as _i610;
import 'package:theadvance/src/domain/usecases/eform/approving_otp_eform_usecase.dart'
    as _i607;
import 'package:theadvance/src/domain/usecases/eform/approving_signal_eform_usecase.dart'
    as _i606;
import 'package:theadvance/src/domain/usecases/eform/create_eform_usecase.dart'
    as _i605;
import 'package:theadvance/src/domain/usecases/eform/get_detail_eform_usecase.dart'
    as _i611;
import 'package:theadvance/src/domain/usecases/eform/get_eform_request_type.dart'
    as _i608;
import 'package:theadvance/src/domain/usecases/eform/get_eform_usecase.dart'
    as _i612;
import 'package:theadvance/src/domain/usecases/eform/reject_eform_usecase.dart'
    as _i609;
import 'package:theadvance/src/domain/usecases/feedback/get_feedback_usecase.dart'
    as _i748;
import 'package:theadvance/src/domain/usecases/feedback/get_saved_feedback_usecase.dart'
    as _i749;
import 'package:theadvance/src/domain/usecases/feedback/remove_feedback_usecase.dart'
    as _i750;
import 'package:theadvance/src/domain/usecases/feedback/save_feedback_usecase.dart'
    as _i747;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_get_usecase.dart'
    as _i245;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_remove_usecase.dart'
    as _i235;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_save_usecase.dart'
    as _i236;
import 'package:theadvance/src/domain/usecases/food/order_food_create_report_usecase.dart'
    as _i536;
import 'package:theadvance/src/domain/usecases/food/order_food_created_usecase.dart'
    as _i535;
import 'package:theadvance/src/domain/usecases/food/order_food_delete_usecase.dart'
    as _i533;
import 'package:theadvance/src/domain/usecases/food/order_food_get_usecase.dart'
    as _i534;
import 'package:theadvance/src/domain/usecases/food/order_food_upload_usecase.dart'
    as _i330;
import 'package:theadvance/src/domain/usecases/food/set_default_address_food_usecase.dart'
    as _i537;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_get_usecase.dart'
    as _i793;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_remove_usecase.dart'
    as _i792;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_save_usecase.dart'
    as _i794;
import 'package:theadvance/src/domain/usecases/group_chat_detail/avatar_upload_group_chat_detail_usecase.dart'
    as _i277;
import 'package:theadvance/src/domain/usecases/group_chat_detail/change_owner_group_chat_detail_usecase.dart'
    as _i284;
import 'package:theadvance/src/domain/usecases/group_chat_detail/delete_group_usecase.dart'
    as _i280;
import 'package:theadvance/src/domain/usecases/group_chat_detail/file_load_group_chat_detail_usecase.dart'
    as _i288;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_group_chat_detail_usecase.dart'
    as _i283;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_rule_by_role_group_chat_detail_usecase.dart'
    as _i289;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_saved_group_chat_detail_usecase.dart'
    as _i291;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_user_exception_group_chat_detail_usecase.dart'
    as _i276;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_user_rules_group_chat_detail_usecase.dart'
    as _i287;
import 'package:theadvance/src/domain/usecases/group_chat_detail/link_load_group_chat_detail_usecase.dart'
    as _i285;
import 'package:theadvance/src/domain/usecases/group_chat_detail/media_load_group_chat_detail_usecase.dart'
    as _i281;
import 'package:theadvance/src/domain/usecases/group_chat_detail/member_info_load_group_chat_detail_usecase.dart'
    as _i279;
import 'package:theadvance/src/domain/usecases/group_chat_detail/remove_group_chat_detail_usecase.dart'
    as _i278;
import 'package:theadvance/src/domain/usecases/group_chat_detail/save_group_chat_detail_usecase.dart'
    as _i292;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_admin_rule_group_chat_detail_usecase.dart'
    as _i286;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_group_chat_detail_usecase.dart'
    as _i290;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_member_rule_group_chat_detail_usecase.dart'
    as _i282;
import 'package:theadvance/src/domain/usecases/helper/delete_latitude_usecase.dart'
    as _i240;
import 'package:theadvance/src/domain/usecases/helper/delete_longitude_usecase.dart'
    as _i239;
import 'package:theadvance/src/domain/usecases/helper/get_access_token_usecase.dart'
    as _i249;
import 'package:theadvance/src/domain/usecases/helper/get_app_version_usecase.dart'
    as _i247;
import 'package:theadvance/src/domain/usecases/helper/get_device_info_usecase.dart'
    as _i252;
import 'package:theadvance/src/domain/usecases/helper/get_firebase_token_usecase.dart'
    as _i250;
import 'package:theadvance/src/domain/usecases/helper/get_key_appsflyer_usecase.dart'
    as _i241;
import 'package:theadvance/src/domain/usecases/helper/get_latitude_usecase.dart'
    as _i248;
import 'package:theadvance/src/domain/usecases/helper/get_longitude_usecase.dart'
    as _i251;
import 'package:theadvance/src/domain/usecases/helper/get_phone_usecase.dart'
    as _i246;
import 'package:theadvance/src/domain/usecases/helper/save_app_version_usecase.dart'
    as _i242;
import 'package:theadvance/src/domain/usecases/helper/save_device_info_usecase.dart'
    as _i237;
import 'package:theadvance/src/domain/usecases/helper/save_key_appflyer_usecase.dart'
    as _i244;
import 'package:theadvance/src/domain/usecases/helper/save_latitude_usecase.dart'
    as _i238;
import 'package:theadvance/src/domain/usecases/helper/save_longitude_usecase.dart'
    as _i243;
import 'package:theadvance/src/domain/usecases/history_checkin/fetch_history_checkin_usecase.dart'
    as _i493;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i835;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i562;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i563;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i836;
import 'package:theadvance/src/domain/usecases/hr_organization/get_hr_organization_usecase.dart'
    as _i624;
import 'package:theadvance/src/domain/usecases/hr_organization/get_saved_hr_organization_usecase.dart'
    as _i625;
import 'package:theadvance/src/domain/usecases/hr_organization/remove_hr_organization_usecase.dart'
    as _i626;
import 'package:theadvance/src/domain/usecases/hr_organization/save_hr_organization_usecase.dart'
    as _i623;
import 'package:theadvance/src/domain/usecases/important_notes/get_important_notes_usecase.dart'
    as _i506;
import 'package:theadvance/src/domain/usecases/important_notes/get_note_category_important_notes_usecase.dart'
    as _i505;
import 'package:theadvance/src/domain/usecases/important_notes/get_saved_important_notes_usecase.dart'
    as _i503;
import 'package:theadvance/src/domain/usecases/important_notes/remove_important_notes_usecase.dart'
    as _i507;
import 'package:theadvance/src/domain/usecases/important_notes/save_important_notes_usecase.dart'
    as _i504;
import 'package:theadvance/src/domain/usecases/kpi_employee/get_kpi_employee_detail_usecase.dart'
    as _i711;
import 'package:theadvance/src/domain/usecases/kpi_employee/get_kpi_employee_usecase.dart'
    as _i712;
import 'package:theadvance/src/domain/usecases/like_list/get_like_list_usecase.dart'
    as _i399;
import 'package:theadvance/src/domain/usecases/like_list/post_like_comment_usecase.dart'
    as _i398;
import 'package:theadvance/src/domain/usecases/like_list/post_like_story_usecase.dart'
    as _i397;
import 'package:theadvance/src/domain/usecases/list_customer/get_list_customer_usecase.dart'
    as _i876;
import 'package:theadvance/src/domain/usecases/list_customer/get_saved_list_customer_usecase.dart'
    as _i875;
import 'package:theadvance/src/domain/usecases/list_customer/get_status_list_customer_usecase.dart'
    as _i874;
import 'package:theadvance/src/domain/usecases/list_customer/remove_list_customer_usecase.dart'
    as _i878;
import 'package:theadvance/src/domain/usecases/list_customer/save_list_customer_usecase.dart'
    as _i877;
import 'package:theadvance/src/domain/usecases/list_customer/search_list_customer_usecase.dart'
    as _i879;
import 'package:theadvance/src/domain/usecases/location_google/get_address_current_usecase.dart'
    as _i621;
import 'package:theadvance/src/domain/usecases/location_google/get_address_near_usecase.dart'
    as _i619;
import 'package:theadvance/src/domain/usecases/location_google/get_address_search_usecase.dart'
    as _i622;
import 'package:theadvance/src/domain/usecases/location_google/get_location_google_usecase.dart'
    as _i620;
import 'package:theadvance/src/domain/usecases/login/socket_access_token_get_login_usecase.dart'
    as _i598;
import 'package:theadvance/src/domain/usecases/media/upload_avatar_usecase.dart'
    as _i673;
import 'package:theadvance/src/domain/usecases/media/upload_background_usecase.dart'
    as _i674;
import 'package:theadvance/src/domain/usecases/media/upload_checkin_image_usecase.dart'
    as _i672;
import 'package:theadvance/src/domain/usecases/media/upload_feedback_usecase.dart'
    as _i671;
import 'package:theadvance/src/domain/usecases/media/upload_kyc_usecase.dart'
    as _i675;
import 'package:theadvance/src/domain/usecases/medical_department_list/get_medical_department_list_usecase.dart'
    as _i384;
import 'package:theadvance/src/domain/usecases/medical_department_list/get_saved_medical_department_list_usecase.dart'
    as _i383;
import 'package:theadvance/src/domain/usecases/medical_department_list/remove_medical_department_list_usecase.dart'
    as _i386;
import 'package:theadvance/src/domain/usecases/medical_department_list/save_medical_department_list_usecase.dart'
    as _i385;
import 'package:theadvance/src/domain/usecases/medical_log_detail/create_log_medical_detail_usecase.dart'
    as _i896;
import 'package:theadvance/src/domain/usecases/medical_log_detail/doctor_list_get_medical_log_detail_usecase.dart'
    as _i886;
import 'package:theadvance/src/domain/usecases/medical_log_detail/dosage_list_get_medical_log_detail_usecase.dart'
    as _i888;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_medical_log_detail_usecase.dart'
    as _i898;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_origin_status_medical_log_detail_usecase.dart'
    as _i894;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_post_sai_medical_log_detail_usecase.dart'
    as _i899;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_saved_medical_log_detail_usecase.dart'
    as _i895;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_skin_machine_medical_log_detail_usecase.dart'
    as _i897;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_tattoo_color_medical_log_detail_usecase.dart'
    as _i887;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_tattoo_time_medical_log_detail_usecase.dart'
    as _i892;
import 'package:theadvance/src/domain/usecases/medical_log_detail/ha_point_list_get_medical_log_detail_usecase.dart'
    as _i885;
import 'package:theadvance/src/domain/usecases/medical_log_detail/khacnho_list_get_medical_log_detail_usecase.dart'
    as _i893;
import 'package:theadvance/src/domain/usecases/medical_log_detail/medicine_list_get_medical_log_detail_usecase.dart'
    as _i889;
import 'package:theadvance/src/domain/usecases/medical_log_detail/remove_medical_log_detail_usecase.dart'
    as _i891;
import 'package:theadvance/src/domain/usecases/medical_log_detail/save_medical_log_detail_usecase.dart'
    as _i900;
import 'package:theadvance/src/domain/usecases/medical_log_detail/update_log_medical_detail_usecase.dart'
    as _i890;
import 'package:theadvance/src/domain/usecases/medical_product_create/get_saved_medical_product_create_usecase.dart'
    as _i520;
import 'package:theadvance/src/domain/usecases/medical_product_create/medical_product_create_usecase.dart'
    as _i523;
import 'package:theadvance/src/domain/usecases/medical_product_create/products_medical_product_create_usecase.dart'
    as _i519;
import 'package:theadvance/src/domain/usecases/medical_product_create/remove_medical_product_create_usecase.dart'
    as _i521;
import 'package:theadvance/src/domain/usecases/medical_product_create/save_medical_product_create_usecase.dart'
    as _i522;
import 'package:theadvance/src/domain/usecases/medical_service_create/get_saved_medical_service_create_usecase.dart'
    as _i744;
import 'package:theadvance/src/domain/usecases/medical_service_create/medical_service_create_usecase.dart'
    as _i746;
import 'package:theadvance/src/domain/usecases/medical_service_create/methods_medical_service_create_usecase.dart'
    as _i743;
import 'package:theadvance/src/domain/usecases/medical_service_create/remove_medical_service_create_usecase.dart'
    as _i741;
import 'package:theadvance/src/domain/usecases/medical_service_create/save_medical_service_create_usecase.dart'
    as _i745;
import 'package:theadvance/src/domain/usecases/medical_service_create/services_medical_service_create_usecase.dart'
    as _i742;
import 'package:theadvance/src/domain/usecases/medical_service_list/get_medical_service_list_usecase.dart'
    as _i482;
import 'package:theadvance/src/domain/usecases/medical_service_list/get_saved_medical_service_list_usecase.dart'
    as _i479;
import 'package:theadvance/src/domain/usecases/medical_service_list/remove_medical_service_list_usecase.dart'
    as _i481;
import 'package:theadvance/src/domain/usecases/medical_service_list/save_medical_service_list_usecase.dart'
    as _i480;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/get_medical_service_log_list_usecase.dart'
    as _i831;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/get_saved_medical_service_log_list_usecase.dart'
    as _i832;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/remove_medical_service_log_list_usecase.dart'
    as _i830;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/save_medical_service_log_list_usecase.dart'
    as _i829;
import 'package:theadvance/src/domain/usecases/medical_template_list/get_medical_template_list_usecase.dart'
    as _i882;
import 'package:theadvance/src/domain/usecases/medical_template_list/get_saved_medical_template_list_usecase.dart'
    as _i883;
import 'package:theadvance/src/domain/usecases/medical_template_list/medical_template_detail_get_medical_template_list_usecase.dart'
    as _i881;
import 'package:theadvance/src/domain/usecases/medical_template_list/remove_medical_template_list_usecase.dart'
    as _i880;
import 'package:theadvance/src/domain/usecases/medical_template_list/save_medical_template_list_usecase.dart'
    as _i884;
import 'package:theadvance/src/domain/usecases/medicine_detail/create_medicine_detail_usecase.dart'
    as _i727;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_medicine_detail_usecase.dart'
    as _i730;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_saved_medicine_detail_usecase.dart'
    as _i729;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_unit_medicine_detail_usecase.dart'
    as _i726;
import 'package:theadvance/src/domain/usecases/medicine_detail/remove_medicine_detail_usecase.dart'
    as _i725;
import 'package:theadvance/src/domain/usecases/medicine_detail/save_medicine_detail_usecase.dart'
    as _i731;
import 'package:theadvance/src/domain/usecases/medicine_detail/update_medicine_detail_usecase.dart'
    as _i728;
import 'package:theadvance/src/domain/usecases/news/get_detail_news_usecase.dart'
    as _i601;
import 'package:theadvance/src/domain/usecases/news/get_news_usecase.dart'
    as _i599;
import 'package:theadvance/src/domain/usecases/news/search_news_usecase.dart'
    as _i600;
import 'package:theadvance/src/domain/usecases/note_details/create_note_details_usecase.dart'
    as _i753;
import 'package:theadvance/src/domain/usecases/note_details/get_note_details_usecase.dart'
    as _i757;
import 'package:theadvance/src/domain/usecases/note_details/get_saved_note_details_usecase.dart'
    as _i756;
import 'package:theadvance/src/domain/usecases/note_details/remove_note_details_usecase.dart'
    as _i755;
import 'package:theadvance/src/domain/usecases/note_details/save_note_details_usecase.dart'
    as _i758;
import 'package:theadvance/src/domain/usecases/note_details/update_note_details_usecase.dart'
    as _i754;
import 'package:theadvance/src/domain/usecases/notification/get_detail_notification_usecase.dart'
    as _i654;
import 'package:theadvance/src/domain/usecases/notification/get_navigation_info_usecase.dart'
    as _i655;
import 'package:theadvance/src/domain/usecases/notification/get_notifications_usecase.dart'
    as _i657;
import 'package:theadvance/src/domain/usecases/notification/post_read_notifcation_usecase.dart'
    as _i653;
import 'package:theadvance/src/domain/usecases/notification/read_all_notification_usecase.dart'
    as _i658;
import 'package:theadvance/src/domain/usecases/notification/search_notifications_usecase.dart'
    as _i656;
import 'package:theadvance/src/domain/usecases/notification_list/delete_notification_usecase.dart'
    as _i264;
import 'package:theadvance/src/domain/usecases/notification_list/get_notification_list_usecase.dart'
    as _i262;
import 'package:theadvance/src/domain/usecases/notification_list/get_saved_notification_list_usecase.dart'
    as _i266;
import 'package:theadvance/src/domain/usecases/notification_list/put_read_all_social_usecase.dart'
    as _i263;
import 'package:theadvance/src/domain/usecases/notification_list/remove_notification_list_usecase.dart'
    as _i261;
import 'package:theadvance/src/domain/usecases/notification_list/save_notification_list_usecase.dart'
    as _i265;
import 'package:theadvance/src/domain/usecases/product_confirm/approval_product_detail_confirm_usecase.dart'
    as _i595;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_confirm_branch_usecase.dart'
    as _i596;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_confirm_usecase.dart'
    as _i593;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_detail_confirm_usecase.dart'
    as _i594;
import 'package:theadvance/src/domain/usecases/product_confirm/reject_product_detail_confirm_usecase.dart'
    as _i597;
import 'package:theadvance/src/domain/usecases/province/get_province_usecase.dart'
    as _i649;
import 'package:theadvance/src/domain/usecases/px_list/get_px_list_usecase.dart'
    as _i826;
import 'package:theadvance/src/domain/usecases/px_list/get_saved_px_list_usecase.dart'
    as _i825;
import 'package:theadvance/src/domain/usecases/px_list/remove_px_list_usecase.dart'
    as _i828;
import 'package:theadvance/src/domain/usecases/px_list/save_px_list_usecase.dart'
    as _i827;
import 'package:theadvance/src/domain/usecases/px_recheck/assign_px_recheck_update_usecase.dart'
    as _i870;
import 'package:theadvance/src/domain/usecases/px_recheck/assigns_fetch_px_recheck_usecase.dart'
    as _i904;
import 'package:theadvance/src/domain/usecases/px_recheck/note_finish_px_recheck_usecase.dart'
    as _i902;
import 'package:theadvance/src/domain/usecases/px_recheck/work_status_update_px_recheck_usecase.dart'
    as _i903;
import 'package:theadvance/src/domain/usecases/px_task_list/get_px_task_list_usecase.dart'
    as _i531;
import 'package:theadvance/src/domain/usecases/px_task_list/get_saved_px_task_list_usecase.dart'
    as _i529;
import 'package:theadvance/src/domain/usecases/px_task_list/remove_px_task_list_usecase.dart'
    as _i528;
import 'package:theadvance/src/domain/usecases/px_task_list/save_px_task_list_usecase.dart'
    as _i530;
import 'package:theadvance/src/domain/usecases/px_unasigned/get_px_customer_list_usecase.dart'
    as _i765;
import 'package:theadvance/src/domain/usecases/px_unasigned/get_saved_px_unasigned_usecase.dart'
    as _i767;
import 'package:theadvance/src/domain/usecases/px_unasigned/remove_px_unasigned_usecase.dart'
    as _i764;
import 'package:theadvance/src/domain/usecases/px_unasigned/save_px_unasigned_usecase.dart'
    as _i766;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/assign_px_unasigned_update_usecase.dart'
    as _i939;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/employees_fetch_px_unasigned_update_usecase.dart'
    as _i938;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/employees_in_room_usecase.dart'
    as _i937;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/get_px_unasigned_update_usecase.dart'
    as _i941;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/get_saved_px_unasigned_update_usecase.dart'
    as _i944;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/remove_px_unasigned_update_usecase.dart'
    as _i943;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/save_px_unasigned_update_usecase.dart'
    as _i942;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/works_fetch_px_unasigned_update_usecase.dart'
    as _i940;
import 'package:theadvance/src/domain/usecases/rating_human/get_question_detail_usecase.dart'
    as _i721;
import 'package:theadvance/src/domain/usecases/rating_human/get_rating_human_usecase.dart'
    as _i723;
import 'package:theadvance/src/domain/usecases/rating_human/save_question_usecase.dart'
    as _i722;
import 'package:theadvance/src/domain/usecases/rating_human/submit_question_usecase.dart'
    as _i720;
import 'package:theadvance/src/domain/usecases/request/get_list_support_request_usecase.dart'
    as _i950;
import 'package:theadvance/src/domain/usecases/request/send_support_request_usecase.dart'
    as _i951;
import 'package:theadvance/src/domain/usecases/schedule_details/get_saved_schedule_details_usecase.dart'
    as _i579;
import 'package:theadvance/src/domain/usecases/schedule_details/get_schedule_details_usecase.dart'
    as _i580;
import 'package:theadvance/src/domain/usecases/schedule_details/remove_schedule_details_usecase.dart'
    as _i577;
import 'package:theadvance/src/domain/usecases/schedule_details/save_schedule_details_usecase.dart'
    as _i578;
import 'package:theadvance/src/domain/usecases/select_px_room/get_saved_select_px_room_usecase.dart'
    as _i432;
import 'package:theadvance/src/domain/usecases/select_px_room/get_select_px_room_usecase.dart'
    as _i429;
import 'package:theadvance/src/domain/usecases/select_px_room/remove_select_px_room_usecase.dart'
    as _i430;
import 'package:theadvance/src/domain/usecases/select_px_room/room_change_select_px_room_usecase.dart'
    as _i431;
import 'package:theadvance/src/domain/usecases/select_px_room/save_select_px_room_usecase.dart'
    as _i433;
import 'package:theadvance/src/domain/usecases/service_and_product/get_category_service_and_product_usecase.dart'
    as _i918;
import 'package:theadvance/src/domain/usecases/service_and_product/get_saved_service_and_product_usecase.dart'
    as _i917;
import 'package:theadvance/src/domain/usecases/service_and_product/get_service_and_product_actions_usecase.dart'
    as _i911;
import 'package:theadvance/src/domain/usecases/service_and_product/get_service_and_product_usecase.dart'
    as _i912;
import 'package:theadvance/src/domain/usecases/service_and_product/products_get_service_and_product_usecase.dart'
    as _i915;
import 'package:theadvance/src/domain/usecases/service_and_product/remove_service_and_product_usecase.dart'
    as _i916;
import 'package:theadvance/src/domain/usecases/service_and_product/save_service_and_product_usecase.dart'
    as _i913;
import 'package:theadvance/src/domain/usecases/service_and_product/services_get_service_and_product_usecase.dart'
    as _i914;
import 'package:theadvance/src/domain/usecases/service_detail/doctor_fetch_service_detail_usecase.dart'
    as _i676;
import 'package:theadvance/src/domain/usecases/service_detail/employee_fetch_service_detail_usecase.dart'
    as _i677;
import 'package:theadvance/src/domain/usecases/setting/get_font_option_usecase.dart'
    as _i422;
import 'package:theadvance/src/domain/usecases/setting/get_language_option_usecase.dart'
    as _i417;
import 'package:theadvance/src/domain/usecases/setting/get_theme_option_usecase.dart'
    as _i418;
import 'package:theadvance/src/domain/usecases/setting/is_dark_mode_usecase.dart'
    as _i421;
import 'package:theadvance/src/domain/usecases/setting/is_light_mode_usecase.dart'
    as _i423;
import 'package:theadvance/src/domain/usecases/setting/save_font_option_usecase.dart'
    as _i416;
import 'package:theadvance/src/domain/usecases/setting/save_language_option_usecase.dart'
    as _i419;
import 'package:theadvance/src/domain/usecases/setting/save_theme_option_usecase.dart'
    as _i420;
import 'package:theadvance/src/domain/usecases/staff/get_department_usecase.dart'
    as _i602;
import 'package:theadvance/src/domain/usecases/staff/get_function_room_usecase.dart'
    as _i603;
import 'package:theadvance/src/domain/usecases/staff/get_staff_usecase.dart'
    as _i604;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/get_saved_staff_evaluation_periods_usecase.dart'
    as _i512;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/get_staff_evaluation_periods_usecase.dart'
    as _i513;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/remove_staff_evaluation_periods_usecase.dart'
    as _i510;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/save_staff_evaluation_periods_usecase.dart'
    as _i511;
import 'package:theadvance/src/domain/usecases/sticker_social/create_sticker_usecase.dart'
    as _i349;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_only_set_usecase.dart'
    as _i344;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_recent_usecase.dart'
    as _i345;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_set_usecase.dart'
    as _i348;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_usecase.dart'
    as _i351;
import 'package:theadvance/src/domain/usecases/sticker_social/remove_sticker_set_usecase.dart'
    as _i352;
import 'package:theadvance/src/domain/usecases/sticker_social/remove_sticker_usecase.dart'
    as _i346;
import 'package:theadvance/src/domain/usecases/sticker_social/update_sticker_recent_usecase.dart'
    as _i347;
import 'package:theadvance/src/domain/usecases/sticker_social/update_sticker_set_usecase.dart'
    as _i343;
import 'package:theadvance/src/domain/usecases/sticker_social/upload_sticker_usecase.dart'
    as _i350;
import 'package:theadvance/src/domain/usecases/story_detail/get_saved_story_detail_usecase.dart'
    as _i175;
import 'package:theadvance/src/domain/usecases/story_detail/get_story_detail_usecase.dart'
    as _i176;
import 'package:theadvance/src/domain/usecases/story_detail/remove_story_detail_usecase.dart'
    as _i174;
import 'package:theadvance/src/domain/usecases/story_detail/save_story_detail_usecase.dart'
    as _i177;
import 'package:theadvance/src/domain/usecases/story_list/delete_story_usecase.dart'
    as _i475;
import 'package:theadvance/src/domain/usecases/story_list/delete_story_vote_usecase.dart'
    as _i474;
import 'package:theadvance/src/domain/usecases/story_list/get_emoji_list_usecase.dart'
    as _i465;
import 'package:theadvance/src/domain/usecases/story_list/get_story_list_usecase.dart'
    as _i466;
import 'package:theadvance/src/domain/usecases/story_list/get_story_rule_usecase.dart'
    as _i476;
import 'package:theadvance/src/domain/usecases/story_list/get_story_search_usecase.dart'
    as _i467;
import 'package:theadvance/src/domain/usecases/story_list/get_total_notification_usecase.dart'
    as _i470;
import 'package:theadvance/src/domain/usecases/story_list/get_vote_users_usecase.dart'
    as _i473;
import 'package:theadvance/src/domain/usecases/story_list/post_story_usecase.dart'
    as _i468;
import 'package:theadvance/src/domain/usecases/story_list/put_story_vote_usecase.dart'
    as _i469;
import 'package:theadvance/src/domain/usecases/story_list/social_upload_file_usecase.dart'
    as _i472;
import 'package:theadvance/src/domain/usecases/story_list/update_story_usecase.dart'
    as _i471;
import 'package:theadvance/src/domain/usecases/story_person_list/get_story_person_list_usecase.dart'
    as _i414;
import 'package:theadvance/src/domain/usecases/story_person_list/get_story_person_list_user_usecase.dart'
    as _i415;
import 'package:theadvance/src/domain/usecases/tag_image/delete_image_by_combo_usecase.dart'
    as _i845;
import 'package:theadvance/src/domain/usecases/tag_image/delete_tag_by_combo_usecase.dart'
    as _i844;
import 'package:theadvance/src/domain/usecases/tag_image/get_combo_tag_usecase.dart'
    as _i846;
import 'package:theadvance/src/domain/usecases/tag_image/get_image_by_combo_tag_usecase.dart'
    as _i847;
import 'package:theadvance/src/domain/usecases/tag_list/get_tag_list_usecase.dart'
    as _i271;
import 'package:theadvance/src/domain/usecases/taking_care_customer/bot_type_load_taking_care_customer_usecase.dart'
    as _i822;
import 'package:theadvance/src/domain/usecases/taking_care_customer/check_employee_in_room_taking_care_customer_usecase.dart'
    as _i818;
import 'package:theadvance/src/domain/usecases/taking_care_customer/create_support_taking_care_customer_usecase.dart'
    as _i813;
import 'package:theadvance/src/domain/usecases/taking_care_customer/create_treatment_details_taking_care_customer_usecase.dart'
    as _i817;
import 'package:theadvance/src/domain/usecases/taking_care_customer/finish_task_taking_care_customer_usecase.dart'
    as _i811;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_saved_taking_care_customer_usecase.dart'
    as _i812;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_section_taking_care_customer_usecase.dart'
    as _i814;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_taking_care_customer_usecase.dart'
    as _i820;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_treatment_photo_taking_care_customer_usecase.dart'
    as _i821;
import 'package:theadvance/src/domain/usecases/taking_care_customer/noti_bot_type_load_taking_care_customer_usecase.dart'
    as _i809;
import 'package:theadvance/src/domain/usecases/taking_care_customer/remove_image_taking_care_customer_usecase.dart'
    as _i810;
import 'package:theadvance/src/domain/usecases/taking_care_customer/remove_taking_care_customer_usecase.dart'
    as _i815;
import 'package:theadvance/src/domain/usecases/taking_care_customer/save_taking_care_customer_usecase.dart'
    as _i816;
import 'package:theadvance/src/domain/usecases/taking_care_customer/update_service_detail_taking_care_customer_usecase.dart'
    as _i819;
import 'package:theadvance/src/domain/usecases/taking_care_customer/upload_images_taking_care_customer_usecase.dart'
    as _i824;
import 'package:theadvance/src/domain/usecases/taking_care_customer/upload_record_taking_care_customer_usecase.dart'
    as _i823;
import 'package:theadvance/src/domain/usecases/task/creating_task_usecase.dart'
    as _i406;
import 'package:theadvance/src/domain/usecases/task/get_detail_job_scheduler_usecase.dart'
    as _i411;
import 'package:theadvance/src/domain/usecases/task/get_general_job_scheduler_usecase.dart'
    as _i410;
import 'package:theadvance/src/domain/usecases/task/get_job_scheduler_usecase.dart'
    as _i408;
import 'package:theadvance/src/domain/usecases/task/get_repeat_task_usecase.dart'
    as _i407;
import 'package:theadvance/src/domain/usecases/task/submit_job_scheduler_usecase.dart'
    as _i409;
import 'package:theadvance/src/domain/usecases/ticket_active/complete_ticket_usecase.dart'
    as _i615;
import 'package:theadvance/src/domain/usecases/ticket_active/create_ticket_active_usecase.dart'
    as _i617;
import 'package:theadvance/src/domain/usecases/ticket_active/get_ticket_active_usecase.dart'
    as _i616;
import 'package:theadvance/src/domain/usecases/ticket_detail/confirm_ticket_usecase.dart'
    as _i571;
import 'package:theadvance/src/domain/usecases/ticket_detail/get_saved_ticket_detail_usecase.dart'
    as _i568;
import 'package:theadvance/src/domain/usecases/ticket_detail/get_ticket_detail_usecase.dart'
    as _i566;
import 'package:theadvance/src/domain/usecases/ticket_detail/recept_ticket_usecase.dart'
    as _i569;
import 'package:theadvance/src/domain/usecases/ticket_detail/remove_ticket_detail_usecase.dart'
    as _i572;
import 'package:theadvance/src/domain/usecases/ticket_detail/save_ticket_detail_usecase.dart'
    as _i565;
import 'package:theadvance/src/domain/usecases/ticket_detail/ticket_detail_reason_usecase.dart'
    as _i567;
import 'package:theadvance/src/domain/usecases/ticket_detail/ticket_rework_usecase.dart'
    as _i570;
import 'package:theadvance/src/domain/usecases/ticketv2/create_ticket_usecase.dart'
    as _i442;
import 'package:theadvance/src/domain/usecases/ticketv2/get_my_ticket_usecase.dart'
    as _i445;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_all_group_usecase.dart'
    as _i441;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_all_type_usecase.dart'
    as _i447;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_created_type_usecase.dart'
    as _i440;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_group_type_usecase.dart'
    as _i448;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_ticketv2_usecase.dart'
    as _i443;
import 'package:theadvance/src/domain/usecases/ticketv2/ticket_upload_file_usecase.dart'
    as _i444;
import 'package:theadvance/src/domain/usecases/ticketv2/update_ticket_usecase.dart'
    as _i446;
import 'package:theadvance/src/domain/usecases/tracking/send_event_use_case.dart'
    as _i72;
import 'package:theadvance/src/domain/usecases/tracking/track_banner_use_case.dart'
    as _i70;
import 'package:theadvance/src/domain/usecases/tracking/track_login_use_case.dart'
    as _i66;
import 'package:theadvance/src/domain/usecases/tracking/track_notification_use_case.dart'
    as _i75;
import 'package:theadvance/src/domain/usecases/tracking/track_open_app_use_case.dart'
    as _i71;
import 'package:theadvance/src/domain/usecases/tracking/track_open_landing_page_use_case.dart'
    as _i69;
import 'package:theadvance/src/domain/usecases/tracking/track_popup_use_case.dart'
    as _i73;
import 'package:theadvance/src/domain/usecases/tracking/track_register_use_case.dart'
    as _i74;
import 'package:theadvance/src/domain/usecases/tracking/track_valid_install_use_case.dart'
    as _i68;
import 'package:theadvance/src/domain/usecases/universal_qr_scan/universal_qr_scan_usecase.dart'
    as _i377;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_get_usecase.dart'
    as _i798;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_remove_usecase.dart'
    as _i795;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_save_usecase.dart'
    as _i800;
import 'package:theadvance/src/domain/usecases/user/cache_user_usecase.dart'
    as _i799;
import 'package:theadvance/src/domain/usecases/user/change_password_usecase.dart'
    as _i789;
import 'package:theadvance/src/domain/usecases/user/check_employee_usecase.dart'
    as _i772;
import 'package:theadvance/src/domain/usecases/user/check_permission_user_usecase.dart'
    as _i681;
import 'package:theadvance/src/domain/usecases/user/check_phone_usecase.dart'
    as _i774;
import 'package:theadvance/src/domain/usecases/user/checkin_permission_check_user_usecase.dart'
    as _i679;
import 'package:theadvance/src/domain/usecases/user/checkin_usecase.dart'
    as _i372;
import 'package:theadvance/src/domain/usecases/user/clear_cache_usecase.dart'
    as _i368;
import 'package:theadvance/src/domain/usecases/user/confirm_otp_usecase.dart'
    as _i784;
import 'package:theadvance/src/domain/usecases/user/delete_home_menu_cache_usecase.dart'
    as _i371;
import 'package:theadvance/src/domain/usecases/user/delete_profile_usecase.dart'
    as _i369;
import 'package:theadvance/src/domain/usecases/user/delete_token_usecase.dart'
    as _i367;
import 'package:theadvance/src/domain/usecases/user/get_configuration_usecase.dart'
    as _i783;
import 'package:theadvance/src/domain/usecases/user/get_enable_online_logger_usecase.dart'
    as _i779;
import 'package:theadvance/src/domain/usecases/user/get_otp_usecase.dart'
    as _i773;
import 'package:theadvance/src/domain/usecases/user/get_profile_usecase.dart'
    as _i780;
import 'package:theadvance/src/domain/usecases/user/get_profiles_usecase.dart'
    as _i778;
import 'package:theadvance/src/domain/usecases/user/get_user_info_usecase.dart'
    as _i791;
import 'package:theadvance/src/domain/usecases/user/has_token_usecase.dart'
    as _i797;
import 'package:theadvance/src/domain/usecases/user/has_user_data_usecase.dart'
    as _i365;
import 'package:theadvance/src/domain/usecases/user/is_show_boarding.dart'
    as _i796;
import 'package:theadvance/src/domain/usecases/user/login_social_usecase.dart'
    as _i786;
import 'package:theadvance/src/domain/usecases/user/login_usecase.dart'
    as _i775;
import 'package:theadvance/src/domain/usecases/user/logout_usecase.dart'
    as _i776;
import 'package:theadvance/src/domain/usecases/user/persist_token_usecase.dart'
    as _i366;
import 'package:theadvance/src/domain/usecases/user/reset_password_usecase.dart'
    as _i781;
import 'package:theadvance/src/domain/usecases/user/save_account_usecase.dart'
    as _i370;
import 'package:theadvance/src/domain/usecases/user/save_enable_online_logger_usecase.dart'
    as _i363;
import 'package:theadvance/src/domain/usecases/user/save_navigation_usecase.dart'
    as _i362;
import 'package:theadvance/src/domain/usecases/user/save_profile_usecase.dart'
    as _i361;
import 'package:theadvance/src/domain/usecases/user/send_kyc_photos_setting_usecase.dart'
    as _i678;
import 'package:theadvance/src/domain/usecases/user/show_onboarding_usecase.dart'
    as _i364;
import 'package:theadvance/src/domain/usecases/user/stringee_token_fetch_user_usecase.dart'
    as _i680;
import 'package:theadvance/src/domain/usecases/user/submit_feedback_usecase.dart'
    as _i787;
import 'package:theadvance/src/domain/usecases/user/update_bio_usecase.dart'
    as _i785;
import 'package:theadvance/src/domain/usecases/user/update_profile_usecase.dart'
    as _i771;
import 'package:theadvance/src/domain/usecases/user/upload_audios_usecase.dart'
    as _i788;
import 'package:theadvance/src/domain/usecases/user/upload_files_usecase.dart'
    as _i782;
import 'package:theadvance/src/domain/usecases/user/upload_images_usecase.dart'
    as _i777;
import 'package:theadvance/src/domain/usecases/user/upload_usecase.dart'
    as _i790;
import 'package:theadvance/src/domain/usecases/user/user_deletion_usecase.dart'
    as _i682;
import 'package:theadvance/src/domain/usecases/user_list/get_saved_user_list_usecase.dart'
    as _i849;
import 'package:theadvance/src/domain/usecases/user_list/get_user_list_usecase.dart'
    as _i850;
import 'package:theadvance/src/domain/usecases/user_list/remove_user_list_usecase.dart'
    as _i851;
import 'package:theadvance/src/domain/usecases/user_list/save_user_list_usecase.dart'
    as _i852;
import 'package:theadvance/src/domain/usecases/user_ticket/get_dropdown_list_status.dart'
    as _i453;
import 'package:theadvance/src/domain/usecases/user_ticket/get_user_ticket.dart'
    as _i454;
import 'package:theadvance/src/module/register_module.dart' as _i1007;
import 'package:theadvance/src/presentation/_blocs/authentication/authentication_bloc.dart'
    as _i962;
import 'package:theadvance/src/presentation/_blocs/collaborator_user/collaborator_user_bloc.dart'
    as _i807;
import 'package:theadvance/src/presentation/_blocs/event_authentication_bloc/event_authentication_bloc.dart'
    as _i3;
import 'package:theadvance/src/presentation/_blocs/general_bloc/general_bloc.dart'
    as _i984;
import 'package:theadvance/src/presentation/action_attendance/bloc/action_attendance_bloc.dart'
    as _i991;
import 'package:theadvance/src/presentation/add_tags_image/bloc/add_tags_image_bloc.dart'
    as _i981;
import 'package:theadvance/src/presentation/assign_task/bloc/assign_task_bloc.dart'
    as _i1004;
import 'package:theadvance/src/presentation/bed_selection/bloc/bed_selection_bloc.dart'
    as _i868;
import 'package:theadvance/src/presentation/branch_chat_list/bloc/branch_chat_list_bloc.dart'
    as _i910;
import 'package:theadvance/src/presentation/branch_selection/bloc/branch_selection_bloc.dart'
    as _i858;
import 'package:theadvance/src/presentation/chat/bloc/chat_bloc.dart' as _i436;
import 'package:theadvance/src/presentation/chat_list/bloc/chat_list_bloc.dart'
    as _i770;
import 'package:theadvance/src/presentation/chat_select_branch/bloc/chat_select_branch_bloc.dart'
    as _i532;
import 'package:theadvance/src/presentation/checkin_photo/bloc/checkin_photo_bloc.dart'
    as _i960;
import 'package:theadvance/src/presentation/collaborator/checkin_reminder/bloc/checkin_reminder_bloc.dart'
    as _i834;
import 'package:theadvance/src/presentation/collaborator/confirm_otp/bloc/confirm_otp_bloc.dart'
    as _i873;
import 'package:theadvance/src/presentation/collaborator/create_support_request/bloc/create_support_requests_bloc.dart'
    as _i994;
import 'package:theadvance/src/presentation/collaborator/creating_eform/bloc/creating_eform_bloc.dart'
    as _i857;
import 'package:theadvance/src/presentation/collaborator/creating_task/bloc/creating_task_bloc.dart'
    as _i869;
import 'package:theadvance/src/presentation/collaborator/creating_task/selecting_office/bloc/selecting_office_bloc.dart'
    as _i860;
import 'package:theadvance/src/presentation/collaborator/creating_task/selecting_staff/bloc/selecting_staff_bloc.dart'
    as _i922;
import 'package:theadvance/src/presentation/collaborator/creating_task/sheet/bloc/repeat_task_bloc.dart'
    as _i426;
import 'package:theadvance/src/presentation/collaborator/customer/customer/bloc/customer_bloc.dart'
    as _i964;
import 'package:theadvance/src/presentation/collaborator/customer/function_room/bloc/function_room_bloc.dart'
    as _i923;
import 'package:theadvance/src/presentation/collaborator/customer/info_customer/bloc/info_customer_bloc.dart'
    as _i592;
import 'package:theadvance/src/presentation/collaborator/detail_eform/approval_otp/bloc/approving_otp_bloc.dart'
    as _i965;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/bloc/detail_eform_bloc.dart'
    as _i618;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/sheet/refuse_bottom_sheet/bloc/refuse_bloc.dart'
    as _i871;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/sheet/type_approving_bottom_sheet/bloc/type_approving_bloc.dart'
    as _i919;
import 'package:theadvance/src/presentation/collaborator/detail_job_scheduler/bloc/detail_job_scheduler_bloc.dart'
    as _i499;
import 'package:theadvance/src/presentation/collaborator/detail_job_scheduler/sheet/bloc/feedback_bloc.dart'
    as _i987;
import 'package:theadvance/src/presentation/collaborator/detail_news/bloc/detail_news_bloc.dart'
    as _i637;
import 'package:theadvance/src/presentation/collaborator/detail_notification/bloc/detail_notification_bloc.dart'
    as _i949;
import 'package:theadvance/src/presentation/collaborator/edit_home_menu/bloc/edit_home_menu_bloc.dart'
    as _i952;
import 'package:theadvance/src/presentation/collaborator/eform/bloc/eform_bloc.dart'
    as _i752;
import 'package:theadvance/src/presentation/collaborator/eform/sheet/eform_category_bottom_sheet/bloc/eform_category_bloc.dart'
    as _i901;
import 'package:theadvance/src/presentation/collaborator/history_checkin/bloc/history_checkin_bloc.dart'
    as _i1002;
import 'package:theadvance/src/presentation/collaborator/home/<USER>/home_bloc.dart'
    as _i945;
import 'package:theadvance/src/presentation/collaborator/home_find/bloc/home_find_bloc.dart'
    as _i691;
import 'package:theadvance/src/presentation/collaborator/job_scheduler/bloc/job_scheduler_bloc.dart'
    as _i905;
import 'package:theadvance/src/presentation/collaborator/language/bloc/language_bloc.dart'
    as _i4;
import 'package:theadvance/src/presentation/collaborator/login/bloc/login_bloc.dart'
    as _i976;
import 'package:theadvance/src/presentation/collaborator/monthly_history_checkin/bloc/monthly_history_checkin_bloc.dart'
    as _i977;
import 'package:theadvance/src/presentation/collaborator/more/bloc/more_bloc.dart'
    as _i999;
import 'package:theadvance/src/presentation/collaborator/news/bloc/news_bloc.dart'
    as _i613;
import 'package:theadvance/src/presentation/collaborator/notifications/bloc/notifications_bloc.dart'
    as _i872;
import 'package:theadvance/src/presentation/collaborator/order_food/bloc/order_food_bloc.dart'
    as _i614;
import 'package:theadvance/src/presentation/collaborator/order_food/bloc/order_food_service_bloc.dart'
    as _i948;
import 'package:theadvance/src/presentation/collaborator/profile/bloc/profile_bloc.dart'
    as _i955;
import 'package:theadvance/src/presentation/collaborator/service_and_product/bloc/service_and_product_bloc.dart'
    as _i1005;
import 'package:theadvance/src/presentation/collaborator/set_password/bloc/set_password_bloc.dart'
    as _i959;
import 'package:theadvance/src/presentation/collaborator/setting/bloc/setting_bloc.dart'
    as _i996;
import 'package:theadvance/src/presentation/collaborator/support_requests/bloc/support_requests_bloc.dart'
    as _i1003;
import 'package:theadvance/src/presentation/collaborator/tabbar/bloc/tabbar_bloc.dart'
    as _i699;
import 'package:theadvance/src/presentation/comment_list/bloc/comment_list_bloc.dart'
    as _i906;
import 'package:theadvance/src/presentation/consultation_customer/bloc/consultation_customer_bloc.dart'
    as _i997;
import 'package:theadvance/src/presentation/consultation_customer/bloc/service_detail_bloc.dart'
    as _i990;
import 'package:theadvance/src/presentation/consultation_customer/bloc/service_type_purchase_bloc.dart'
    as _i380;
import 'package:theadvance/src/presentation/consultation_history/bloc/consultation_history_bloc.dart'
    as _i978;
import 'package:theadvance/src/presentation/consultation_history_detail/bloc/consultation_history_detail_bloc.dart'
    as _i983;
import 'package:theadvance/src/presentation/consultation_manager/bloc/consultation_manager_bloc.dart'
    as _i1006;
import 'package:theadvance/src/presentation/create_chat_folder/bloc/create_chat_folder_bloc.dart'
    as _i564;
import 'package:theadvance/src/presentation/create_chat_group/bloc/create_chat_group_bloc.dart'
    as _i483;
import 'package:theadvance/src/presentation/create_customer/bloc/create_customer_bloc.dart'
    as _i957;
import 'package:theadvance/src/presentation/customer_booking_info/bloc/customer_booking_info_bloc.dart'
    as _i980;
import 'package:theadvance/src/presentation/customer_info_details/bloc/customer_info_details_bloc.dart'
    as _i975;
import 'package:theadvance/src/presentation/customer_list/bloc/customer_list_bloc.dart'
    as _i956;
import 'package:theadvance/src/presentation/customer_profile/bloc/customer_profile_bloc.dart'
    as _i979;
import 'package:theadvance/src/presentation/customer_record/bloc/customer_record_bloc.dart'
    as _i706;
import 'package:theadvance/src/presentation/customer_schedule/bloc/customer_schedule_bloc.dart'
    as _i963;
import 'package:theadvance/src/presentation/detail_crm_customer/bloc/detail_crm_customer_bloc.dart'
    as _i986;
import 'package:theadvance/src/presentation/detail_staff_evaluation_period/bloc/detail_staff_evaluation_period_bloc.dart'
    as _i690;
import 'package:theadvance/src/presentation/dev/bloc/dev_bloc.dart' as _i805;
import 'package:theadvance/src/presentation/group_chat_detail/bloc/group_chat_detail_bloc.dart'
    as _i833;
import 'package:theadvance/src/presentation/hr_organization/bloc/hr_organization_bloc.dart'
    as _i724;
import 'package:theadvance/src/presentation/important_notes/bloc/important_notes_bloc.dart'
    as _i700;
import 'package:theadvance/src/presentation/kpi_employee/bloc/kpi_employee_bloc.dart'
    as _i866;
import 'package:theadvance/src/presentation/like_list/bloc/like_list_bloc.dart'
    as _i768;
import 'package:theadvance/src/presentation/list_customer/bloc/list_customer_bloc.dart'
    as _i907;
import 'package:theadvance/src/presentation/medical_department_list/bloc/medical_department_list_bloc.dart'
    as _i652;
import 'package:theadvance/src/presentation/medical_log_detail/bloc/medical_log_detail_bloc.dart'
    as _i995;
import 'package:theadvance/src/presentation/medical_product_creation/bloc/medical_product_creation_bloc.dart'
    as _i989;
import 'package:theadvance/src/presentation/medical_service_creation/bloc/medical_service_creation_bloc.dart'
    as _i998;
import 'package:theadvance/src/presentation/medical_service_list/bloc/medical_service_list_bloc.dart'
    as _i498;
import 'package:theadvance/src/presentation/medical_service_log_list/bloc/medical_service_log_list_bloc.dart'
    as _i953;
import 'package:theadvance/src/presentation/medical_template_list/bloc/medical_template_list_bloc.dart'
    as _i920;
import 'package:theadvance/src/presentation/medicine_detail/bloc/medicine_detail_bloc.dart'
    as _i946;
import 'package:theadvance/src/presentation/note_details/bloc/note_details_bloc.dart'
    as _i842;
import 'package:theadvance/src/presentation/notification_list/bloc/notification_list_bloc.dart'
    as _i502;
import 'package:theadvance/src/presentation/product_confirm/bloc/product_confirm_bloc.dart'
    as _i921;
import 'package:theadvance/src/presentation/product_confirm/bloc/product_detail_confirm_bloc.dart'
    as _i806;
import 'package:theadvance/src/presentation/px_list/bloc/px_list_bloc.dart'
    as _i1001;
import 'package:theadvance/src/presentation/px_recheck/bloc/px_recheck_bloc.dart'
    as _i1000;
import 'package:theadvance/src/presentation/px_task_list/bloc/px_task_list_bloc.dart'
    as _i992;
import 'package:theadvance/src/presentation/px_unasigned/bloc/px_unasigned_bloc.dart'
    as _i985;
import 'package:theadvance/src/presentation/px_unasigned_update/bloc/px_unasigned_update_bloc.dart'
    as _i988;
import 'package:theadvance/src/presentation/rating_human/bloc/rating_human_bloc.dart'
    as _i982;
import 'package:theadvance/src/presentation/schedule_details/bloc/schedule_details_bloc.dart'
    as _i808;
import 'package:theadvance/src/presentation/select_px_room/bloc/select_px_room_bloc.dart'
    as _i936;
import 'package:theadvance/src/presentation/settings/fonts/fonts_bloc.dart'
    as _i500;
import 'package:theadvance/src/presentation/settings/multi_language/multi_language_bloc.dart'
    as _i751;
import 'package:theadvance/src/presentation/settings/theme/theme_bloc.dart'
    as _i692;
import 'package:theadvance/src/presentation/staff_evaluation_periods/bloc/staff_evaluation_periods_bloc.dart'
    as _i859;
import 'package:theadvance/src/presentation/story_detail/bloc/story_detail_bloc.dart'
    as _i848;
import 'package:theadvance/src/presentation/story_list/bloc/sticker_bloc.dart'
    as _i841;
import 'package:theadvance/src/presentation/story_list/bloc/story_list_bloc.dart'
    as _i908;
import 'package:theadvance/src/presentation/story_list/bloc/story_write_bloc.dart'
    as _i961;
import 'package:theadvance/src/presentation/story_person_list/bloc/story_person_list_bloc.dart'
    as _i501;
import 'package:theadvance/src/presentation/tag_image/bloc/tag_image_bloc.dart'
    as _i993;
import 'package:theadvance/src/presentation/taking_care_customer/bloc/taking_care_customer_bloc.dart'
    as _i843;
import 'package:theadvance/src/presentation/ticket/bloc/ticket_bloc.dart'
    as _i947;
import 'package:theadvance/src/presentation/ticket_detail/bloc/ticket_active_bloc.dart'
    as _i769;
import 'package:theadvance/src/presentation/ticket_detail/bloc/ticket_detail_bloc.dart'
    as _i958;
import 'package:theadvance/src/presentation/user_list/bloc/user_list_bloc.dart'
    as _i867;
import 'package:theadvance/src/presentation/user_ticket/bloc/drop_down_status_bloc.dart'
    as _i909;
import 'package:theadvance/src/presentation/user_ticket/bloc/user_ticket_bloc.dart'
    as _i954;
import 'package:theadvance/src/presentation/widgets/user_profile/bloc/user_profile_bloc.dart'
    as _i966;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i1.GetIt init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i3.EventAuthenticationBloc>(
        () => _i3.EventAuthenticationBloc());
    gh.factory<_i4.LanguageBloc>(() => _i4.LanguageBloc());
    gh.lazySingleton<_i5.StringeeHelper>(() => _i5.StringeeHelper());
    gh.lazySingleton<_i6.Mapper>(() => _i6.Mapper());
    gh.lazySingleton<_i7.DeeplinkHelper>(() => _i7.DeeplinkHelper());
    gh.lazySingleton<_i8.DefaultCacheManager>(
        () => registerModule.cacheManager);
    gh.lazySingleton<_i9.EZCache>(() => registerModule.collaboratorCache);
    gh.lazySingleton<_i10.AppRouter>(() => registerModule.appRouter);
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorStaticApiDio,
      instanceName: 'StaticApiDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorApiSocialDio,
      instanceName: 'ApiSocialDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorApiDio,
      instanceName: 'ApiDio',
    );
    gh.factory<String>(
      () => registerModule.collaboratorStaticApiBaseUrl,
      instanceName: 'StaticApiBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.collaboratorApiSocialBaseUrl,
      instanceName: 'ApiSocialBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.googleMapApiBaseUrl,
      instanceName: 'ApiGoogleMapBaseUrl',
    );
    gh.lazySingleton<_i12.TagListDao>(() => _i13.TagListDaoImpl());
    gh.factory<_i11.Dio>(
      () => registerModule.aIApiDio,
      instanceName: 'ApiAiDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.googleMapApiDio,
      instanceName: 'ApiGoogleMapDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.customerStaticApiDio,
      instanceName: 'StaticCustomerApiDio',
    );
    gh.factory<String>(
      () => registerModule.aIApiBaseUrl,
      instanceName: 'ApiAiBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.customerStaticApiBaseUrl,
      instanceName: 'StaticCustomerApiBaseUrl',
    );
    gh.lazySingleton<_i14.StoryListApiService>(
        () => registerModule.storyListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i15.CommentListApiService>(
        () => registerModule.commentListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i16.LikeListApiService>(
        () => registerModule.likeListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i17.StoryPersonListApiService>(
        () => registerModule.storyPersonListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i18.TagListApiService>(
        () => registerModule.tagListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i18.StoryDetailApiService>(
        () => registerModule.storyDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i18.NotificationListApiService>(
        () => registerModule.notificationListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.factory<String>(
      () => registerModule.collaboratorApiBaseUrl,
      instanceName: 'ApiBaseUrl',
    );
    gh.lazySingleton<_i19.MedicalServiceCreationDao>(
        () => _i20.MedicalServiceCreationDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i21.PxTaskListDao>(
        () => _i22.PxTaskListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i24.LocationGoogleDao>(
        () => _i25.LocationGoogleDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i26.GroupChatDetailDao>(
        () => _i27.GroupChatDetailDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i28.CheckinPhotoDao>(
        () => _i29.CheckinPhotoDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i30.MedicalLogDetailDao>(
        () => _i31.MedicalLogDetailDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i32.PxRecheckDao>(
        () => _i33.PxRecheckDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i34.HrOrganizationDao>(
        () => _i35.HrOrganizationDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i36.TakingCareCustomerDao>(
        () => _i37.TakingCareCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i38.PxListDao>(
        () => _i39.PxListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i40.ChatListRepository>(
        () => _i41.ChatListRepositoryImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i42.BranchSelectionDao>(
        () => _i43.BranchSelectionDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i44.CreateChatGroupRepository>(
        () => _i45.CreateChatGroupRepositoryImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i46.ServiceAndProductDao>(
        () => _i47.ServiceAndProductDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i48.PxUnasignedDao>(
        () => _i49.PxUnasignedDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i50.MedicalProductCreationDao>(
        () => _i51.MedicalProductCreationDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i52.CustomerListDao>(
        () => _i53.CustomerListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i54.MedicalServiceLogListDao>(
        () => _i55.MedicalServiceLogListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i56.CustomerScheduleDao>(
        () => _i57.CustomerScheduleDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i58.DetailCrmCustomerDao>(
        () => _i59.DetailCrmCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i60.BranchChatListDao>(
        () => _i61.BranchChatListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i62.CreateChatFolderDao>(
        () => _i63.CreateChatFolderDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i64.FeedbackDao>(
        () => _i65.FeedbackDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i66.TrackLoginUseCase>(
        () => _i66.TrackLoginUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i68.TrackValidInstallUseCase>(
        () => _i68.TrackValidInstallUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i69.TrackOpenLandingPageUseCase>(
        () => _i69.TrackOpenLandingPageUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i70.TrackBannerUseCase>(
        () => _i70.TrackBannerUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i71.TrackOpenAppUseCase>(
        () => _i71.TrackOpenAppUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i72.SendEventUseCase>(
        () => _i72.SendEventUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i73.TrackPopupUseCase>(
        () => _i73.TrackPopupUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i74.TrackRegisterUseCase>(
        () => _i74.TrackRegisterUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i75.TrackNotificationUseCase>(
        () => _i75.TrackNotificationUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i76.PxUnasignedUpdateDao>(
        () => _i77.PxUnasignedUpdateDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i78.AssignTaskDao>(
        () => _i79.AssignTaskDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i80.ConsultationCustomerDao>(
        () => _i81.ConsultationCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i82.MedicalServiceListDao>(
        () => _i83.MedicalServiceListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i84.DevDao>(() => _i85.DevDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i86.AddTagsImageDao>(
        () => _i87.AddTagsImageDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i9.StaticApiService>(
        () => registerModule.collaboratorStaticApiService(
              gh<_i11.Dio>(instanceName: 'StaticApiDio'),
              gh<String>(instanceName: 'StaticApiBaseUrl'),
            ));
    gh.lazySingleton<_i88.MedicalDepartmentListDao>(
        () => _i89.MedicalDepartmentListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i90.NotificationListRepository>(
        () => _i91.NotificationListRepositoryImpl(
              gh<_i18.NotificationListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i92.CustomerProfileDao>(
        () => _i93.CustomerProfileDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i94.ChatListDao>(
        () => _i95.ChatListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i96.ConsultationManagerDao>(
        () => _i97.ConsultationManagerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i98.CreateCustomerDao>(
        () => _i99.CreateCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i100.CustomerBookingInfoDao>(
        () => _i101.CustomerBookingInfoDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i102.NoteDetailsDao>(
        () => _i103.NoteDetailsDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i104.CreateChatGroupDao>(
        () => _i105.CreateChatGroupDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i106.ProductConfirmDao>(
        () => _i107.ProductConfirmDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i108.ListCustomerDao>(
        () => _i109.ListCustomerDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i110.CustomerRecordDao>(
        () => _i111.CustomerRecordDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i112.MedicineDetailDao>(
        () => _i113.MedicineDetailDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i114.ChatSelectBranchDao>(
        () => _i115.ChatSelectBranchDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i116.StoryDetailDao>(
        () => _i117.StoryDetailDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i118.ScheduleDetailsDao>(
        () => _i119.ScheduleDetailsDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i120.ImportantNotesDao>(
        () => _i121.ImportantNotesDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i122.MedicalTemplateListDao>(
        () => _i123.MedicalTemplateListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i124.StoryDetailRepository>(
        () => _i125.StoryDetailRepositoryImpl(
              gh<_i18.StoryDetailApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i126.CreateChatFolderRepository>(
        () => _i127.CreateChatFolderRepositoryImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i128.UserListDao>(
        () => _i129.UserListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i130.HelperRepository>(
        () => _i131.HelperRepositoryImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i132.SelectPxRoomDao>(
        () => _i133.SelectPxRoomDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i134.CustomerInfoDetailsDao>(
        () => _i135.CustomerInfoDetailsDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i136.NotificationListDao>(
        () => _i137.NotificationListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i138.StaffEvaluationPeriodsDao>(
        () => _i139.StaffEvaluationPeriodsDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i140.DetailStaffEvaluationPeriodDao>(
        () => _i141.DetailStaffEvaluationPeriodDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i142.SettingRepository>(
        () => _i143.SettingRepositoryImpl(cache: gh<_i9.EZCache>()));
    gh.lazySingleton<_i144.MediaUploadApiService>(
        () => registerModule.recordApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i145.ChatApiService>(() => registerModule.chatApiService(
          gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
          gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
        ));
    gh.lazySingleton<_i146.SocialApiService>(
        () => registerModule.socialApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.GroupChatDetailApiService>(
        () => registerModule.groupChatDetailApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i147.StickerSocialApiService>(
        () => registerModule.stickerSocialApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i148.TicketDetailDao>(
        () => _i149.TicketDetailDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i150.ChatDao>(
        () => _i151.ChatDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i152.TagListRepository>(
        () => _i153.TagListRepositoryImpl(gh<_i18.TagListApiService>()));
    gh.factory<_i154.SortFolderChatListUseCase>(
        () => _i154.SortFolderChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i155.JoinGroupChatListUseCase>(
        () => _i155.JoinGroupChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i156.GetTotalUnreadChatListUseCase>(() =>
        _i156.GetTotalUnreadChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i157.GetSavedChatListUseCase>(
        () => _i157.GetSavedChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i158.PinConversationChatListUseCase>(() =>
        _i158.PinConversationChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i159.SearchChatListUseCase>(
        () => _i159.SearchChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i160.MarkAsReadChatListUseCase>(
        () => _i160.MarkAsReadChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i161.SearchMessageChatListUseCase>(() =>
        _i161.SearchMessageChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i162.GetConversationByInviteIdChatListUseCase>(() =>
        _i162.GetConversationByInviteIdChatListUseCase(
            gh<_i40.ChatListRepository>()));
    gh.factory<_i163.GetRecentContactsChatListUseCase>(() =>
        _i163.GetRecentContactsChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i164.GetChatListUseCase>(
        () => _i164.GetChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i165.UpdatePinConversationChatListUseCase>(() =>
        _i165.UpdatePinConversationChatListUseCase(
            gh<_i40.ChatListRepository>()));
    gh.factory<_i166.RemoveChatListUseCase>(
        () => _i166.RemoveChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i167.SaveChatListUseCase>(
        () => _i167.SaveChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.lazySingleton<_i168.StoryPersonListRepository>(() =>
        _i169.StoryPersonListRepositoryImpl(
            gh<_i17.StoryPersonListApiService>()));
    gh.lazySingleton<_i170.StoryListRepository>(
        () => _i171.StoryListRepositoryImpl(
              gh<_i14.StoryListApiService>(),
              gh<_i146.SocialApiService>(),
            ));
    gh.lazySingleton<_i18.LocationGoogleApiService>(
        () => registerModule.locationGoogleApiService(
              gh<_i11.Dio>(instanceName: 'ApiGoogleMapDio'),
              gh<String>(instanceName: 'ApiGoogleMapBaseUrl'),
            ));
    gh.lazySingleton<_i172.GroupChatDetailRepository>(
        () => _i173.GroupChatDetailRepositoryImpl(
              gh<_i18.GroupChatDetailApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i174.RemoveStoryDetailUseCase>(() =>
        _i174.RemoveStoryDetailUseCase(gh<_i124.StoryDetailRepository>()));
    gh.factory<_i175.GetSavedStoryDetailUseCase>(() =>
        _i175.GetSavedStoryDetailUseCase(gh<_i124.StoryDetailRepository>()));
    gh.factory<_i176.GetStoryDetailUseCase>(
        () => _i176.GetStoryDetailUseCase(gh<_i124.StoryDetailRepository>()));
    gh.factory<_i177.SaveStoryDetailUseCase>(
        () => _i177.SaveStoryDetailUseCase(gh<_i124.StoryDetailRepository>()));
    gh.lazySingleton<_i178.ChatRepository>(() => _i179.ChatRepositoryImpl(
          gh<_i23.EZCache>(),
          gh<_i145.ChatApiService>(),
        ));
    gh.factory<_i180.GetSavedCreateChatGroupUseCase>(() =>
        _i180.GetSavedCreateChatGroupUseCase(
            gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i181.SaveCreateChatGroupUseCase>(() =>
        _i181.SaveCreateChatGroupUseCase(gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i182.UserLoadCreateChatGroupUseCase>(() =>
        _i182.UserLoadCreateChatGroupUseCase(
            gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i183.GetCreateChatGroupUseCase>(() =>
        _i183.GetCreateChatGroupUseCase(gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i184.RemoveCreateChatGroupUseCase>(() =>
        _i184.RemoveCreateChatGroupUseCase(
            gh<_i44.CreateChatGroupRepository>()));
    gh.lazySingleton<_i185.StickerSocailRepository>(
        () => _i186.StickerSocialRepositoryImpl(
              gh<_i147.StickerSocialApiService>(),
              gh<_i146.SocialApiService>(),
            ));
    gh.lazySingleton<_i9.EformApiService>(() => registerModule.eformApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i9.UserApiService>(
        () => registerModule.collaboratorUserApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.StaffApiService>(() => registerModule.staffApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i9.HomeApiService>(
        () => registerModule.collaboratorHomeApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CustomerApiService>(
        () => registerModule.collaboratorCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CheckinApiService>(
        () => registerModule.checkinApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.NewsApiService>(
        () => registerModule.collaboratornewsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.NotificationApiService>(
        () => registerModule.collaboratorNotificationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.TaskApiService>(() => registerModule.taskApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i9.RequestApiService>(
        () => registerModule.collaboratorRequestApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.TrackingApiService>(
        () => registerModule.trackingApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.BranchSelectionApiService>(
        () => registerModule.branchSelectionApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ImportantNotesApiService>(
        () => registerModule.importantNotesApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CustomerProfileApiService>(
        () => registerModule.customerProfileApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CustomerInfoDetailsApiService>(
        () => registerModule.customerInfoDetailsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.NoteDetailsApiService>(
        () => registerModule.noteDetailsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.MedicalDepartmentListApiService>(
        () => registerModule.medicalDepartmentListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.ListCustomerApiService>(
        () => registerModule.listCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ServiceAndProductApiService>(
        () => registerModule.serviceAndProductApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalServiceLogListApiService>(
        () => registerModule.medicalServiceLogListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalLogDetailApiService>(
        () => registerModule.medicalLogDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicineDetailApiService>(
        () => registerModule.medicineDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalTemplateListApiService>(
        () => registerModule.medicalTemplateListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalProductCreationApiService>(
        () => registerModule.medicalProductCreationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalServiceCreationApiService>(
        () => registerModule.medicalServiceCreationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.FoodApiService>(() => registerModule.foodApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i18.ScheduleDetailsApiService>(
        () => registerModule.scheduleDetailsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerScheduleApiService>(
        () => registerModule.customerScheduleApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerBookingInfoApiService>(
        () => registerModule.customerBookingInfoApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.AssignTaskApiService>(
        () => registerModule.assignTaskApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ChatSelectBranchApiService>(
        () => registerModule.chatSelectBranchApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.BranchChatListApiService>(
        () => registerModule.branchChatListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxListApiService>(
        () => registerModule.pxListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxUnasignedApiService>(
        () => registerModule.pxUnasignedApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxTaskListApiService>(
        () => registerModule.pxTaskListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxUnasignedUpdateApiService>(
        () => registerModule.pxUnasignedUpdateApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.TakingCareCustomerApiService>(
        () => registerModule.takingCareCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxRecheckApiService>(
        () => registerModule.pxRecheckApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CreateCustomerApiService>(
        () => registerModule.createCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.SelectPxRoomApiService>(
        () => registerModule.selectPxRoomApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerListApiService>(
        () => registerModule.customerListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ConsultationManagerApiService>(
        () => registerModule.consultationManagerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ConsultationCustomerApiService>(
        () => registerModule.consultationCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.StaffEvaluationPeriodsApiService>(
        () => registerModule.staffEvaluationPeriodsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.DetailStaffEvaluationPeriodApiService>(
        () => registerModule.detailStaffEvaluationPeriodApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i187.RatingHumanApiService>(
        () => registerModule.ratingHumanApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i188.NoteListApiService>(
        () => registerModule.noteListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.DetailCrmCustomerApiService>(
        () => registerModule.detailCrmCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.HrOrganizationApiService>(
        () => registerModule.hrOrganizationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerRecordApiService>(
        () => registerModule.customerRecordApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CheckinPhotoApiService>(
        () => registerModule.checkinPhotoApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.FeedbackApiService>(
        () => registerModule.feedbackApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i189.CreateChatGroupApiService>(
        () => registerModule.createChatGroupApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.UserListApiService>(
        () => registerModule.userListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CreateChatFolderApiService>(
        () => registerModule.createChatFolderApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i190.TicketApiService>(
        () => registerModule.ticketApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.TicketDetailApiService>(
        () => registerModule.ticketDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i191.TicketActiveApiService>(
        () => registerModule.ticketActiveApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.DevApiService>(() => registerModule.devApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i18.KpiEmployeeApiService>(
        () => registerModule.kpiEmployeeApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ProductConfirmApiService>(
        () => registerModule.productConfirmApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i192.TagImageApiService>(
        () => registerModule.tagImageApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.AddTagsImageApiService>(
        () => registerModule.addTagsImageApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.factory<_i18.MedicalServiceListApiService>(
        () => registerModule.medicalServiceListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i193.SelectPxRoomRepository>(
        () => _i194.SelectPxRoomRepositoryImpl(
              gh<_i18.SelectPxRoomApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i195.LikeListRepository>(
        () => _i196.LikeListRepositoryImpl(gh<_i16.LikeListApiService>()));
    gh.lazySingleton<_i197.FoodRepository>(() => _i198.FoodRepositoryImpl(
          gh<_i9.FoodApiService>(),
          gh<_i144.MediaUploadApiService>(),
        ));
    gh.lazySingleton<_i199.BranchSelectionRepository>(
        () => _i200.BranchSelectionRepositoryImpl(
              gh<_i9.BranchSelectionApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i201.TicketActiveRepository>(() =>
        _i202.TicketActiveRepositoryImpl(gh<_i191.TicketActiveApiService>()));
    gh.lazySingleton<_i203.MedicalDepartmentListRepository>(
        () => _i204.MedicalDepartmentListRepositoryImpl(
              gh<_i9.MedicalDepartmentListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i205.DetailStaffEvaluationPeriodRepository>(
        () => _i206.DetailStaffEvaluationPeriodRepositoryImpl(
              gh<_i18.DetailStaffEvaluationPeriodApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i207.ImportantNotesRepository>(
        () => _i208.ImportantNotesRepositoryImpl(
              gh<_i18.ImportantNotesApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i209.UserTickerRepository>(() =>
        _i210.StaffEvaluationPeriodsRepositoryImpl(
            gh<_i188.NoteListApiService>()));
    gh.lazySingleton<_i211.MedicineDetailRepository>(
        () => _i212.MedicineDetailRepositoryImpl(
              gh<_i18.MedicineDetailApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i213.GetSavedChatUseCase>(
        () => _i213.GetSavedChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i214.MessageEditChatUseCase>(
        () => _i214.MessageEditChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i215.GetUserSeenChatUseCase>(
        () => _i215.GetUserSeenChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i216.TranscribeChatUseCase>(
        () => _i216.TranscribeChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i217.SearchChatUseCase>(
        () => _i217.SearchChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i218.GetConversationChatUseCase>(
        () => _i218.GetConversationChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i219.GetPinListChatUseCase>(
        () => _i219.GetPinListChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i220.SaveChatUseCase>(
        () => _i220.SaveChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i221.VotePollChatUseCase>(
        () => _i221.VotePollChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i222.GetUserStickerChatUseCase>(
        () => _i222.GetUserStickerChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i223.UploadFileChatUseCase>(
        () => _i223.UploadFileChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i224.UnpinMessageChatUseCase>(
        () => _i224.UnpinMessageChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i225.ReplyBotMessageChatUseCase>(
        () => _i225.ReplyBotMessageChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i226.ReactChatUseCase>(
        () => _i226.ReactChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i227.GetConversationByIdUseCase>(
        () => _i227.GetConversationByIdUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i228.SendChatUseCase>(
        () => _i228.SendChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i229.PinMessageChatUseCase>(
        () => _i229.PinMessageChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i230.UpdatePollChatUseCase>(
        () => _i230.UpdatePollChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i231.GetChatUseCase>(
        () => _i231.GetChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i232.MessageRemoveChatUseCase>(
        () => _i232.MessageRemoveChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i233.RemoveChatUseCase>(
        () => _i233.RemoveChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i234.ConversationDetailsUpdateChatUseCase>(() =>
        _i234.ConversationDetailsUpdateChatUseCase(gh<_i178.ChatRepository>()));
    gh.factory<_i235.CacheTextScaleRemoveUseCase>(
        () => _i235.CacheTextScaleRemoveUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i236.CacheTextScaleSaveUseCase>(
        () => _i236.CacheTextScaleSaveUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i237.SaveDeviceInfoUseCase>(
        () => _i237.SaveDeviceInfoUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i238.SaveLatitudeUseCase>(
        () => _i238.SaveLatitudeUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i239.DeleteLongitudeUseCase>(
        () => _i239.DeleteLongitudeUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i240.DeleteLatitudeUseCase>(
        () => _i240.DeleteLatitudeUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i241.GetKeyAppsFlyerUseCase>(
        () => _i241.GetKeyAppsFlyerUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i242.SaveAppVersionUseCase>(
        () => _i242.SaveAppVersionUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i243.SaveLongitudeUseCase>(
        () => _i243.SaveLongitudeUseCase(gh<_i130.HelperRepository>()));
    gh.factory<_i244.SaveKeyAppflyerUseCase>(
        () => _i244.SaveKeyAppflyerUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i245.CacheTextScaleGetUseCase>(
        () => _i245.CacheTextScaleGetUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i246.GetPhoneUseCase>(
        () => _i246.GetPhoneUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i247.GetAppVersionUseCase>(
        () => _i247.GetAppVersionUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i248.GetLatitudeUseCase>(
        () => _i248.GetLatitudeUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i249.GetAccessTokenUseCase>(
        () => _i249.GetAccessTokenUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i250.GetFirebaseTokenUseCase>(
        () => _i250.GetFirebaseTokenUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i251.GetLongitudeUseCase>(
        () => _i251.GetLongitudeUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i252.GetDeviceInfoUseCase>(
        () => _i252.GetDeviceInfoUseCase(gh<_i130.HelperRepository>()));
    gh.lazySingleton<_i253.CheckinPhotoRepository>(
        () => _i254.CheckinPhotoRepositoryImpl(
              gh<_i18.CheckinPhotoApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i255.PxTaskListRepository>(
        () => _i256.PxTaskListRepositoryImpl(
              gh<_i18.PxTaskListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i257.TicketRepository>(() => _i258.TicketRepositoryImpl(
          gh<_i190.TicketApiService>(),
          gh<_i144.MediaUploadApiService>(),
        ));
    gh.lazySingleton<_i259.RatingHumanRepository>(() =>
        _i260.RatingHumanRepositoryImpl(gh<_i187.RatingHumanApiService>()));
    gh.factory<_i261.RemoveNotificationListUseCase>(() =>
        _i261.RemoveNotificationListUseCase(
            gh<_i90.NotificationListRepository>()));
    gh.factory<_i262.GetNotificationListUseCase>(() =>
        _i262.GetNotificationListUseCase(
            gh<_i90.NotificationListRepository>()));
    gh.factory<_i263.PutReadAllSocialUseCase>(() =>
        _i263.PutReadAllSocialUseCase(gh<_i90.NotificationListRepository>()));
    gh.factory<_i264.DeleteNotificationSocialUseCase>(() =>
        _i264.DeleteNotificationSocialUseCase(
            gh<_i90.NotificationListRepository>()));
    gh.factory<_i265.SaveNotificationListUseCase>(() =>
        _i265.SaveNotificationListUseCase(
            gh<_i90.NotificationListRepository>()));
    gh.factory<_i266.GetSavedNotificationListUseCase>(() =>
        _i266.GetSavedNotificationListUseCase(
            gh<_i90.NotificationListRepository>()));
    gh.lazySingleton<_i267.UserRepository>(() => _i268.UserRepositoryImpl(
          gh<_i9.UserApiService>(),
          gh<_i9.StaticApiService>(),
          gh<_i9.EZCache>(),
        ));
    gh.lazySingleton<_i269.ConsultationCustomerRepository>(
        () => _i270.ConsultationCustomerRepositoryImpl(
              gh<_i18.ConsultationCustomerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i271.GetTagListUseCase>(
        () => _i271.GetTagListUseCase(gh<_i152.TagListRepository>()));
    gh.lazySingleton<_i272.FeedbackRepository>(
        () => _i273.FeedbackRepositoryImpl(
              gh<_i18.FeedbackApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i274.AddTagsImageRepository>(
        () => _i275.AddTagsImageRepositoryImpl(
              gh<_i18.AddTagsImageApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i276.GetUserExceptionGroupChatDetailUseCase>(() =>
        _i276.GetUserExceptionGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i277.AvatarUploadGroupChatDetailUseCase>(() =>
        _i277.AvatarUploadGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i278.RemoveGroupChatDetailUseCase>(() =>
        _i278.RemoveGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i279.MemberInfoLoadGroupChatDetailUseCase>(() =>
        _i279.MemberInfoLoadGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i280.DeleteGroupUseCase>(
        () => _i280.DeleteGroupUseCase(gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i281.MediaLoadGroupChatDetailUseCase>(() =>
        _i281.MediaLoadGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i282.UpdateMemberRuleGroupChatDetailUseCase>(() =>
        _i282.UpdateMemberRuleGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i283.GetGroupChatDetailUseCase>(() =>
        _i283.GetGroupChatDetailUseCase(gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i284.ChangeOwnerGroupChatDetailUseCase>(() =>
        _i284.ChangeOwnerGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i285.LinkLoadGroupChatDetailUseCase>(() =>
        _i285.LinkLoadGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i286.UpdateAdminRuleGroupChatDetailUseCase>(() =>
        _i286.UpdateAdminRuleGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i287.GetUserRulesGroupChatDetailUseCase>(() =>
        _i287.GetUserRulesGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i288.FileLoadGroupChatDetailUseCase>(() =>
        _i288.FileLoadGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i289.GetRuleByRoleGroupChatDetailUseCase>(() =>
        _i289.GetRuleByRoleGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i290.UpdateGroupChatDetailUseCase>(() =>
        _i290.UpdateGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i291.GetSavedGroupChatDetailUseCase>(() =>
        _i291.GetSavedGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.factory<_i292.SaveGroupChatDetailUseCase>(() =>
        _i292.SaveGroupChatDetailUseCase(
            gh<_i172.GroupChatDetailRepository>()));
    gh.lazySingleton<_i293.CustomerRepository>(
        () => _i294.CustomerRepositoryImpl(
              gh<_i9.CustomerApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i295.CreateTreatmentOMDetailUseCase>(() =>
        _i295.CreateTreatmentOMDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i296.DeleteResultOfFitUseCase>(() =>
        _i296.DeleteResultOfFitUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i297.GetServiceInsideTicketUseCase>(() =>
        _i297.GetServiceInsideTicketUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i298.GetConsultationNDTVUseCase>(() =>
        _i298.GetConsultationNDTVUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i299.GetResultListOfFitUseCase>(() =>
        _i299.GetResultListOfFitUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i300.GetSkinCustomerInfoUseCase>(() =>
        _i300.GetSkinCustomerInfoUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i301.RemoveConsultationCustomerUseCase>(() =>
        _i301.RemoveConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i302.ProductLoadConsultationCustomerUseCase>(() =>
        _i302.ProductLoadConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i303.CreateTreatmentDetailUseCase>(() =>
        _i303.CreateTreatmentDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i304.GetFitCustomerInfoUseCase>(() =>
        _i304.GetFitCustomerInfoUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i305.GetTreatmentNoteUseCase>(() =>
        _i305.GetTreatmentNoteUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i306.GetServiceUsageConsultationCustomerUseCase>(() =>
        _i306.GetServiceUsageConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i307.GetServiceConsultationCustomerUseCase>(() =>
        _i307.GetServiceConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i308.GetTreatmentOMDetailUseCase>(() =>
        _i308.GetTreatmentOMDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i309.GetResultOfFitUseCase>(() => _i309.GetResultOfFitUseCase(
        gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i310.GetSavedConsultationCustomerUseCase>(() =>
        _i310.GetSavedConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i311.UpdateSkinCustomerInfoUseCase>(() =>
        _i311.UpdateSkinCustomerInfoUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i312.GetConsultationCustomerUseCase>(() =>
        _i312.GetConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i313.UpdateFitCustomerInfoUseCase>(() =>
        _i313.UpdateFitCustomerInfoUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i314.SaveConsultationCustomerUseCase>(() =>
        _i314.SaveConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i315.UpdateTreatmentNoteUseCase>(() =>
        _i315.UpdateTreatmentNoteUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i316.GetActionConsultationCustomerUseCase>(() =>
        _i316.GetActionConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i317.UpdateResultOfFitUseCase>(() =>
        _i317.UpdateResultOfFitUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i318.UpdateConsultationTTBDUseCase>(() =>
        _i318.UpdateConsultationTTBDUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i319.CompleteConsultationCustomerUseCase>(() =>
        _i319.CompleteConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i320.EditServiceConsultationCustomerUseCase>(() =>
        _i320.EditServiceConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i321.UpdateTreatmentDetailUseCase>(() =>
        _i321.UpdateTreatmentDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i322.RemoveServiceConsultationCustomerUseCase>(() =>
        _i322.RemoveServiceConsultationCustomerUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i323.GetTreatmentDetailUseCase>(() =>
        _i323.GetTreatmentDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i324.GetComboServiceUseCase>(() => _i324.GetComboServiceUseCase(
        gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i325.GetDealServiceUseCase>(() => _i325.GetDealServiceUseCase(
        gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i326.UpdateComboServiceUseCase>(() =>
        _i326.UpdateComboServiceUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i327.CreateComboServiceUseCase>(() =>
        _i327.CreateComboServiceUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i328.GetServiceFromAccUseCase>(() =>
        _i328.GetServiceFromAccUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i329.GetServiceTypePurchaseDetailUseCase>(() =>
        _i329.GetServiceTypePurchaseDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i330.OrderFoodUploadUseCase>(
        () => _i330.OrderFoodUploadUseCase(gh<_i197.FoodRepository>()));
    gh.lazySingleton<_i331.CustomerRecordRepository>(
        () => _i332.CustomerRecordRepositoryImpl(
              gh<_i18.CustomerRecordApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i333.RemoveCreateChatFolderUseCase>(() =>
        _i333.RemoveCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i334.GetSavedCreateChatFolderUseCase>(() =>
        _i334.GetSavedCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i335.LoadCreateChatFolderUseCase>(() =>
        _i335.LoadCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i336.GetCreateChatFolderUseCase>(() =>
        _i336.GetCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i337.SaveCreateChatFolderUseCase>(() =>
        _i337.SaveCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i338.ConversationLoadCreateChatFolderUseCase>(() =>
        _i338.ConversationLoadCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i339.UpdateCreateChatFolderUseCase>(() =>
        _i339.UpdateCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.factory<_i340.RemoveFolderCreateChatFolderUseCase>(() =>
        _i340.RemoveFolderCreateChatFolderUseCase(
            gh<_i126.CreateChatFolderRepository>()));
    gh.lazySingleton<_i341.ChatSelectBranchRepository>(
        () => _i342.ChatSelectBranchRepositoryImpl(
              gh<_i18.ChatSelectBranchApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i343.UploadStickerSetUseCase>(() =>
        _i343.UploadStickerSetUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i344.GetStickerOnlySetUseCase>(() =>
        _i344.GetStickerOnlySetUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i345.GetStickerRecentUseCase>(() =>
        _i345.GetStickerRecentUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i346.RemoveStickerUseCase>(
        () => _i346.RemoveStickerUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i347.UpdateStickerRecentUseCase>(() =>
        _i347.UpdateStickerRecentUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i348.GetStickerSetUseCase>(
        () => _i348.GetStickerSetUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i349.CreateStickerListUseCase>(() =>
        _i349.CreateStickerListUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i350.UploadStickerUseCase>(
        () => _i350.UploadStickerUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i351.GetStickerListUseCase>(
        () => _i351.GetStickerListUseCase(gh<_i185.StickerSocailRepository>()));
    gh.factory<_i352.RemoveStickerSetUseCase>(() =>
        _i352.RemoveStickerSetUseCase(gh<_i185.StickerSocailRepository>()));
    gh.lazySingleton<_i353.PxListRepository>(() => _i354.PxListRepositoryImpl(
          gh<_i18.PxListApiService>(),
          gh<_i23.EZCache>(),
        ));
    gh.lazySingleton<_i355.TaskRepository>(
        () => _i356.TaskRepositoryImpl(gh<_i9.TaskApiService>()));
    gh.lazySingleton<_i357.DevRepository>(() => _i358.DevRepositoryImpl(
          gh<_i18.DevApiService>(),
          gh<_i23.EZCache>(),
        ));
    gh.lazySingleton<_i359.LocationGoogleRepository>(() =>
        _i360.LocationGoogleRepositoryImpl(
            gh<_i18.LocationGoogleApiService>()));
    gh.factory<_i361.SaveProfileUseCase>(
        () => _i361.SaveProfileUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i362.SaveNavigationUseCase>(
        () => _i362.SaveNavigationUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i363.SaveEnableOnlineLoggerUseCase>(
        () => _i363.SaveEnableOnlineLoggerUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i364.SetOnboardingUseCase>(
        () => _i364.SetOnboardingUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i365.HasUserDataUseCase>(
        () => _i365.HasUserDataUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i366.PersistTokenUseCase>(
        () => _i366.PersistTokenUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i367.DeleteTokenUseCase>(
        () => _i367.DeleteTokenUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i368.ClearCacheUseCase>(
        () => _i368.ClearCacheUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i369.DeleteProfileUseCase>(
        () => _i369.DeleteProfileUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i370.SaveAccountUseCase>(
        () => _i370.SaveAccountUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i371.DeleteHomeMenuCacheUseCase>(
        () => _i371.DeleteHomeMenuCacheUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i372.CheckinUseCase>(
        () => _i372.CheckinUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i373.MedicalServiceListRepository>(
        () => _i374.MedicalServiceListRepositoryImpl(
              gh<_i18.MedicalServiceListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i375.ProductConfirmRepository>(() =>
        _i376.ProductConfirmRepositoryImpl(
            gh<_i18.ProductConfirmApiService>()));
    gh.factory<_i377.UniversalQrScanUseCase>(
        () => _i377.UniversalQrScanUseCase(gh<_i197.FoodRepository>()));
    gh.lazySingleton<_i378.DetailCrmCustomerRepository>(
        () => _i379.DetailCrmCustomerRepositoryImpl(
              gh<_i18.DetailCrmCustomerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i380.ServiceTypePurchaseBloc>(
        () => _i380.ServiceTypePurchaseBloc(
              gh<_i324.GetComboServiceUseCase>(),
              gh<_i325.GetDealServiceUseCase>(),
              gh<_i328.GetServiceFromAccUseCase>(),
              gh<_i327.CreateComboServiceUseCase>(),
              gh<_i326.UpdateComboServiceUseCase>(),
              gh<_i328.GetServiceFromAccUseCase>(),
            ));
    gh.lazySingleton<_i381.TakingCareCustomerRepository>(
        () => _i382.TakingCareCustomerRepositoryImpl(
              gh<_i18.TakingCareCustomerApiService>(),
              gh<_i23.EZCache>(),
              gh<_i144.MediaUploadApiService>(),
            ));
    gh.factory<_i383.GetSavedMedicalDepartmentListUseCase>(() =>
        _i383.GetSavedMedicalDepartmentListUseCase(
            gh<_i203.MedicalDepartmentListRepository>()));
    gh.factory<_i384.GetMedicalDepartmentListUseCase>(() =>
        _i384.GetMedicalDepartmentListUseCase(
            gh<_i203.MedicalDepartmentListRepository>()));
    gh.factory<_i385.SaveMedicalDepartmentListUseCase>(() =>
        _i385.SaveMedicalDepartmentListUseCase(
            gh<_i203.MedicalDepartmentListRepository>()));
    gh.factory<_i386.RemoveMedicalDepartmentListUseCase>(() =>
        _i386.RemoveMedicalDepartmentListUseCase(
            gh<_i203.MedicalDepartmentListRepository>()));
    gh.lazySingleton<_i387.MediaUploadRepository>(() =>
        _i388.MediaUploadRepositoryImpl(gh<_i144.MediaUploadApiService>()));
    gh.lazySingleton<_i389.CustomerBookingInfoRepository>(
        () => _i390.CustomerBookingInfoRepositoryImpl(
              gh<_i18.CustomerBookingInfoApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i391.CustomerListRepository>(
        () => _i392.CustomerListRepositoryImpl(
              gh<_i18.CustomerListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i393.PxRecheckRepository>(
        () => _i394.PxRecheckRepositoryImpl(
              gh<_i18.PxRecheckApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i395.TicketDetailRepository>(
        () => _i396.TicketDetailRepositoryImpl(
              gh<_i18.TicketDetailApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i397.PostLikeStoryUseCase>(
        () => _i397.PostLikeStoryUseCase(gh<_i195.LikeListRepository>()));
    gh.factory<_i398.PostLikeCommentUseCase>(
        () => _i398.PostLikeCommentUseCase(gh<_i195.LikeListRepository>()));
    gh.factory<_i399.GetLikeListUseCase>(
        () => _i399.GetLikeListUseCase(gh<_i195.LikeListRepository>()));
    gh.lazySingleton<_i400.NewsRepository>(
        () => _i401.NewsRepositoryImpl(gh<_i9.NewsApiService>()));
    gh.lazySingleton<_i402.CreateCustomerRepository>(
        () => _i403.CreateCustomerRepositoryImpl(
              gh<_i18.CreateCustomerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i404.CheckinRepository>(
        () => _i405.CheckinRepositoryImpl(gh<_i9.CheckinApiService>()));
    gh.factory<_i406.CreatingTaskUseCase>(
        () => _i406.CreatingTaskUseCase(gh<_i355.TaskRepository>()));
    gh.factory<_i407.GetRepeatTaskUseCase>(
        () => _i407.GetRepeatTaskUseCase(gh<_i355.TaskRepository>()));
    gh.factory<_i408.GetJobSchedulerUseCase>(
        () => _i408.GetJobSchedulerUseCase(gh<_i355.TaskRepository>()));
    gh.factory<_i409.SubmitJobSchedulerUseCase>(
        () => _i409.SubmitJobSchedulerUseCase(gh<_i355.TaskRepository>()));
    gh.factory<_i410.GetGeneralJobSchedulerUseCase>(
        () => _i410.GetGeneralJobSchedulerUseCase(gh<_i355.TaskRepository>()));
    gh.factory<_i411.GetDetailJobSchedulerUseCase>(
        () => _i411.GetDetailJobSchedulerUseCase(gh<_i355.TaskRepository>()));
    gh.lazySingleton<_i412.CustomerProfileRepository>(
        () => _i413.CustomerProfileRepositoryImpl(
              gh<_i9.CustomerProfileApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i414.GetStoryPersonListUseCase>(() =>
        _i414.GetStoryPersonListUseCase(gh<_i168.StoryPersonListRepository>()));
    gh.factory<_i415.GetStoryPersonListUserUseCase>(() =>
        _i415.GetStoryPersonListUserUseCase(
            gh<_i168.StoryPersonListRepository>()));
    gh.factory<_i416.SaveFontOptionUseCase>(
        () => _i416.SaveFontOptionUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i417.GetLanguageOptionUseCase>(
        () => _i417.GetLanguageOptionUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i418.GetThemeOptionUseCase>(
        () => _i418.GetThemeOptionUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i419.SaveLanguageOptionUseCase>(
        () => _i419.SaveLanguageOptionUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i420.SaveThemeOptionUseCase>(
        () => _i420.SaveThemeOptionUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i421.IsDarkModeUseCase>(
        () => _i421.IsDarkModeUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i422.GetFontOptionUseCase>(
        () => _i422.GetFontOptionUseCase(gh<_i142.SettingRepository>()));
    gh.factory<_i423.IsLightModeUseCase>(
        () => _i423.IsLightModeUseCase(gh<_i142.SettingRepository>()));
    gh.lazySingleton<_i424.NoteDetailsRepository>(
        () => _i425.NoteDetailsRepositoryImpl(
              gh<_i18.NoteDetailsApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i426.RepeatTaskBloc>(
        () => _i426.RepeatTaskBloc(gh<_i407.GetRepeatTaskUseCase>()));
    gh.lazySingleton<_i427.BranchChatListRepository>(
        () => _i428.BranchChatListRepositoryImpl(
              gh<_i18.BranchChatListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i429.GetSelectPxRoomUseCase>(
        () => _i429.GetSelectPxRoomUseCase(gh<_i193.SelectPxRoomRepository>()));
    gh.factory<_i430.RemoveSelectPxRoomUseCase>(() =>
        _i430.RemoveSelectPxRoomUseCase(gh<_i193.SelectPxRoomRepository>()));
    gh.factory<_i431.RoomChangeSelectPxRoomUseCase>(() =>
        _i431.RoomChangeSelectPxRoomUseCase(
            gh<_i193.SelectPxRoomRepository>()));
    gh.factory<_i432.GetSavedSelectPxRoomUseCase>(() =>
        _i432.GetSavedSelectPxRoomUseCase(gh<_i193.SelectPxRoomRepository>()));
    gh.factory<_i433.SaveSelectPxRoomUseCase>(() =>
        _i433.SaveSelectPxRoomUseCase(gh<_i193.SelectPxRoomRepository>()));
    gh.lazySingleton<_i434.ListCustomerRepository>(
        () => _i435.ListCustomerRepositoryImpl(
              gh<_i9.ListCustomerApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i436.ChatBloc>(() => _i436.ChatBloc(
          gh<_i231.GetChatUseCase>(),
          gh<_i220.SaveChatUseCase>(),
          gh<_i213.GetSavedChatUseCase>(),
          gh<_i233.RemoveChatUseCase>(),
          gh<_i228.SendChatUseCase>(),
          gh<_i223.UploadFileChatUseCase>(),
          gh<_i234.ConversationDetailsUpdateChatUseCase>(),
          gh<_i226.ReactChatUseCase>(),
          gh<_i227.GetConversationByIdUseCase>(),
          gh<_i232.MessageRemoveChatUseCase>(),
          gh<_i214.MessageEditChatUseCase>(),
          gh<_i218.GetConversationChatUseCase>(),
          gh<_i229.PinMessageChatUseCase>(),
          gh<_i224.UnpinMessageChatUseCase>(),
          gh<_i219.GetPinListChatUseCase>(),
          gh<_i230.UpdatePollChatUseCase>(),
          gh<_i221.VotePollChatUseCase>(),
          gh<_i215.GetUserSeenChatUseCase>(),
          gh<_i217.SearchChatUseCase>(),
          gh<_i216.TranscribeChatUseCase>(),
          gh<_i225.ReplyBotMessageChatUseCase>(),
          gh<_i222.GetUserStickerChatUseCase>(),
          gh<_i287.GetUserRulesGroupChatDetailUseCase>(),
        ));
    gh.lazySingleton<_i437.ScheduleDetailsRepository>(
        () => _i438.ScheduleDetailsRepositoryImpl(
              gh<_i439.ScheduleDetailsApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i440.TicketCreatedTypeUseCase>(
        () => _i440.TicketCreatedTypeUseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i441.GetTicketAllGroupUseCase>(
        () => _i441.GetTicketAllGroupUseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i442.CreateTicketUseCase>(
        () => _i442.CreateTicketUseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i443.GetTicketTicketv2UseCase>(
        () => _i443.GetTicketTicketv2UseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i444.TicketUploadFileUseCase>(
        () => _i444.TicketUploadFileUseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i445.GetMyTicketTicketv2UseCase>(
        () => _i445.GetMyTicketTicketv2UseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i446.UpdateTicketUseCase>(
        () => _i446.UpdateTicketUseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i447.GetTicketAllTypeUseCase>(
        () => _i447.GetTicketAllTypeUseCase(gh<_i257.TicketRepository>()));
    gh.factory<_i448.TicketGroupTypeUseCase>(
        () => _i448.TicketGroupTypeUseCase(gh<_i257.TicketRepository>()));
    gh.lazySingleton<_i449.StaffEvaluationPeriodsRepository>(
        () => _i450.StaffEvaluationPeriodsRepositoryImpl(
              gh<_i18.StaffEvaluationPeriodsApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i451.PxUnasignedRepository>(
        () => _i452.PxUnasignedRepositoryImpl(
              gh<_i18.PxUnasignedApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i453.GetDropDownStatusUseCase>(
        () => _i453.GetDropDownStatusUseCase(gh<_i209.UserTickerRepository>()));
    gh.factory<_i454.GetUserTicketUseCase>(
        () => _i454.GetUserTicketUseCase(gh<_i209.UserTickerRepository>()));
    gh.lazySingleton<_i455.StaffRepository>(
        () => _i456.StaffRepositoryImpl(gh<_i9.StaffApiService>()));
    gh.factory<_i457.RemoveCustomerRecordUseCase>(() =>
        _i457.RemoveCustomerRecordUseCase(
            gh<_i331.CustomerRecordRepository>()));
    gh.factory<_i458.GetSavedCustomerRecordUseCase>(() =>
        _i458.GetSavedCustomerRecordUseCase(
            gh<_i331.CustomerRecordRepository>()));
    gh.factory<_i459.GetCustomerRecordUseCase>(() =>
        _i459.GetCustomerRecordUseCase(gh<_i331.CustomerRecordRepository>()));
    gh.factory<_i460.SaveCustomerRecordUseCase>(() =>
        _i460.SaveCustomerRecordUseCase(gh<_i331.CustomerRecordRepository>()));
    gh.lazySingleton<_i461.MedicalProductCreationRepository>(
        () => _i462.MedicalProductCreationRepositoryImpl(
              gh<_i18.MedicalProductCreationApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i463.HomeRepository>(
        () => _i464.HomeRepositoryImpl(gh<_i9.HomeApiService>()));
    gh.factory<_i465.GetEmojiListUseCase>(
        () => _i465.GetEmojiListUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i466.GetStoryListUseCase>(
        () => _i466.GetStoryListUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i467.GetStoryListSearchUseCase>(
        () => _i467.GetStoryListSearchUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i468.PostStoryUseCase>(
        () => _i468.PostStoryUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i469.PutStoryVoteUseCase>(
        () => _i469.PutStoryVoteUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i470.GetTotalNotificationUseCase>(() =>
        _i470.GetTotalNotificationUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i471.UpdateStoryUseCase>(
        () => _i471.UpdateStoryUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i472.SocialUploadFileUseCase>(
        () => _i472.SocialUploadFileUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i473.GetVoteUsersUseCase>(
        () => _i473.GetVoteUsersUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i474.DeleteStoryVoteUseCase>(
        () => _i474.DeleteStoryVoteUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i475.DeleteStoryUseCase>(
        () => _i475.DeleteStoryUseCase(gh<_i170.StoryListRepository>()));
    gh.factory<_i476.GetStoryRuleUseCase>(
        () => _i476.GetStoryRuleUseCase(gh<_i170.StoryListRepository>()));
    gh.lazySingleton<_i477.CommentListRepository>(
        () => _i478.CommentListRepositoryImpl(
              gh<_i15.CommentListApiService>(),
              gh<_i146.SocialApiService>(),
            ));
    gh.factory<_i479.GetSavedMedicalServiceListUseCase>(() =>
        _i479.GetSavedMedicalServiceListUseCase(
            gh<_i373.MedicalServiceListRepository>()));
    gh.factory<_i480.SaveMedicalServiceListUseCase>(() =>
        _i480.SaveMedicalServiceListUseCase(
            gh<_i373.MedicalServiceListRepository>()));
    gh.factory<_i481.RemoveMedicalServiceListUseCase>(() =>
        _i481.RemoveMedicalServiceListUseCase(
            gh<_i373.MedicalServiceListRepository>()));
    gh.factory<_i482.GetMedicalServiceListUseCase>(() =>
        _i482.GetMedicalServiceListUseCase(
            gh<_i373.MedicalServiceListRepository>()));
    gh.factory<_i483.CreateChatGroupBloc>(() => _i483.CreateChatGroupBloc(
          gh<_i183.GetCreateChatGroupUseCase>(),
          gh<_i181.SaveCreateChatGroupUseCase>(),
          gh<_i180.GetSavedCreateChatGroupUseCase>(),
          gh<_i184.RemoveCreateChatGroupUseCase>(),
          gh<_i182.UserLoadCreateChatGroupUseCase>(),
          gh<_i223.UploadFileChatUseCase>(),
        ));
    gh.factory<_i484.GetSavedChatSelectBranchUseCase>(() =>
        _i484.GetSavedChatSelectBranchUseCase(
            gh<_i341.ChatSelectBranchRepository>()));
    gh.factory<_i485.RemoveChatSelectBranchUseCase>(() =>
        _i485.RemoveChatSelectBranchUseCase(
            gh<_i341.ChatSelectBranchRepository>()));
    gh.factory<_i486.SaveChatSelectBranchUseCase>(() =>
        _i486.SaveChatSelectBranchUseCase(
            gh<_i341.ChatSelectBranchRepository>()));
    gh.factory<_i487.GetChatSelectBranchUseCase>(() =>
        _i487.GetChatSelectBranchUseCase(
            gh<_i341.ChatSelectBranchRepository>()));
    gh.factory<_i488.SaveDetailStaffEvaluationPeriodUseCase>(() =>
        _i488.SaveDetailStaffEvaluationPeriodUseCase(
            gh<_i205.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i489.EmployeeFetchDetailStaffEvaluationPeriodUseCase>(() =>
        _i489.EmployeeFetchDetailStaffEvaluationPeriodUseCase(
            gh<_i205.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i490.RemoveDetailStaffEvaluationPeriodUseCase>(() =>
        _i490.RemoveDetailStaffEvaluationPeriodUseCase(
            gh<_i205.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i491.GetSavedDetailStaffEvaluationPeriodUseCase>(() =>
        _i491.GetSavedDetailStaffEvaluationPeriodUseCase(
            gh<_i205.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i492.GetDetailStaffEvaluationPeriodUseCase>(() =>
        _i492.GetDetailStaffEvaluationPeriodUseCase(
            gh<_i205.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i493.FetchHistoryCheckinUseCase>(
        () => _i493.FetchHistoryCheckinUseCase(gh<_i404.CheckinRepository>()));
    gh.lazySingleton<_i494.NotificationRepository>(() =>
        _i495.NotificationRepositoryImpl(gh<_i9.NotificationApiService>()));
    gh.lazySingleton<_i496.EformRepository>(
        () => _i497.EformRepositoryImpl(gh<_i9.EformApiService>()));
    gh.factory<_i498.MedicalServiceListBloc>(() => _i498.MedicalServiceListBloc(
          gh<_i482.GetMedicalServiceListUseCase>(),
          gh<_i480.SaveMedicalServiceListUseCase>(),
          gh<_i479.GetSavedMedicalServiceListUseCase>(),
          gh<_i481.RemoveMedicalServiceListUseCase>(),
        ));
    gh.factory<_i499.DetailJobSchedulerBloc>(() => _i499.DetailJobSchedulerBloc(
          gh<_i411.GetDetailJobSchedulerUseCase>(),
          gh<_i409.SubmitJobSchedulerUseCase>(),
        ));
    gh.factory<_i500.FontsBloc>(() => _i500.FontsBloc(
          gh<_i422.GetFontOptionUseCase>(),
          gh<_i416.SaveFontOptionUseCase>(),
          gh<_i245.CacheTextScaleGetUseCase>(),
          gh<_i236.CacheTextScaleSaveUseCase>(),
        ));
    gh.factory<_i501.StoryPersonListBloc>(() => _i501.StoryPersonListBloc(
          gh<_i414.GetStoryPersonListUseCase>(),
          gh<_i475.DeleteStoryUseCase>(),
          gh<_i397.PostLikeStoryUseCase>(),
          gh<_i468.PostStoryUseCase>(),
          gh<_i471.UpdateStoryUseCase>(),
          gh<_i415.GetStoryPersonListUserUseCase>(),
        ));
    gh.factory<_i502.NotificationListBloc>(() => _i502.NotificationListBloc(
          gh<_i262.GetNotificationListUseCase>(),
          gh<_i265.SaveNotificationListUseCase>(),
          gh<_i266.GetSavedNotificationListUseCase>(),
          gh<_i261.RemoveNotificationListUseCase>(),
          gh<_i263.PutReadAllSocialUseCase>(),
          gh<_i264.DeleteNotificationSocialUseCase>(),
        ));
    gh.factory<_i503.GetSavedImportantNotesUseCase>(() =>
        _i503.GetSavedImportantNotesUseCase(
            gh<_i207.ImportantNotesRepository>()));
    gh.factory<_i504.SaveImportantNotesUseCase>(() =>
        _i504.SaveImportantNotesUseCase(gh<_i207.ImportantNotesRepository>()));
    gh.factory<_i505.GetNoteCategoryImportantNotesUseCase>(() =>
        _i505.GetNoteCategoryImportantNotesUseCase(
            gh<_i207.ImportantNotesRepository>()));
    gh.factory<_i506.GetImportantNotesUseCase>(() =>
        _i506.GetImportantNotesUseCase(gh<_i207.ImportantNotesRepository>()));
    gh.factory<_i507.RemoveImportantNotesUseCase>(() =>
        _i507.RemoveImportantNotesUseCase(
            gh<_i207.ImportantNotesRepository>()));
    gh.lazySingleton<_i508.ServiceAndProductRepository>(
        () => _i509.ServiceAndProductRepositoryImpl(
              gh<_i18.ServiceAndProductApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i510.RemoveStaffEvaluationPeriodsUseCase>(() =>
        _i510.RemoveStaffEvaluationPeriodsUseCase(
            gh<_i449.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i511.SaveStaffEvaluationPeriodsUseCase>(() =>
        _i511.SaveStaffEvaluationPeriodsUseCase(
            gh<_i449.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i512.GetSavedStaffEvaluationPeriodsUseCase>(() =>
        _i512.GetSavedStaffEvaluationPeriodsUseCase(
            gh<_i449.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i513.GetStaffEvaluationPeriodsUseCase>(() =>
        _i513.GetStaffEvaluationPeriodsUseCase(
            gh<_i449.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i514.GetCustomerInfoByQrUseCase>(
        () => _i514.GetCustomerInfoByQrUseCase(gh<_i293.CustomerRepository>()));
    gh.factory<_i515.GetCustomerInfoUseCase>(
        () => _i515.GetCustomerInfoUseCase(gh<_i293.CustomerRepository>()));
    gh.factory<_i516.GetCustomerRoomCodeUseCase>(
        () => _i516.GetCustomerRoomCodeUseCase(gh<_i293.CustomerRepository>()));
    gh.lazySingleton<_i517.MedicalLogDetailRepository>(
        () => _i518.MedicalLogDetailRepositoryImpl(
              gh<_i18.MedicalLogDetailApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i519.ProductsMedicalProductCreationUseCase>(() =>
        _i519.ProductsMedicalProductCreationUseCase(
            gh<_i461.MedicalProductCreationRepository>()));
    gh.factory<_i520.GetSavedMedicalProductCreationUseCase>(() =>
        _i520.GetSavedMedicalProductCreationUseCase(
            gh<_i461.MedicalProductCreationRepository>()));
    gh.factory<_i521.RemoveMedicalProductCreationUseCase>(() =>
        _i521.RemoveMedicalProductCreationUseCase(
            gh<_i461.MedicalProductCreationRepository>()));
    gh.factory<_i522.SaveMedicalProductCreationUseCase>(() =>
        _i522.SaveMedicalProductCreationUseCase(
            gh<_i461.MedicalProductCreationRepository>()));
    gh.factory<_i523.MedicalProductCreationUseCase>(() =>
        _i523.MedicalProductCreationUseCase(
            gh<_i461.MedicalProductCreationRepository>()));
    gh.lazySingleton<_i524.CustomerScheduleRepository>(
        () => _i525.CustomerScheduleRepositoryImpl(
              gh<_i18.CustomerScheduleApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i526.KpiEmployeeRepository>(() =>
        _i527.KpiEmployeeRepositoryImpl(gh<_i18.KpiEmployeeApiService>()));
    gh.factory<_i528.RemovePxTaskListUseCase>(
        () => _i528.RemovePxTaskListUseCase(gh<_i255.PxTaskListRepository>()));
    gh.factory<_i529.GetSavedPxTaskListUseCase>(() =>
        _i529.GetSavedPxTaskListUseCase(gh<_i255.PxTaskListRepository>()));
    gh.factory<_i530.SavePxTaskListUseCase>(
        () => _i530.SavePxTaskListUseCase(gh<_i255.PxTaskListRepository>()));
    gh.factory<_i531.GetPxTaskListUseCase>(
        () => _i531.GetPxTaskListUseCase(gh<_i255.PxTaskListRepository>()));
    gh.factory<_i532.ChatSelectBranchBloc>(() => _i532.ChatSelectBranchBloc(
          gh<_i487.GetChatSelectBranchUseCase>(),
          gh<_i486.SaveChatSelectBranchUseCase>(),
          gh<_i485.RemoveChatSelectBranchUseCase>(),
        ));
    gh.factory<_i533.OrderFoodDeleteUseCase>(
        () => _i533.OrderFoodDeleteUseCase(gh<_i197.FoodRepository>()));
    gh.factory<_i534.OrderFoodGetUseCase>(
        () => _i534.OrderFoodGetUseCase(gh<_i197.FoodRepository>()));
    gh.factory<_i535.OrderFoodCreatedUseCase>(
        () => _i535.OrderFoodCreatedUseCase(gh<_i197.FoodRepository>()));
    gh.factory<_i536.OrderFoodCreateReportUseCase>(
        () => _i536.OrderFoodCreateReportUseCase(gh<_i197.FoodRepository>()));
    gh.factory<_i537.SetDefaultAddressFoodUseCase>(
        () => _i537.SetDefaultAddressFoodUseCase(gh<_i197.FoodRepository>()));
    gh.factory<_i538.RoomLoadDetailCrmCustomerUseCase>(() =>
        _i538.RoomLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i539.PromotionLoadDetailCrmCustomerUseCase>(() =>
        _i539.PromotionLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i540.ServiceFetchDetailCrmCustomerUseCase>(() =>
        _i540.ServiceFetchDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i541.AdviceFetchDetailCrmCustomerUseCase>(() =>
        _i541.AdviceFetchDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i542.GetDetailCrmCustomerUseCase>(() =>
        _i542.GetDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i543.SaveDetailCrmCustomerUseCase>(() =>
        _i543.SaveDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i544.ServiceLoadDetailCrmCustomerUseCase>(() =>
        _i544.ServiceLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i545.RemoveDetailCrmCustomerUseCase>(() =>
        _i545.RemoveDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i546.CallLogFetchDetailCrmCustomerUseCase>(() =>
        _i546.CallLogFetchDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i547.BranchLoadDetailCrmCustomerUseCase>(() =>
        _i547.BranchLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i548.GetSavedDetailCrmCustomerUseCase>(() =>
        _i548.GetSavedDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i549.BookingLogFetchDetailCrmCustomerUseCase>(() =>
        _i549.BookingLogFetchDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i550.BookDetailCrmCustomerUseCase>(() =>
        _i550.BookDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i551.TimeLoadDetailCrmCustomerUseCase>(() =>
        _i551.TimeLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i552.AdviceUpdateDetailCrmCustomerUseCase>(() =>
        _i552.AdviceUpdateDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i553.AdviceTypeFetchDetailCrmCustomerUseCase>(() =>
        _i553.AdviceTypeFetchDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i554.BookingDetailLoadDetailCrmCustomerUseCase>(() =>
        _i554.BookingDetailLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i555.MessageLogFetchDetailCrmCustomerUseCase>(() =>
        _i555.MessageLogFetchDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i556.NumberBookingLoadDetailCrmCustomerUseCase>(() =>
        _i556.NumberBookingLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.factory<_i557.BookingLoadDetailCrmCustomerUseCase>(() =>
        _i557.BookingLoadDetailCrmCustomerUseCase(
            gh<_i378.DetailCrmCustomerRepository>()));
    gh.lazySingleton<_i558.PxUnasignedUpdateRepository>(
        () => _i559.PxUnasignedUpdateRepositoryImpl(
              gh<_i18.PxUnasignedUpdateApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i560.UserListRepository>(
        () => _i561.UserListRepositoryImpl(
              gh<_i18.UserListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i562.GetServicesUseCase>(
        () => _i562.GetServicesUseCase(gh<_i463.HomeRepository>()));
    gh.factory<_i563.PopupInfoUseCase>(
        () => _i563.PopupInfoUseCase(gh<_i463.HomeRepository>()));
    gh.factory<_i564.CreateChatFolderBloc>(() => _i564.CreateChatFolderBloc(
          gh<_i336.GetCreateChatFolderUseCase>(),
          gh<_i337.SaveCreateChatFolderUseCase>(),
          gh<_i334.GetSavedCreateChatFolderUseCase>(),
          gh<_i333.RemoveCreateChatFolderUseCase>(),
          gh<_i335.LoadCreateChatFolderUseCase>(),
          gh<_i340.RemoveFolderCreateChatFolderUseCase>(),
          gh<_i338.ConversationLoadCreateChatFolderUseCase>(),
          gh<_i339.UpdateCreateChatFolderUseCase>(),
        ));
    gh.factory<_i565.SaveTicketDetailUseCase>(() =>
        _i565.SaveTicketDetailUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i566.GetTicketDetailUseCase>(
        () => _i566.GetTicketDetailUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i567.GetTicketDetailReasonUseCase>(() =>
        _i567.GetTicketDetailReasonUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i568.GetSavedTicketDetailUseCase>(() =>
        _i568.GetSavedTicketDetailUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i569.ReceptTicketDetailUseCase>(() =>
        _i569.ReceptTicketDetailUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i570.TicketDetailReworkUseCase>(() =>
        _i570.TicketDetailReworkUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i571.ConfirmTicketDetailUseCase>(() =>
        _i571.ConfirmTicketDetailUseCase(gh<_i395.TicketDetailRepository>()));
    gh.factory<_i572.RemoveTicketDetailUseCase>(() =>
        _i572.RemoveTicketDetailUseCase(gh<_i395.TicketDetailRepository>()));
    gh.lazySingleton<_i573.CustomerInfoDetailsRepository>(
        () => _i574.CustomerInfoDetailsRepositoryImpl(
              gh<_i9.CustomerInfoDetailsApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i575.HrOrganizationRepository>(
        () => _i576.HrOrganizationRepositoryImpl(
              gh<_i18.HrOrganizationApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i577.RemoveScheduleDetailsUseCase>(() =>
        _i577.RemoveScheduleDetailsUseCase(
            gh<_i437.ScheduleDetailsRepository>()));
    gh.factory<_i578.SaveScheduleDetailsUseCase>(() =>
        _i578.SaveScheduleDetailsUseCase(
            gh<_i437.ScheduleDetailsRepository>()));
    gh.factory<_i579.GetSavedScheduleDetailsUseCase>(() =>
        _i579.GetSavedScheduleDetailsUseCase(
            gh<_i437.ScheduleDetailsRepository>()));
    gh.factory<_i580.GetScheduleDetailsUseCase>(() =>
        _i580.GetScheduleDetailsUseCase(gh<_i437.ScheduleDetailsRepository>()));
    gh.factory<_i581.GetProvinceCreateCustomerUseCase>(() =>
        _i581.GetProvinceCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i582.GetJobCreateCustomerUseCase>(() =>
        _i582.GetJobCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i583.RemoveCreateCustomerUseCase>(() =>
        _i583.RemoveCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i584.SaveCreateCustomerUseCase>(() =>
        _i584.SaveCreateCustomerUseCase(gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i585.GetDistrictCreateCustomerUseCase>(() =>
        _i585.GetDistrictCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i586.GetWardCreateCustomerUseCase>(() =>
        _i586.GetWardCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i587.GetCreateCustomerUseCase>(() =>
        _i587.GetCreateCustomerUseCase(gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i588.GetSavedCreateCustomerUseCase>(() =>
        _i588.GetSavedCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i589.UpdateCreateCustomerUseCase>(() =>
        _i589.UpdateCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i590.SurveyLoadCreateCustomerUseCase>(() =>
        _i590.SurveyLoadCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i591.CustomerSearchCreateCustomerUseCase>(() =>
        _i591.CustomerSearchCreateCustomerUseCase(
            gh<_i402.CreateCustomerRepository>()));
    gh.factory<_i592.InfoCustomerBloc>(
        () => _i592.InfoCustomerBloc(gh<_i515.GetCustomerInfoUseCase>()));
    gh.factory<_i593.GetProductConfirmBranchUseCase>(() =>
        _i593.GetProductConfirmBranchUseCase(
            gh<_i375.ProductConfirmRepository>()));
    gh.factory<_i594.GetProductDetailConfirmUseCase>(() =>
        _i594.GetProductDetailConfirmUseCase(
            gh<_i375.ProductConfirmRepository>()));
    gh.factory<_i595.ApprovalProductDetailConfirmUsecase>(() =>
        _i595.ApprovalProductDetailConfirmUsecase(
            gh<_i375.ProductConfirmRepository>()));
    gh.factory<_i596.GetProductConfirmUseCase>(() =>
        _i596.GetProductConfirmUseCase(gh<_i375.ProductConfirmRepository>()));
    gh.factory<_i597.RejectProductDetailConfirmUsecase>(() =>
        _i597.RejectProductDetailConfirmUsecase(
            gh<_i375.ProductConfirmRepository>()));
    gh.factory<_i598.SocketAccessTokenGetLoginUseCase>(() =>
        _i598.SocketAccessTokenGetLoginUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i599.GetNewsUseCase>(
        () => _i599.GetNewsUseCase(gh<_i400.NewsRepository>()));
    gh.factory<_i600.SearchNewsUseCase>(
        () => _i600.SearchNewsUseCase(gh<_i400.NewsRepository>()));
    gh.factory<_i601.GetDetailNewsUseCase>(
        () => _i601.GetDetailNewsUseCase(gh<_i400.NewsRepository>()));
    gh.factory<_i602.GetDepartmentUseCase>(
        () => _i602.GetDepartmentUseCase(gh<_i455.StaffRepository>()));
    gh.factory<_i603.GetFunctionRoomUseCase>(
        () => _i603.GetFunctionRoomUseCase(gh<_i455.StaffRepository>()));
    gh.factory<_i604.GetStaffUseCase>(
        () => _i604.GetStaffUseCase(gh<_i455.StaffRepository>()));
    gh.factory<_i605.CreateEformUseCase>(
        () => _i605.CreateEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i606.ApprovingSignalEformUseCase>(
        () => _i606.ApprovingSignalEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i607.ApprovingOtpEformUseCase>(
        () => _i607.ApprovingOtpEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i608.GetEformRequestTypeUseCase>(
        () => _i608.GetEformRequestTypeUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i609.RejectEformUseCase>(
        () => _i609.RejectEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i610.ApprovingEformUseCase>(
        () => _i610.ApprovingEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i611.GetDetailEformUseCase>(
        () => _i611.GetDetailEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i612.GetEformUseCase>(
        () => _i612.GetEformUseCase(gh<_i496.EformRepository>()));
    gh.factory<_i613.NewsBloc>(() => _i613.NewsBloc(
          gh<_i599.GetNewsUseCase>(),
          gh<_i600.SearchNewsUseCase>(),
        ));
    gh.factory<_i614.OrderFoodBloc>(() => _i614.OrderFoodBloc(
          gh<_i534.OrderFoodGetUseCase>(),
          gh<_i535.OrderFoodCreatedUseCase>(),
          gh<_i533.OrderFoodDeleteUseCase>(),
          gh<_i377.UniversalQrScanUseCase>(),
          gh<_i537.SetDefaultAddressFoodUseCase>(),
        ));
    gh.factory<_i615.CompleteTicketActiveUseCase>(() =>
        _i615.CompleteTicketActiveUseCase(gh<_i201.TicketActiveRepository>()));
    gh.factory<_i616.GetTicketActiveUseCase>(
        () => _i616.GetTicketActiveUseCase(gh<_i201.TicketActiveRepository>()));
    gh.factory<_i617.CreateTicketActiveUseCase>(() =>
        _i617.CreateTicketActiveUseCase(gh<_i201.TicketActiveRepository>()));
    gh.factory<_i618.DetailEformBloc>(() => _i618.DetailEformBloc(
          gh<_i611.GetDetailEformUseCase>(),
          gh<_i610.ApprovingEformUseCase>(),
        ));
    gh.factory<_i619.GetAddressNearLocationGoogleUseCase>(() =>
        _i619.GetAddressNearLocationGoogleUseCase(
            gh<_i359.LocationGoogleRepository>()));
    gh.factory<_i620.GetLocationGoogleUseCase>(() =>
        _i620.GetLocationGoogleUseCase(gh<_i359.LocationGoogleRepository>()));
    gh.factory<_i621.GetAddressAddressLocationGoogleUseCase>(() =>
        _i621.GetAddressAddressLocationGoogleUseCase(
            gh<_i359.LocationGoogleRepository>()));
    gh.factory<_i622.GetAddressSearchLocationGoogleUseCase>(() =>
        _i622.GetAddressSearchLocationGoogleUseCase(
            gh<_i359.LocationGoogleRepository>()));
    gh.factory<_i623.SaveHrOrganizationUseCase>(() =>
        _i623.SaveHrOrganizationUseCase(gh<_i575.HrOrganizationRepository>()));
    gh.factory<_i624.GetHrOrganizationUseCase>(() =>
        _i624.GetHrOrganizationUseCase(gh<_i575.HrOrganizationRepository>()));
    gh.factory<_i625.GetSavedHrOrganizationUseCase>(() =>
        _i625.GetSavedHrOrganizationUseCase(
            gh<_i575.HrOrganizationRepository>()));
    gh.factory<_i626.RemoveHrOrganizationUseCase>(() =>
        _i626.RemoveHrOrganizationUseCase(
            gh<_i575.HrOrganizationRepository>()));
    gh.factory<_i627.GetRoomListAddTagsImageUseCase>(() =>
        _i627.GetRoomListAddTagsImageUseCase(
            gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i628.CreateImageTagAddTagsImageUseCase>(() =>
        _i628.CreateImageTagAddTagsImageUseCase(
            gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i629.CreateMergeImageAddTagsImageUseCase>(() =>
        _i629.CreateMergeImageAddTagsImageUseCase(
            gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i630.SaveAddTagsImageUseCase>(() =>
        _i630.SaveAddTagsImageUseCase(gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i631.GetSavedAddTagsImageUseCase>(() =>
        _i631.GetSavedAddTagsImageUseCase(gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i632.RemoveAddTagsImageUseCase>(() =>
        _i632.RemoveAddTagsImageUseCase(gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i633.GetTagListAddTagsImageUseCase>(() =>
        _i633.GetTagListAddTagsImageUseCase(
            gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i634.UserSearchAddTagsImageUseCase>(() =>
        _i634.UserSearchAddTagsImageUseCase(
            gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i635.GetAddTagsImageUseCase>(
        () => _i635.GetAddTagsImageUseCase(gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i636.GetImageListAddTagsImageUseCase>(() =>
        _i636.GetImageListAddTagsImageUseCase(
            gh<_i274.AddTagsImageRepository>()));
    gh.factory<_i637.DetailNewsBloc>(
        () => _i637.DetailNewsBloc(gh<_i601.GetDetailNewsUseCase>()));
    gh.factory<_i638.EmployeeGetBranchSelectionUseCase>(() =>
        _i638.EmployeeGetBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i639.RemoveBranchSelectionUseCase>(() =>
        _i639.RemoveBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i640.SaveBranchSelectionUseCase>(() =>
        _i640.SaveBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i641.BedSelectBranchSelectionUseCase>(() =>
        _i641.BedSelectBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i642.GetFloorBranchSelectionUseCase>(() =>
        _i642.GetFloorBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i643.BedChangeBranchSelectionUseCase>(() =>
        _i643.BedChangeBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i644.EstimateTimeGetBranchSelectionUseCase>(() =>
        _i644.EstimateTimeGetBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i645.GetSavedBranchSelectionUseCase>(() =>
        _i645.GetSavedBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i646.GetBedBranchSelectionUseCase>(() =>
        _i646.GetBedBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i647.GetBranchSelectionUseCase>(() =>
        _i647.GetBranchSelectionUseCase(gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i648.GetRoomBranchSelectionUseCase>(() =>
        _i648.GetRoomBranchSelectionUseCase(
            gh<_i199.BranchSelectionRepository>()));
    gh.factory<_i649.GetProvinceUseCase>(
        () => _i649.GetProvinceUseCase(gh<_i199.BranchSelectionRepository>()));
    gh.lazySingleton<_i650.ConsultationManagerRepository>(
        () => _i651.ConsultationManagerRepositoryImpl(
              gh<_i18.ConsultationManagerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i652.MedicalDepartmentListBloc>(
        () => _i652.MedicalDepartmentListBloc(
              gh<_i384.GetMedicalDepartmentListUseCase>(),
              gh<_i385.SaveMedicalDepartmentListUseCase>(),
              gh<_i383.GetSavedMedicalDepartmentListUseCase>(),
              gh<_i386.RemoveMedicalDepartmentListUseCase>(),
            ));
    gh.factory<_i653.PostReadNotificationUseCase>(() =>
        _i653.PostReadNotificationUseCase(gh<_i494.NotificationRepository>()));
    gh.factory<_i654.GetDetailNotificationUseCase>(() =>
        _i654.GetDetailNotificationUseCase(gh<_i494.NotificationRepository>()));
    gh.factory<_i655.GetNavigationInfoUseCase>(() =>
        _i655.GetNavigationInfoUseCase(gh<_i494.NotificationRepository>()));
    gh.factory<_i656.SearchNotificationsUseCase>(() =>
        _i656.SearchNotificationsUseCase(gh<_i494.NotificationRepository>()));
    gh.factory<_i657.GetNotificationsUseCase>(() =>
        _i657.GetNotificationsUseCase(gh<_i494.NotificationRepository>()));
    gh.factory<_i658.ReadAllNotificationUseCase>(() =>
        _i658.ReadAllNotificationUseCase(gh<_i494.NotificationRepository>()));
    gh.lazySingleton<_i659.AssignTaskRepository>(
        () => _i660.AssignTaskRepositoryImpl(
              gh<_i18.AssignTaskApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i661.MedicalServiceLogListRepository>(
        () => _i662.MedicalServiceLogListRepositoryImpl(
              gh<_i18.MedicalServiceLogListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i663.SuggestServicesFetchCustomerBookingInfoUseCase>(() =>
        _i663.SuggestServicesFetchCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i664.RemoveCustomerBookingInfoUseCase>(() =>
        _i664.RemoveCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i665.ServiceDetailsLoadCustomerBookingInfoUseCase>(() =>
        _i665.ServiceDetailsLoadCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i666.SaveCustomerBookingInfoUseCase>(() =>
        _i666.SaveCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i667.GetCustomerBookingInfoUseCase>(() =>
        _i667.GetCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i668.BookedServicesFetchCustomerBookingInfoUseCase>(() =>
        _i668.BookedServicesFetchCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i669.GetSavedCustomerBookingInfoUseCase>(() =>
        _i669.GetSavedCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i670.UsedServiceFetchCustomerBookingInfoUseCase>(() =>
        _i670.UsedServiceFetchCustomerBookingInfoUseCase(
            gh<_i389.CustomerBookingInfoRepository>()));
    gh.factory<_i671.UploadFeedbackUseCase>(
        () => _i671.UploadFeedbackUseCase(gh<_i387.MediaUploadRepository>()));
    gh.factory<_i672.UploadCheckInImageUseCase>(() =>
        _i672.UploadCheckInImageUseCase(gh<_i387.MediaUploadRepository>()));
    gh.factory<_i673.UploadAvatarUseCase>(
        () => _i673.UploadAvatarUseCase(gh<_i387.MediaUploadRepository>()));
    gh.factory<_i674.UploadBackgroundUseCase>(
        () => _i674.UploadBackgroundUseCase(gh<_i387.MediaUploadRepository>()));
    gh.factory<_i675.UploadKYCUseCase>(
        () => _i675.UploadKYCUseCase(gh<_i387.MediaUploadRepository>()));
    gh.factory<_i676.DoctorFetchServiceDetailUseCase>(() =>
        _i676.DoctorFetchServiceDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i677.EmployeeFetchServiceDetailUseCase>(() =>
        _i677.EmployeeFetchServiceDetailUseCase(
            gh<_i269.ConsultationCustomerRepository>()));
    gh.factory<_i678.SendKycPhotosSettingUseCase>(
        () => _i678.SendKycPhotosSettingUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i679.CheckinPermissionCheckUserUseCase>(() =>
        _i679.CheckinPermissionCheckUserUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i680.StringeeTokenFetchUserUseCase>(
        () => _i680.StringeeTokenFetchUserUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i681.CheckPermissionUserUseCase>(
        () => _i681.CheckPermissionUserUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i682.UserDeletionUseCase>(
        () => _i682.UserDeletionUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i683.MedicalTemplateListRepository>(
        () => _i684.MedicalTemplateListRepositoryImpl(
              gh<_i18.MedicalTemplateListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i685.UpdateCommentUseCase>(
        () => _i685.UpdateCommentUseCase(gh<_i477.CommentListRepository>()));
    gh.factory<_i686.GetCheckinPhotoUseCase>(
        () => _i686.GetCheckinPhotoUseCase(gh<_i253.CheckinPhotoRepository>()));
    gh.factory<_i687.GetSavedCheckinPhotoUseCase>(() =>
        _i687.GetSavedCheckinPhotoUseCase(gh<_i253.CheckinPhotoRepository>()));
    gh.factory<_i688.SaveCheckinPhotoUseCase>(() =>
        _i688.SaveCheckinPhotoUseCase(gh<_i253.CheckinPhotoRepository>()));
    gh.factory<_i689.RemoveCheckinPhotoUseCase>(() =>
        _i689.RemoveCheckinPhotoUseCase(gh<_i253.CheckinPhotoRepository>()));
    gh.factory<_i690.DetailStaffEvaluationPeriodBloc>(
        () => _i690.DetailStaffEvaluationPeriodBloc(
              gh<_i492.GetDetailStaffEvaluationPeriodUseCase>(),
              gh<_i488.SaveDetailStaffEvaluationPeriodUseCase>(),
              gh<_i491.GetSavedDetailStaffEvaluationPeriodUseCase>(),
              gh<_i490.RemoveDetailStaffEvaluationPeriodUseCase>(),
              gh<_i489.EmployeeFetchDetailStaffEvaluationPeriodUseCase>(),
            ));
    gh.factory<_i691.HomeFindBloc>(() =>
        _i691.HomeFindBloc(gh<_i677.EmployeeFetchServiceDetailUseCase>()));
    gh.factory<_i692.ThemeBloc>(() => _i692.ThemeBloc(
          gh<_i418.GetThemeOptionUseCase>(),
          gh<_i420.SaveThemeOptionUseCase>(),
        ));
    gh.factory<_i693.GetBranchChatListUseCase>(() =>
        _i693.GetBranchChatListUseCase(gh<_i427.BranchChatListRepository>()));
    gh.factory<_i694.GetSavedBranchChatListUseCase>(() =>
        _i694.GetSavedBranchChatListUseCase(
            gh<_i427.BranchChatListRepository>()));
    gh.factory<_i695.RemoveBranchChatListUseCase>(() =>
        _i695.RemoveBranchChatListUseCase(
            gh<_i427.BranchChatListRepository>()));
    gh.factory<_i696.SaveBranchChatListUseCase>(() =>
        _i696.SaveBranchChatListUseCase(gh<_i427.BranchChatListRepository>()));
    gh.lazySingleton<_i697.RequestRepository>(() => _i698.RequestRepositoryImpl(
          gh<_i9.RequestApiService>(),
          cache: gh<_i9.EZCache>(),
        ));
    gh.factory<_i699.TabBarBloc>(() => _i699.TabBarBloc(
          gh<_i466.GetStoryListUseCase>(),
          gh<_i562.GetServicesUseCase>(),
        ));
    gh.factory<_i700.ImportantNotesBloc>(() => _i700.ImportantNotesBloc(
          gh<_i506.GetImportantNotesUseCase>(),
          gh<_i504.SaveImportantNotesUseCase>(),
          gh<_i503.GetSavedImportantNotesUseCase>(),
          gh<_i507.RemoveImportantNotesUseCase>(),
        ));
    gh.factory<_i701.SaveCustomerInfoDetailsUseCase>(() =>
        _i701.SaveCustomerInfoDetailsUseCase(
            gh<_i573.CustomerInfoDetailsRepository>()));
    gh.factory<_i702.GetCustomerInfoDetailsUseCase>(() =>
        _i702.GetCustomerInfoDetailsUseCase(
            gh<_i573.CustomerInfoDetailsRepository>()));
    gh.factory<_i703.CheckoutCustomerInfoDetailsUseCase>(() =>
        _i703.CheckoutCustomerInfoDetailsUseCase(
            gh<_i573.CustomerInfoDetailsRepository>()));
    gh.factory<_i704.GetSavedCustomerInfoDetailsUseCase>(() =>
        _i704.GetSavedCustomerInfoDetailsUseCase(
            gh<_i573.CustomerInfoDetailsRepository>()));
    gh.factory<_i705.RemoveCustomerInfoDetailsUseCase>(() =>
        _i705.RemoveCustomerInfoDetailsUseCase(
            gh<_i573.CustomerInfoDetailsRepository>()));
    gh.factory<_i706.CustomerRecordBloc>(() => _i706.CustomerRecordBloc(
          gh<_i459.GetCustomerRecordUseCase>(),
          gh<_i460.SaveCustomerRecordUseCase>(),
          gh<_i458.GetSavedCustomerRecordUseCase>(),
          gh<_i457.RemoveCustomerRecordUseCase>(),
        ));
    gh.lazySingleton<_i707.MedicalServiceCreationRepository>(
        () => _i708.MedicalServiceCreationRepositoryImpl(
              gh<_i18.MedicalServiceCreationApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i709.TagImageRepository>(
        () => _i710.TagImageRepositoryImpl(gh<_i192.TagImageApiService>()));
    gh.factory<_i711.GetKpiEmployeeDetailUseCase>(() =>
        _i711.GetKpiEmployeeDetailUseCase(gh<_i526.KpiEmployeeRepository>()));
    gh.factory<_i712.GetKpiEmployeeUseCase>(
        () => _i712.GetKpiEmployeeUseCase(gh<_i526.KpiEmployeeRepository>()));
    gh.factory<_i713.CreateConsultationCustomerProfileUseCase>(() =>
        _i713.CreateConsultationCustomerProfileUseCase(
            gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i714.SaveCustomerProfileUseCase>(() =>
        _i714.SaveCustomerProfileUseCase(
            gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i715.RemoveCustomerProfileUseCase>(() =>
        _i715.RemoveCustomerProfileUseCase(
            gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i716.GetConsultationHistoryCustomerProfileUseCase>(() =>
        _i716.GetConsultationHistoryCustomerProfileUseCase(
            gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i717.GetCustomerProfileUseCase>(() =>
        _i717.GetCustomerProfileUseCase(gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i718.GetSavedCustomerProfileUseCase>(() =>
        _i718.GetSavedCustomerProfileUseCase(
            gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i719.UpdateConsultationCustomerProfileUseCase>(() =>
        _i719.UpdateConsultationCustomerProfileUseCase(
            gh<_i412.CustomerProfileRepository>()));
    gh.factory<_i720.SubmitRatingHumanUseCase>(() =>
        _i720.SubmitRatingHumanUseCase(gh<_i259.RatingHumanRepository>()));
    gh.factory<_i721.GetQuestionDetailUseCase>(() =>
        _i721.GetQuestionDetailUseCase(gh<_i259.RatingHumanRepository>()));
    gh.factory<_i722.SaveRatingHumanUseCase>(
        () => _i722.SaveRatingHumanUseCase(gh<_i259.RatingHumanRepository>()));
    gh.factory<_i723.GetRatingHumanUseCase>(
        () => _i723.GetRatingHumanUseCase(gh<_i259.RatingHumanRepository>()));
    gh.factory<_i724.HrOrganizationBloc>(() => _i724.HrOrganizationBloc(
          gh<_i624.GetHrOrganizationUseCase>(),
          gh<_i623.SaveHrOrganizationUseCase>(),
          gh<_i625.GetSavedHrOrganizationUseCase>(),
          gh<_i626.RemoveHrOrganizationUseCase>(),
        ));
    gh.factory<_i725.RemoveMedicineDetailUseCase>(() =>
        _i725.RemoveMedicineDetailUseCase(
            gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i726.GetUnitMedicineDetailUseCase>(() =>
        _i726.GetUnitMedicineDetailUseCase(
            gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i727.CreateMedicineDetailUseCase>(() =>
        _i727.CreateMedicineDetailUseCase(
            gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i728.UpdateMedicineDetailUseCase>(() =>
        _i728.UpdateMedicineDetailUseCase(
            gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i729.GetSavedMedicineDetailUseCase>(() =>
        _i729.GetSavedMedicineDetailUseCase(
            gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i730.GetMedicineDetailUseCase>(() =>
        _i730.GetMedicineDetailUseCase(gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i731.SaveMedicineDetailUseCase>(() =>
        _i731.SaveMedicineDetailUseCase(gh<_i211.MedicineDetailRepository>()));
    gh.factory<_i732.SaveCustomerScheduleUseCase>(() =>
        _i732.SaveCustomerScheduleUseCase(
            gh<_i524.CustomerScheduleRepository>()));
    gh.factory<_i733.GetSavedCustomerScheduleUseCase>(() =>
        _i733.GetSavedCustomerScheduleUseCase(
            gh<_i524.CustomerScheduleRepository>()));
    gh.factory<_i734.RemoveCustomerScheduleUseCase>(() =>
        _i734.RemoveCustomerScheduleUseCase(
            gh<_i524.CustomerScheduleRepository>()));
    gh.factory<_i735.GetCustomerScheduleUseCase>(() =>
        _i735.GetCustomerScheduleUseCase(
            gh<_i524.CustomerScheduleRepository>()));
    gh.factory<_i736.GetSavedDevUseCase>(
        () => _i736.GetSavedDevUseCase(gh<_i357.DevRepository>()));
    gh.factory<_i737.GetDevUseCase>(
        () => _i737.GetDevUseCase(gh<_i357.DevRepository>()));
    gh.factory<_i738.SaveDevUseCase>(
        () => _i738.SaveDevUseCase(gh<_i357.DevRepository>()));
    gh.factory<_i739.MiniAppDevUseCase>(
        () => _i739.MiniAppDevUseCase(gh<_i357.DevRepository>()));
    gh.factory<_i740.RemoveDevUseCase>(
        () => _i740.RemoveDevUseCase(gh<_i357.DevRepository>()));
    gh.factory<_i741.RemoveMedicalServiceCreationUseCase>(() =>
        _i741.RemoveMedicalServiceCreationUseCase(
            gh<_i707.MedicalServiceCreationRepository>()));
    gh.factory<_i742.ServicesMedicalServiceCreationUseCase>(() =>
        _i742.ServicesMedicalServiceCreationUseCase(
            gh<_i707.MedicalServiceCreationRepository>()));
    gh.factory<_i743.MethodsMedicalServiceCreationUseCase>(() =>
        _i743.MethodsMedicalServiceCreationUseCase(
            gh<_i707.MedicalServiceCreationRepository>()));
    gh.factory<_i744.GetSavedMedicalServiceCreationUseCase>(() =>
        _i744.GetSavedMedicalServiceCreationUseCase(
            gh<_i707.MedicalServiceCreationRepository>()));
    gh.factory<_i745.SaveMedicalServiceCreationUseCase>(() =>
        _i745.SaveMedicalServiceCreationUseCase(
            gh<_i707.MedicalServiceCreationRepository>()));
    gh.factory<_i746.MedicalServiceCreationUseCase>(() =>
        _i746.MedicalServiceCreationUseCase(
            gh<_i707.MedicalServiceCreationRepository>()));
    gh.factory<_i747.SaveFeedbackUseCase>(
        () => _i747.SaveFeedbackUseCase(gh<_i272.FeedbackRepository>()));
    gh.factory<_i748.SendFeedbackUseCase>(
        () => _i748.SendFeedbackUseCase(gh<_i272.FeedbackRepository>()));
    gh.factory<_i749.GetSavedFeedbackUseCase>(
        () => _i749.GetSavedFeedbackUseCase(gh<_i272.FeedbackRepository>()));
    gh.factory<_i750.RemoveFeedbackUseCase>(
        () => _i750.RemoveFeedbackUseCase(gh<_i272.FeedbackRepository>()));
    gh.factory<_i751.MultiLanguageBloc>(() => _i751.MultiLanguageBloc(
          gh<_i417.GetLanguageOptionUseCase>(),
          gh<_i419.SaveLanguageOptionUseCase>(),
        ));
    gh.factory<_i752.EformBloc>(
        () => _i752.EformBloc(gh<_i612.GetEformUseCase>()));
    gh.factory<_i753.CreateNoteDetailsUseCase>(() =>
        _i753.CreateNoteDetailsUseCase(gh<_i424.NoteDetailsRepository>()));
    gh.factory<_i754.UpdateNoteDetailsUseCase>(() =>
        _i754.UpdateNoteDetailsUseCase(gh<_i424.NoteDetailsRepository>()));
    gh.factory<_i755.RemoveNoteDetailsUseCase>(() =>
        _i755.RemoveNoteDetailsUseCase(gh<_i424.NoteDetailsRepository>()));
    gh.factory<_i756.GetSavedNoteDetailsUseCase>(() =>
        _i756.GetSavedNoteDetailsUseCase(gh<_i424.NoteDetailsRepository>()));
    gh.factory<_i757.GetNoteDetailsUseCase>(
        () => _i757.GetNoteDetailsUseCase(gh<_i424.NoteDetailsRepository>()));
    gh.factory<_i758.SaveNoteDetailsUseCase>(
        () => _i758.SaveNoteDetailsUseCase(gh<_i424.NoteDetailsRepository>()));
    gh.factory<_i759.SaveCustomerListUseCase>(() =>
        _i759.SaveCustomerListUseCase(gh<_i391.CustomerListRepository>()));
    gh.factory<_i760.GetCustomerListUseCase>(
        () => _i760.GetCustomerListUseCase(gh<_i391.CustomerListRepository>()));
    gh.factory<_i761.GetSavedCustomerListUseCase>(() =>
        _i761.GetSavedCustomerListUseCase(gh<_i391.CustomerListRepository>()));
    gh.factory<_i762.RemoveCustomerListUseCase>(() =>
        _i762.RemoveCustomerListUseCase(gh<_i391.CustomerListRepository>()));
    gh.factory<_i763.GetCustomerRelationShipListUseCase>(() =>
        _i763.GetCustomerRelationShipListUseCase(
            gh<_i391.CustomerListRepository>()));
    gh.factory<_i764.RemovePxUnasignedUseCase>(() =>
        _i764.RemovePxUnasignedUseCase(gh<_i451.PxUnasignedRepository>()));
    gh.factory<_i765.GetPxCustomerListUseCase>(() =>
        _i765.GetPxCustomerListUseCase(gh<_i451.PxUnasignedRepository>()));
    gh.factory<_i766.SavePxUnasignedUseCase>(
        () => _i766.SavePxUnasignedUseCase(gh<_i451.PxUnasignedRepository>()));
    gh.factory<_i767.GetSavedPxUnasignedUseCase>(() =>
        _i767.GetSavedPxUnasignedUseCase(gh<_i451.PxUnasignedRepository>()));
    gh.factory<_i768.LikeListBloc>(
        () => _i768.LikeListBloc(gh<_i399.GetLikeListUseCase>()));
    gh.factory<_i769.TicketActiveBloc>(
        () => _i769.TicketActiveBloc(gh<_i616.GetTicketActiveUseCase>()));
    gh.factory<_i770.ChatListBloc>(() => _i770.ChatListBloc(
          gh<_i164.GetChatListUseCase>(),
          gh<_i167.SaveChatListUseCase>(),
          gh<_i157.GetSavedChatListUseCase>(),
          gh<_i166.RemoveChatListUseCase>(),
          gh<_i159.SearchChatListUseCase>(),
          gh<_i161.SearchMessageChatListUseCase>(),
          gh<_i158.PinConversationChatListUseCase>(),
          gh<_i156.GetTotalUnreadChatListUseCase>(),
          gh<_i335.LoadCreateChatFolderUseCase>(),
          gh<_i338.ConversationLoadCreateChatFolderUseCase>(),
          gh<_i160.MarkAsReadChatListUseCase>(),
          gh<_i163.GetRecentContactsChatListUseCase>(),
          gh<_i165.UpdatePinConversationChatListUseCase>(),
          gh<_i154.SortFolderChatListUseCase>(),
        ));
    gh.factory<_i771.UpdateProfileUseCase>(
        () => _i771.UpdateProfileUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i772.CheckEmployeeUseCase>(
        () => _i772.CheckEmployeeUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i773.GetOtpUseCase>(
        () => _i773.GetOtpUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i774.CheckPhoneUseCase>(
        () => _i774.CheckPhoneUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i775.LoginUseCase>(
        () => _i775.LoginUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i776.LogoutUseCase>(
        () => _i776.LogoutUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i777.UploadImagesUseCase>(
        () => _i777.UploadImagesUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i778.GetProfilesUseCase>(
        () => _i778.GetProfilesUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i779.GetEnableOnlineLoggerUseCase>(
        () => _i779.GetEnableOnlineLoggerUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i780.GetProfileUseCase>(
        () => _i780.GetProfileUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i781.ResetPasswordUseCase>(
        () => _i781.ResetPasswordUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i782.UploadFilesUseCase>(
        () => _i782.UploadFilesUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i783.GetConfigurationUseCase>(
        () => _i783.GetConfigurationUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i784.ConfirmOtpUseCase>(
        () => _i784.ConfirmOtpUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i785.UpdateBioUseCase>(
        () => _i785.UpdateBioUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i786.LoginSocialUseCase>(
        () => _i786.LoginSocialUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i787.SubmitFeedbackUseCase>(
        () => _i787.SubmitFeedbackUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i788.UploadAudiosUseCase>(
        () => _i788.UploadAudiosUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i789.ChangePasswordUseCase>(
        () => _i789.ChangePasswordUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i790.UploadUseCase>(
        () => _i790.UploadUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i791.GetUserInfoUseCase>(
        () => _i791.GetUserInfoUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i792.CacheQuickActionRemoveUseCase>(
        () => _i792.CacheQuickActionRemoveUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i793.CacheQuickActionGetUseCase>(
        () => _i793.CacheQuickActionGetUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i794.CacheQuickActionSaveUseCase>(
        () => _i794.CacheQuickActionSaveUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i795.CacheLoginStringeeRemoveUseCase>(() =>
        _i795.CacheLoginStringeeRemoveUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i796.IsShowBoardingUseCase>(
        () => _i796.IsShowBoardingUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i797.HasTokenUseCase>(
        () => _i797.HasTokenUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i798.CacheLoginStringeeGetUseCase>(
        () => _i798.CacheLoginStringeeGetUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i799.CacheUserUseCase>(
        () => _i799.CacheUserUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i800.CacheLoginStringeeSaveUseCase>(
        () => _i800.CacheLoginStringeeSaveUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i801.CacheCheckoutHourSaveUseCase>(
        () => _i801.CacheCheckoutHourSaveUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i802.CacheCheckinHourSaveUseCase>(
        () => _i802.CacheCheckinHourSaveUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i803.CacheCheckoutHourGetUseCase>(
        () => _i803.CacheCheckoutHourGetUseCase(gh<_i267.UserRepository>()));
    gh.lazySingleton<_i804.CacheCheckinHourGetUseCase>(
        () => _i804.CacheCheckinHourGetUseCase(gh<_i267.UserRepository>()));
    gh.factory<_i805.DevBloc>(() => _i805.DevBloc(
          gh<_i737.GetDevUseCase>(),
          gh<_i738.SaveDevUseCase>(),
          gh<_i736.GetSavedDevUseCase>(),
          gh<_i740.RemoveDevUseCase>(),
          gh<_i739.MiniAppDevUseCase>(),
        ));
    gh.factory<_i806.ProductDetailConfirmBloc>(
        () => _i806.ProductDetailConfirmBloc(
              gh<_i594.GetProductDetailConfirmUseCase>(),
              gh<_i595.ApprovalProductDetailConfirmUsecase>(),
              gh<_i597.RejectProductDetailConfirmUsecase>(),
            ));
    gh.lazySingleton<_i807.CollaboratorUserBloc>(
        () => _i807.CollaboratorUserBloc(gh<_i799.CacheUserUseCase>()));
    gh.factory<_i808.ScheduleDetailsBloc>(() => _i808.ScheduleDetailsBloc(
          gh<_i580.GetScheduleDetailsUseCase>(),
          gh<_i578.SaveScheduleDetailsUseCase>(),
          gh<_i579.GetSavedScheduleDetailsUseCase>(),
          gh<_i577.RemoveScheduleDetailsUseCase>(),
        ));
    gh.factory<_i809.NotiBotTypePostTakingCareCustomerUseCase>(() =>
        _i809.NotiBotTypePostTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i810.RemoveImageTakingCareCustomerUseCase>(() =>
        _i810.RemoveImageTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i811.FinishTaskTakingCareCustomerUseCase>(() =>
        _i811.FinishTaskTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i812.GetSavedTakingCareCustomerUseCase>(() =>
        _i812.GetSavedTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i813.CreateSupportTakingCareCustomerUseCase>(() =>
        _i813.CreateSupportTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i814.GetSectionTakingCareCustomerUseCase>(() =>
        _i814.GetSectionTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i815.RemoveTakingCareCustomerUseCase>(() =>
        _i815.RemoveTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i816.SaveTakingCareCustomerUseCase>(() =>
        _i816.SaveTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i817.CreateTreatmentDetailsTakingCareCustomerUseCase>(() =>
        _i817.CreateTreatmentDetailsTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i818.CheckEmployeeInRoomTakingCareCustomerUseCase>(() =>
        _i818.CheckEmployeeInRoomTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i819.UpdateServiceDetailUseCaseTakingCareCustomerUseCase>(() =>
        _i819.UpdateServiceDetailUseCaseTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i820.GetTakingCareCustomerUseCase>(() =>
        _i820.GetTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i821.GetTreatmentPhotoTakingCareCustomerUseCase>(() =>
        _i821.GetTreatmentPhotoTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i822.BotTypeLoadTakingCareCustomerUseCase>(() =>
        _i822.BotTypeLoadTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i823.UploadRecordTakingCareCustomerUseCase>(() =>
        _i823.UploadRecordTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i824.UploadImagesTakingCareCustomerUseCase>(() =>
        _i824.UploadImagesTakingCareCustomerUseCase(
            gh<_i381.TakingCareCustomerRepository>()));
    gh.factory<_i825.GetSavedPxListUseCase>(
        () => _i825.GetSavedPxListUseCase(gh<_i353.PxListRepository>()));
    gh.factory<_i826.GetPxListUseCase>(
        () => _i826.GetPxListUseCase(gh<_i353.PxListRepository>()));
    gh.factory<_i827.SavePxListUseCase>(
        () => _i827.SavePxListUseCase(gh<_i353.PxListRepository>()));
    gh.factory<_i828.RemovePxListUseCase>(
        () => _i828.RemovePxListUseCase(gh<_i353.PxListRepository>()));
    gh.factory<_i829.SaveMedicalServiceLogListUseCase>(() =>
        _i829.SaveMedicalServiceLogListUseCase(
            gh<_i661.MedicalServiceLogListRepository>()));
    gh.factory<_i830.RemoveMedicalServiceLogListUseCase>(() =>
        _i830.RemoveMedicalServiceLogListUseCase(
            gh<_i661.MedicalServiceLogListRepository>()));
    gh.factory<_i831.GetMedicalServiceLogListUseCase>(() =>
        _i831.GetMedicalServiceLogListUseCase(
            gh<_i661.MedicalServiceLogListRepository>()));
    gh.factory<_i832.GetSavedMedicalServiceLogListUseCase>(() =>
        _i832.GetSavedMedicalServiceLogListUseCase(
            gh<_i661.MedicalServiceLogListRepository>()));
    gh.factory<_i833.GroupChatDetailBloc>(() => _i833.GroupChatDetailBloc(
          gh<_i283.GetGroupChatDetailUseCase>(),
          gh<_i292.SaveGroupChatDetailUseCase>(),
          gh<_i291.GetSavedGroupChatDetailUseCase>(),
          gh<_i278.RemoveGroupChatDetailUseCase>(),
          gh<_i281.MediaLoadGroupChatDetailUseCase>(),
          gh<_i288.FileLoadGroupChatDetailUseCase>(),
          gh<_i285.LinkLoadGroupChatDetailUseCase>(),
          gh<_i290.UpdateGroupChatDetailUseCase>(),
          gh<_i277.AvatarUploadGroupChatDetailUseCase>(),
          gh<_i778.GetProfilesUseCase>(),
          gh<_i280.DeleteGroupUseCase>(),
          gh<_i279.MemberInfoLoadGroupChatDetailUseCase>(),
          gh<_i282.UpdateMemberRuleGroupChatDetailUseCase>(),
          gh<_i286.UpdateAdminRuleGroupChatDetailUseCase>(),
          gh<_i287.GetUserRulesGroupChatDetailUseCase>(),
          gh<_i289.GetRuleByRoleGroupChatDetailUseCase>(),
          gh<_i284.ChangeOwnerGroupChatDetailUseCase>(),
          gh<_i276.GetUserExceptionGroupChatDetailUseCase>(),
        ));
    gh.factory<_i834.CheckinReminderBloc>(() => _i834.CheckinReminderBloc(
          gh<_i804.CacheCheckinHourGetUseCase>(),
          gh<_i802.CacheCheckinHourSaveUseCase>(),
          gh<_i803.CacheCheckoutHourGetUseCase>(),
          gh<_i801.CacheCheckoutHourSaveUseCase>(),
        ));
    gh.factory<_i835.CheckLeaderHomeUseCase>(
        () => _i835.CheckLeaderHomeUseCase(gh<_i463.HomeRepository>()));
    gh.factory<_i836.WorkLeaderCheckHomeUseCase>(
        () => _i836.WorkLeaderCheckHomeUseCase(gh<_i463.HomeRepository>()));
    gh.factory<_i837.CheckinCustomerUseCase>(
        () => _i837.CheckinCustomerUseCase(gh<_i293.CustomerRepository>()));
    gh.factory<_i838.GetRoomListCustomerUseCase>(
        () => _i838.GetRoomListCustomerUseCase(gh<_i293.CustomerRepository>()));
    gh.factory<_i839.SaveCustomerRoomCodeUseCase>(() =>
        _i839.SaveCustomerRoomCodeUseCase(gh<_i293.CustomerRepository>()));
    gh.factory<_i840.PrintCustomerUseCase>(
        () => _i840.PrintCustomerUseCase(gh<_i293.CustomerRepository>()));
    gh.factory<_i841.StickerBloc>(() => _i841.StickerBloc(
          gh<_i349.CreateStickerListUseCase>(),
          gh<_i351.GetStickerListUseCase>(),
          gh<_i348.GetStickerSetUseCase>(),
          gh<_i345.GetStickerRecentUseCase>(),
          gh<_i350.UploadStickerUseCase>(),
          gh<_i347.UpdateStickerRecentUseCase>(),
          gh<_i344.GetStickerOnlySetUseCase>(),
          gh<_i352.RemoveStickerSetUseCase>(),
          gh<_i346.RemoveStickerUseCase>(),
          gh<_i343.UploadStickerSetUseCase>(),
        ));
    gh.factory<_i842.NoteDetailsBloc>(() => _i842.NoteDetailsBloc(
          gh<_i757.GetNoteDetailsUseCase>(),
          gh<_i758.SaveNoteDetailsUseCase>(),
          gh<_i756.GetSavedNoteDetailsUseCase>(),
          gh<_i755.RemoveNoteDetailsUseCase>(),
          gh<_i753.CreateNoteDetailsUseCase>(),
          gh<_i754.UpdateNoteDetailsUseCase>(),
          gh<_i777.UploadImagesUseCase>(),
        ));
    gh.factory<_i843.TakingCareCustomerBloc>(() => _i843.TakingCareCustomerBloc(
          gh<_i820.GetTakingCareCustomerUseCase>(),
          gh<_i816.SaveTakingCareCustomerUseCase>(),
          gh<_i812.GetSavedTakingCareCustomerUseCase>(),
          gh<_i815.RemoveTakingCareCustomerUseCase>(),
          gh<_i811.FinishTaskTakingCareCustomerUseCase>(),
          gh<_i824.UploadImagesTakingCareCustomerUseCase>(),
          gh<_i810.RemoveImageTakingCareCustomerUseCase>(),
          gh<_i823.UploadRecordTakingCareCustomerUseCase>(),
          gh<_i813.CreateSupportTakingCareCustomerUseCase>(),
          gh<_i818.CheckEmployeeInRoomTakingCareCustomerUseCase>(),
          gh<_i822.BotTypeLoadTakingCareCustomerUseCase>(),
          gh<_i302.ProductLoadConsultationCustomerUseCase>(),
          gh<_i323.GetTreatmentDetailUseCase>(),
          gh<_i809.NotiBotTypePostTakingCareCustomerUseCase>(),
          gh<_i821.GetTreatmentPhotoTakingCareCustomerUseCase>(),
          gh<_i819.UpdateServiceDetailUseCaseTakingCareCustomerUseCase>(),
        ));
    gh.factory<_i844.DeleteTagByComboTagUsecase>(
        () => _i844.DeleteTagByComboTagUsecase(gh<_i709.TagImageRepository>()));
    gh.factory<_i845.DeleteImageByComboTagUsecase>(() =>
        _i845.DeleteImageByComboTagUsecase(gh<_i709.TagImageRepository>()));
    gh.factory<_i846.GetComboTagUsecase>(
        () => _i846.GetComboTagUsecase(gh<_i709.TagImageRepository>()));
    gh.factory<_i847.GetImageByComboTagUsecae>(
        () => _i847.GetImageByComboTagUsecae(gh<_i709.TagImageRepository>()));
    gh.factory<_i848.StoryDetailBloc>(() => _i848.StoryDetailBloc(
          gh<_i176.GetStoryDetailUseCase>(),
          gh<_i475.DeleteStoryUseCase>(),
          gh<_i397.PostLikeStoryUseCase>(),
          gh<_i471.UpdateStoryUseCase>(),
        ));
    gh.factory<_i849.GetSavedUserListUseCase>(
        () => _i849.GetSavedUserListUseCase(gh<_i560.UserListRepository>()));
    gh.factory<_i850.GetUserListUseCase>(
        () => _i850.GetUserListUseCase(gh<_i560.UserListRepository>()));
    gh.factory<_i851.RemoveUserListUseCase>(
        () => _i851.RemoveUserListUseCase(gh<_i560.UserListRepository>()));
    gh.factory<_i852.SaveUserListUseCase>(
        () => _i852.SaveUserListUseCase(gh<_i560.UserListRepository>()));
    gh.factory<_i853.GetCommentListUseCase>(
        () => _i853.GetCommentListUseCase(gh<_i477.CommentListRepository>()));
    gh.factory<_i854.PostCommentUseCase>(
        () => _i854.PostCommentUseCase(gh<_i477.CommentListRepository>()));
    gh.factory<_i855.DeleteCommentUseCase>(
        () => _i855.DeleteCommentUseCase(gh<_i477.CommentListRepository>()));
    gh.factory<_i856.CommentUploadFileUseCase>(() =>
        _i856.CommentUploadFileUseCase(gh<_i477.CommentListRepository>()));
    gh.factory<_i857.CreatingEformBloc>(
        () => _i857.CreatingEformBloc(gh<_i605.CreateEformUseCase>()));
    gh.factory<_i858.BranchSelectionBloc>(() => _i858.BranchSelectionBloc(
          gh<_i647.GetBranchSelectionUseCase>(),
          gh<_i640.SaveBranchSelectionUseCase>(),
          gh<_i645.GetSavedBranchSelectionUseCase>(),
          gh<_i639.RemoveBranchSelectionUseCase>(),
          gh<_i649.GetProvinceUseCase>(),
          gh<_i642.GetFloorBranchSelectionUseCase>(),
        ));
    gh.factory<_i859.StaffEvaluationPeriodsBloc>(
        () => _i859.StaffEvaluationPeriodsBloc(
              gh<_i513.GetStaffEvaluationPeriodsUseCase>(),
              gh<_i511.SaveStaffEvaluationPeriodsUseCase>(),
              gh<_i512.GetSavedStaffEvaluationPeriodsUseCase>(),
              gh<_i510.RemoveStaffEvaluationPeriodsUseCase>(),
            ));
    gh.factory<_i860.SelectingOfficeBloc>(
        () => _i860.SelectingOfficeBloc(gh<_i602.GetDepartmentUseCase>()));
    gh.factory<_i861.GetChoicesUseCase>(
        () => _i861.GetChoicesUseCase(gh<_i404.CheckinRepository>()));
    gh.factory<_i862.RequestUpdateHistoryCheckinUseCase>(() =>
        _i862.RequestUpdateHistoryCheckinUseCase(
            gh<_i404.CheckinRepository>()));
    gh.factory<_i863.GetBranchesUseCase>(
        () => _i863.GetBranchesUseCase(gh<_i404.CheckinRepository>()));
    gh.factory<_i864.GetCheckinTypesUseCase>(
        () => _i864.GetCheckinTypesUseCase(gh<_i404.CheckinRepository>()));
    gh.factory<_i865.GetMonthlyHistoryCheckinUseCase>(() =>
        _i865.GetMonthlyHistoryCheckinUseCase(gh<_i404.CheckinRepository>()));
    gh.factory<_i866.KpiEmployeeBloc>(() => _i866.KpiEmployeeBloc(
          gh<_i712.GetKpiEmployeeUseCase>(),
          gh<_i711.GetKpiEmployeeDetailUseCase>(),
        ));
    gh.factory<_i867.UserListBloc>(() => _i867.UserListBloc(
          gh<_i852.SaveUserListUseCase>(),
          gh<_i849.GetSavedUserListUseCase>(),
          gh<_i851.RemoveUserListUseCase>(),
          gh<_i182.UserLoadCreateChatGroupUseCase>(),
          gh<_i290.UpdateGroupChatDetailUseCase>(),
        ));
    gh.factory<_i868.BedSelectionBloc>(() => _i868.BedSelectionBloc(
          gh<_i648.GetRoomBranchSelectionUseCase>(),
          gh<_i642.GetFloorBranchSelectionUseCase>(),
          gh<_i646.GetBedBranchSelectionUseCase>(),
          gh<_i641.BedSelectBranchSelectionUseCase>(),
          gh<_i643.BedChangeBranchSelectionUseCase>(),
          gh<_i638.EmployeeGetBranchSelectionUseCase>(),
          gh<_i644.EstimateTimeGetBranchSelectionUseCase>(),
        ));
    gh.factory<_i869.CreatingTaskBloc>(() => _i869.CreatingTaskBloc(
          gh<_i406.CreatingTaskUseCase>(),
          gh<_i790.UploadUseCase>(),
        ));
    gh.factory<_i870.AssignPxRecheckUpdateUseCase>(() =>
        _i870.AssignPxRecheckUpdateUseCase(gh<_i393.PxRecheckRepository>()));
    gh.factory<_i871.RefuseBloc>(
        () => _i871.RefuseBloc(gh<_i609.RejectEformUseCase>()));
    gh.factory<_i872.NotificationsBloc>(() => _i872.NotificationsBloc(
          gh<_i657.GetNotificationsUseCase>(),
          gh<_i656.SearchNotificationsUseCase>(),
          gh<_i658.ReadAllNotificationUseCase>(),
        ));
    gh.factory<_i873.ConfirmOTPBloc>(() => _i873.ConfirmOTPBloc(
          gh<_i784.ConfirmOtpUseCase>(),
          gh<_i773.GetOtpUseCase>(),
          gh<_i241.GetKeyAppsFlyerUseCase>(),
        ));
    gh.factory<_i874.GetStatusListCustomerUseCase>(() =>
        _i874.GetStatusListCustomerUseCase(gh<_i434.ListCustomerRepository>()));
    gh.factory<_i875.GetSavedListCustomerUseCase>(() =>
        _i875.GetSavedListCustomerUseCase(gh<_i434.ListCustomerRepository>()));
    gh.factory<_i876.GetListCustomerUseCase>(
        () => _i876.GetListCustomerUseCase(gh<_i434.ListCustomerRepository>()));
    gh.factory<_i877.SaveListCustomerUseCase>(() =>
        _i877.SaveListCustomerUseCase(gh<_i434.ListCustomerRepository>()));
    gh.factory<_i878.RemoveListCustomerUseCase>(() =>
        _i878.RemoveListCustomerUseCase(gh<_i434.ListCustomerRepository>()));
    gh.factory<_i879.SearchListCustomerUseCase>(() =>
        _i879.SearchListCustomerUseCase(gh<_i434.ListCustomerRepository>()));
    gh.factory<_i880.RemoveMedicalTemplateListUseCase>(() =>
        _i880.RemoveMedicalTemplateListUseCase(
            gh<_i683.MedicalTemplateListRepository>()));
    gh.factory<_i881.MedicalTemplateDetailGetMedicalTemplateListUseCase>(() =>
        _i881.MedicalTemplateDetailGetMedicalTemplateListUseCase(
            gh<_i683.MedicalTemplateListRepository>()));
    gh.factory<_i882.GetMedicalTemplateListUseCase>(() =>
        _i882.GetMedicalTemplateListUseCase(
            gh<_i683.MedicalTemplateListRepository>()));
    gh.factory<_i883.GetSavedMedicalTemplateListUseCase>(() =>
        _i883.GetSavedMedicalTemplateListUseCase(
            gh<_i683.MedicalTemplateListRepository>()));
    gh.factory<_i884.SaveMedicalTemplateListUseCase>(() =>
        _i884.SaveMedicalTemplateListUseCase(
            gh<_i683.MedicalTemplateListRepository>()));
    gh.factory<_i885.HaPointListGetMedicalLogDetailUseCase>(() =>
        _i885.HaPointListGetMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i886.DoctorListGetMedicalLogDetailUseCase>(() =>
        _i886.DoctorListGetMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i887.GetTattooColorMedicalLogDetailUseCase>(() =>
        _i887.GetTattooColorMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i888.DosageListGetMedicalLogDetailUseCase>(() =>
        _i888.DosageListGetMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i889.MedicineListGetMedicalLogDetailUseCase>(() =>
        _i889.MedicineListGetMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i890.UpdateLogMedicalDetailUseCase>(() =>
        _i890.UpdateLogMedicalDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i891.RemoveMedicalLogDetailUseCase>(() =>
        _i891.RemoveMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i892.GetTattooTimeMedicalLogDetailUseCase>(() =>
        _i892.GetTattooTimeMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i893.KhacnhoListGetMedicalLogDetailUseCase>(() =>
        _i893.KhacnhoListGetMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i894.GetOriginStatusMedicalLogDetailUseCase>(() =>
        _i894.GetOriginStatusMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i895.GetSavedMedicalLogDetailUseCase>(() =>
        _i895.GetSavedMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i896.CreateLogMedicalDetailUseCase>(() =>
        _i896.CreateLogMedicalDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i897.GetSkinMachineMedicalLogDetailUseCase>(() =>
        _i897.GetSkinMachineMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i898.GetMedicalLogDetailUseCase>(() =>
        _i898.GetMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i899.GetPostSaiMedicalLogDetailUseCase>(() =>
        _i899.GetPostSaiMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i900.SaveMedicalLogDetailUseCase>(() =>
        _i900.SaveMedicalLogDetailUseCase(
            gh<_i517.MedicalLogDetailRepository>()));
    gh.factory<_i901.EformCategoryBloc>(
        () => _i901.EformCategoryBloc(gh<_i608.GetEformRequestTypeUseCase>()));
    gh.factory<_i902.NoteFinishPxRecheckUseCase>(() =>
        _i902.NoteFinishPxRecheckUseCase(gh<_i393.PxRecheckRepository>()));
    gh.factory<_i903.WorkStatusUpdatePxRecheckUseCase>(() =>
        _i903.WorkStatusUpdatePxRecheckUseCase(
            gh<_i393.PxRecheckRepository>()));
    gh.factory<_i904.AssignsFetchPxRecheckUseCase>(() =>
        _i904.AssignsFetchPxRecheckUseCase(gh<_i393.PxRecheckRepository>()));
    gh.factory<_i905.JobSchedulerBloc>(() => _i905.JobSchedulerBloc(
          gh<_i410.GetGeneralJobSchedulerUseCase>(),
          gh<_i408.GetJobSchedulerUseCase>(),
        ));
    gh.factory<_i906.CommentListBloc>(() => _i906.CommentListBloc(
          gh<_i853.GetCommentListUseCase>(),
          gh<_i856.CommentUploadFileUseCase>(),
          gh<_i854.PostCommentUseCase>(),
          gh<_i398.PostLikeCommentUseCase>(),
          gh<_i685.UpdateCommentUseCase>(),
          gh<_i855.DeleteCommentUseCase>(),
          gh<_i271.GetTagListUseCase>(),
        ));
    gh.factory<_i907.ListCustomerBloc>(() => _i907.ListCustomerBloc(
          gh<_i876.GetListCustomerUseCase>(),
          gh<_i877.SaveListCustomerUseCase>(),
          gh<_i875.GetSavedListCustomerUseCase>(),
          gh<_i878.RemoveListCustomerUseCase>(),
          gh<_i874.GetStatusListCustomerUseCase>(),
          gh<_i879.SearchListCustomerUseCase>(),
          gh<_i703.CheckoutCustomerInfoDetailsUseCase>(),
        ));
    gh.factory<_i908.StoryListBloc>(() => _i908.StoryListBloc(
          gh<_i466.GetStoryListUseCase>(),
          gh<_i468.PostStoryUseCase>(),
          gh<_i397.PostLikeStoryUseCase>(),
          gh<_i471.UpdateStoryUseCase>(),
          gh<_i475.DeleteStoryUseCase>(),
          gh<_i470.GetTotalNotificationUseCase>(),
          gh<_i467.GetStoryListSearchUseCase>(),
          gh<_i469.PutStoryVoteUseCase>(),
          gh<_i474.DeleteStoryVoteUseCase>(),
          gh<_i473.GetVoteUsersUseCase>(),
        ));
    gh.factory<_i909.DropDownStatusBloc>(
        () => _i909.DropDownStatusBloc(gh<_i453.GetDropDownStatusUseCase>()));
    gh.factory<_i910.BranchChatListBloc>(() => _i910.BranchChatListBloc(
          gh<_i693.GetBranchChatListUseCase>(),
          gh<_i696.SaveBranchChatListUseCase>(),
          gh<_i694.GetSavedBranchChatListUseCase>(),
          gh<_i695.RemoveBranchChatListUseCase>(),
        ));
    gh.factory<_i911.GetServiceAndProductActionsUseCase>(() =>
        _i911.GetServiceAndProductActionsUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i912.GetServiceAndProductUseCase>(() =>
        _i912.GetServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i913.SaveServiceAndProductUseCase>(() =>
        _i913.SaveServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i914.ServicesGetServiceAndProductUseCase>(() =>
        _i914.ServicesGetServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i915.ProductsGetServiceAndProductUseCase>(() =>
        _i915.ProductsGetServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i916.RemoveServiceAndProductUseCase>(() =>
        _i916.RemoveServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i917.GetSavedServiceAndProductUseCase>(() =>
        _i917.GetSavedServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i918.GetCategoryServiceAndProductUseCase>(() =>
        _i918.GetCategoryServiceAndProductUseCase(
            gh<_i508.ServiceAndProductRepository>()));
    gh.factory<_i919.TypeApprovingBloc>(() => _i919.TypeApprovingBloc(
          gh<_i606.ApprovingSignalEformUseCase>(),
          gh<_i790.UploadUseCase>(),
        ));
    gh.factory<_i920.MedicalTemplateListBloc>(
        () => _i920.MedicalTemplateListBloc(
              gh<_i882.GetMedicalTemplateListUseCase>(),
              gh<_i884.SaveMedicalTemplateListUseCase>(),
              gh<_i883.GetSavedMedicalTemplateListUseCase>(),
              gh<_i880.RemoveMedicalTemplateListUseCase>(),
              gh<_i881.MedicalTemplateDetailGetMedicalTemplateListUseCase>(),
            ));
    gh.factory<_i921.ProductConfirmBloc>(() => _i921.ProductConfirmBloc(
          gh<_i596.GetProductConfirmUseCase>(),
          gh<_i593.GetProductConfirmBranchUseCase>(),
        ));
    gh.factory<_i922.SelectingStaffBloc>(
        () => _i922.SelectingStaffBloc(gh<_i604.GetStaffUseCase>()));
    gh.factory<_i923.FunctionRoomBloc>(
        () => _i923.FunctionRoomBloc(gh<_i603.GetFunctionRoomUseCase>()));
    gh.factory<_i924.ListFetchByStaffConsultationManagerUseCase>(() =>
        _i924.ListFetchByStaffConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i925.RoomFetchConsultationManagerUseCase>(() =>
        _i925.RoomFetchConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i926.GetConsultationManagerUseCase>(() =>
        _i926.GetConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i927.RemoveConsultationManagerUseCase>(() =>
        _i927.RemoveConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i928.SaveConsultationManagerUseCase>(() =>
        _i928.SaveConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i929.DeleteServiceAssignUseCase>(() =>
        _i929.DeleteServiceAssignUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i930.AssignUpdateUseCase>(() =>
        _i930.AssignUpdateUseCase(gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i931.BedAssignConsultationManagerUseCase>(() =>
        _i931.BedAssignConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i932.GetSavedConsultationManagerUseCase>(() =>
        _i932.GetSavedConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i933.BedFetchConsultationManagerUseCase>(() =>
        _i933.BedFetchConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i934.DeleteServiceCustomerUseCase>(() =>
        _i934.DeleteServiceCustomerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i935.GetCustomerConsultationManagerUseCase>(() =>
        _i935.GetCustomerConsultationManagerUseCase(
            gh<_i650.ConsultationManagerRepository>()));
    gh.factory<_i936.SelectPxRoomBloc>(() => _i936.SelectPxRoomBloc(
          gh<_i838.GetRoomListCustomerUseCase>(),
          gh<_i433.SaveSelectPxRoomUseCase>(),
          gh<_i430.RemoveSelectPxRoomUseCase>(),
          gh<_i839.SaveCustomerRoomCodeUseCase>(),
          gh<_i431.RoomChangeSelectPxRoomUseCase>(),
        ));
    gh.factory<_i937.EmployeesInRoomUseCase>(() =>
        _i937.EmployeesInRoomUseCase(gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i938.EmployeesFetchPxUnasignedUpdateUseCase>(() =>
        _i938.EmployeesFetchPxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i939.AssignPxUnasignedUpdateUseCase>(() =>
        _i939.AssignPxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i940.WorksFetchPxUnasignedUpdateUseCase>(() =>
        _i940.WorksFetchPxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i941.GetPxUnasignedUpdateUseCase>(() =>
        _i941.GetPxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i942.SavePxUnasignedUpdateUseCase>(() =>
        _i942.SavePxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i943.RemovePxUnasignedUpdateUseCase>(() =>
        _i943.RemovePxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i944.GetSavedPxUnasignedUpdateUseCase>(() =>
        _i944.GetSavedPxUnasignedUpdateUseCase(
            gh<_i558.PxUnasignedUpdateRepository>()));
    gh.factory<_i945.HomeBloc>(() => _i945.HomeBloc(
          gh<_i562.GetServicesUseCase>(),
          gh<_i835.CheckLeaderHomeUseCase>(),
          gh<_i836.WorkLeaderCheckHomeUseCase>(),
          gh<_i838.GetRoomListCustomerUseCase>(),
        ));
    gh.factory<_i946.MedicineDetailBloc>(() => _i946.MedicineDetailBloc(
          gh<_i730.GetMedicineDetailUseCase>(),
          gh<_i731.SaveMedicineDetailUseCase>(),
          gh<_i729.GetSavedMedicineDetailUseCase>(),
          gh<_i725.RemoveMedicineDetailUseCase>(),
          gh<_i726.GetUnitMedicineDetailUseCase>(),
        ));
    gh.factory<_i947.TicketBloc>(() => _i947.TicketBloc(
          gh<_i443.GetTicketTicketv2UseCase>(),
          gh<_i440.TicketCreatedTypeUseCase>(),
          gh<_i445.GetMyTicketTicketv2UseCase>(),
          gh<_i444.TicketUploadFileUseCase>(),
          gh<_i442.CreateTicketUseCase>(),
          gh<_i448.TicketGroupTypeUseCase>(),
          gh<_i446.UpdateTicketUseCase>(),
          gh<_i616.GetTicketActiveUseCase>(),
          gh<_i441.GetTicketAllGroupUseCase>(),
          gh<_i447.GetTicketAllTypeUseCase>(),
        ));
    gh.factory<_i948.OrderFoodServiceBloc>(() => _i948.OrderFoodServiceBloc(
          gh<_i330.OrderFoodUploadUseCase>(),
          gh<_i536.OrderFoodCreateReportUseCase>(),
        ));
    gh.factory<_i949.DetailNotificationBloc>(() =>
        _i949.DetailNotificationBloc(gh<_i654.GetDetailNotificationUseCase>()));
    gh.factory<_i950.GetListSupportRequestUseCase>(() =>
        _i950.GetListSupportRequestUseCase(gh<_i697.RequestRepository>()));
    gh.factory<_i951.SendSupportRequestUseCase>(
        () => _i951.SendSupportRequestUseCase(gh<_i697.RequestRepository>()));
    gh.factory<_i952.EditHomeMenuBloc>(
        () => _i952.EditHomeMenuBloc(gh<_i562.GetServicesUseCase>()));
    gh.factory<_i953.MedicalServiceLogListBloc>(
        () => _i953.MedicalServiceLogListBloc(
              gh<_i831.GetMedicalServiceLogListUseCase>(),
              gh<_i829.SaveMedicalServiceLogListUseCase>(),
              gh<_i832.GetSavedMedicalServiceLogListUseCase>(),
              gh<_i830.RemoveMedicalServiceLogListUseCase>(),
            ));
    gh.factory<_i954.UserTicketBloc>(() => _i954.UserTicketBloc(
          gh<_i454.GetUserTicketUseCase>(),
          gh<_i791.GetUserInfoUseCase>(),
        ));
    gh.factory<_i955.ProfileBloc>(() => _i955.ProfileBloc(
          gh<_i780.GetProfileUseCase>(),
          gh<_i673.UploadAvatarUseCase>(),
          gh<_i785.UpdateBioUseCase>(),
          gh<_i674.UploadBackgroundUseCase>(),
        ));
    gh.factory<_i956.CustomerListBloc>(() => _i956.CustomerListBloc(
          gh<_i760.GetCustomerListUseCase>(),
          gh<_i759.SaveCustomerListUseCase>(),
          gh<_i761.GetSavedCustomerListUseCase>(),
          gh<_i762.RemoveCustomerListUseCase>(),
          gh<_i763.GetCustomerRelationShipListUseCase>(),
        ));
    gh.factory<_i957.CreateCustomerBloc>(() => _i957.CreateCustomerBloc(
          gh<_i587.GetCreateCustomerUseCase>(),
          gh<_i584.SaveCreateCustomerUseCase>(),
          gh<_i588.GetSavedCreateCustomerUseCase>(),
          gh<_i583.RemoveCreateCustomerUseCase>(),
          gh<_i581.GetProvinceCreateCustomerUseCase>(),
          gh<_i585.GetDistrictCreateCustomerUseCase>(),
          gh<_i586.GetWardCreateCustomerUseCase>(),
          gh<_i582.GetJobCreateCustomerUseCase>(),
          gh<_i487.GetChatSelectBranchUseCase>(),
          gh<_i589.UpdateCreateCustomerUseCase>(),
          gh<_i590.SurveyLoadCreateCustomerUseCase>(),
          gh<_i591.CustomerSearchCreateCustomerUseCase>(),
        ));
    gh.factory<_i958.TicketDetailBloc>(() => _i958.TicketDetailBloc(
          gh<_i566.GetTicketDetailUseCase>(),
          gh<_i565.SaveTicketDetailUseCase>(),
          gh<_i568.GetSavedTicketDetailUseCase>(),
          gh<_i572.RemoveTicketDetailUseCase>(),
          gh<_i567.GetTicketDetailReasonUseCase>(),
          gh<_i571.ConfirmTicketDetailUseCase>(),
          gh<_i617.CreateTicketActiveUseCase>(),
          gh<_i569.ReceptTicketDetailUseCase>(),
          gh<_i444.TicketUploadFileUseCase>(),
          gh<_i616.GetTicketActiveUseCase>(),
          gh<_i570.TicketDetailReworkUseCase>(),
        ));
    gh.factory<_i959.SetPasswordBloc>(
        () => _i959.SetPasswordBloc(gh<_i781.ResetPasswordUseCase>()));
    gh.factory<_i960.CheckinPhotoBloc>(() => _i960.CheckinPhotoBloc(
          gh<_i686.GetCheckinPhotoUseCase>(),
          gh<_i688.SaveCheckinPhotoUseCase>(),
          gh<_i687.GetSavedCheckinPhotoUseCase>(),
          gh<_i689.RemoveCheckinPhotoUseCase>(),
        ));
    gh.factory<_i961.StoryWriteBloc>(() => _i961.StoryWriteBloc(
          gh<_i476.GetStoryRuleUseCase>(),
          gh<_i472.SocialUploadFileUseCase>(),
          gh<_i471.UpdateStoryUseCase>(),
          gh<_i620.GetLocationGoogleUseCase>(),
          gh<_i621.GetAddressAddressLocationGoogleUseCase>(),
          gh<_i619.GetAddressNearLocationGoogleUseCase>(),
          gh<_i622.GetAddressSearchLocationGoogleUseCase>(),
          gh<_i465.GetEmojiListUseCase>(),
        ));
    gh.factory<_i962.AuthenticationBloc>(() => _i962.AuthenticationBloc(
          gh<_i783.GetConfigurationUseCase>(),
          gh<_i797.HasTokenUseCase>(),
          gh<_i778.GetProfilesUseCase>(),
          gh<_i368.ClearCacheUseCase>(),
          gh<_i796.IsShowBoardingUseCase>(),
          gh<_i364.SetOnboardingUseCase>(),
          gh<_i679.CheckinPermissionCheckUserUseCase>(),
          gh<_i372.CheckinUseCase>(),
          gh<_i248.GetLatitudeUseCase>(),
          gh<_i251.GetLongitudeUseCase>(),
          gh<_i776.LogoutUseCase>(),
          gh<_i799.CacheUserUseCase>(),
          gh<_i672.UploadCheckInImageUseCase>(),
          gh<_i798.CacheLoginStringeeGetUseCase>(),
          gh<_i598.SocketAccessTokenGetLoginUseCase>(),
        ));
    gh.factory<_i963.CustomerScheduleBloc>(() => _i963.CustomerScheduleBloc(
          gh<_i735.GetCustomerScheduleUseCase>(),
          gh<_i732.SaveCustomerScheduleUseCase>(),
          gh<_i733.GetSavedCustomerScheduleUseCase>(),
          gh<_i734.RemoveCustomerScheduleUseCase>(),
        ));
    gh.factory<_i964.CustomerBloc>(() => _i964.CustomerBloc(
          gh<_i667.GetCustomerBookingInfoUseCase>(),
          gh<_i837.CheckinCustomerUseCase>(),
          gh<_i668.BookedServicesFetchCustomerBookingInfoUseCase>(),
          gh<_i838.GetRoomListCustomerUseCase>(),
          gh<_i840.PrintCustomerUseCase>(),
        ));
    gh.factory<_i965.ApprovingOtpBloc>(
        () => _i965.ApprovingOtpBloc(gh<_i607.ApprovingOtpEformUseCase>()));
    gh.factory<_i966.UserProfileBloc>(
        () => _i966.UserProfileBloc(gh<_i799.CacheUserUseCase>()));
    gh.factory<_i967.DeleteAssignTaskUseCase>(
        () => _i967.DeleteAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i968.UpdateAssignTaskUseCase>(
        () => _i968.UpdateAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i969.GetSavedAssignTaskUseCase>(() =>
        _i969.GetSavedAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i970.CreateAssignTaskUseCase>(
        () => _i970.CreateAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i971.SaveAssignTaskUseCase>(
        () => _i971.SaveAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i972.GetAssignTaskUseCase>(
        () => _i972.GetAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i973.RemoveAssignTaskUseCase>(
        () => _i973.RemoveAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i974.GetStaffAssignTaskUseCase>(() =>
        _i974.GetStaffAssignTaskUseCase(gh<_i659.AssignTaskRepository>()));
    gh.factory<_i975.CustomerInfoDetailsBloc>(
        () => _i975.CustomerInfoDetailsBloc(
              gh<_i702.GetCustomerInfoDetailsUseCase>(),
              gh<_i701.SaveCustomerInfoDetailsUseCase>(),
              gh<_i704.GetSavedCustomerInfoDetailsUseCase>(),
              gh<_i705.RemoveCustomerInfoDetailsUseCase>(),
              gh<_i703.CheckoutCustomerInfoDetailsUseCase>(),
            ));
    gh.factory<_i976.LoginBloc>(() => _i976.LoginBloc(
          gh<_i773.GetOtpUseCase>(),
          gh<_i365.HasUserDataUseCase>(),
          gh<_i774.CheckPhoneUseCase>(),
          gh<_i775.LoginUseCase>(),
          gh<_i799.CacheUserUseCase>(),
          gh<_i786.LoginSocialUseCase>(),
          gh<_i598.SocketAccessTokenGetLoginUseCase>(),
          gh<_i681.CheckPermissionUserUseCase>(),
        ));
    gh.factory<_i977.MonthlyHistoryCheckinBloc>(() =>
        _i977.MonthlyHistoryCheckinBloc(
            gh<_i865.GetMonthlyHistoryCheckinUseCase>()));
    gh.factory<_i978.ConsultationHistoryBloc>(() =>
        _i978.ConsultationHistoryBloc(
            gh<_i716.GetConsultationHistoryCustomerProfileUseCase>()));
    gh.factory<_i979.CustomerProfileBloc>(() => _i979.CustomerProfileBloc(
          gh<_i717.GetCustomerProfileUseCase>(),
          gh<_i714.SaveCustomerProfileUseCase>(),
          gh<_i718.GetSavedCustomerProfileUseCase>(),
          gh<_i715.RemoveCustomerProfileUseCase>(),
        ));
    gh.factory<_i980.CustomerBookingInfoBloc>(
        () => _i980.CustomerBookingInfoBloc(
              gh<_i667.GetCustomerBookingInfoUseCase>(),
              gh<_i666.SaveCustomerBookingInfoUseCase>(),
              gh<_i669.GetSavedCustomerBookingInfoUseCase>(),
              gh<_i664.RemoveCustomerBookingInfoUseCase>(),
              gh<_i668.BookedServicesFetchCustomerBookingInfoUseCase>(),
              gh<_i663.SuggestServicesFetchCustomerBookingInfoUseCase>(),
              gh<_i665.ServiceDetailsLoadCustomerBookingInfoUseCase>(),
              gh<_i838.GetRoomListCustomerUseCase>(),
              gh<_i297.GetServiceInsideTicketUseCase>(),
            ));
    gh.factory<_i981.AddTagsImageBloc>(() => _i981.AddTagsImageBloc(
          gh<_i635.GetAddTagsImageUseCase>(),
          gh<_i630.SaveAddTagsImageUseCase>(),
          gh<_i631.GetSavedAddTagsImageUseCase>(),
          gh<_i632.RemoveAddTagsImageUseCase>(),
          gh<_i636.GetImageListAddTagsImageUseCase>(),
          gh<_i838.GetRoomListCustomerUseCase>(),
          gh<_i760.GetCustomerListUseCase>(),
          gh<_i633.GetTagListAddTagsImageUseCase>(),
          gh<_i628.CreateImageTagAddTagsImageUseCase>(),
          gh<_i629.CreateMergeImageAddTagsImageUseCase>(),
        ));
    gh.factory<_i982.RatingHumanBloc>(() => _i982.RatingHumanBloc(
          gh<_i723.GetRatingHumanUseCase>(),
          gh<_i721.GetQuestionDetailUseCase>(),
          gh<_i722.SaveRatingHumanUseCase>(),
          gh<_i720.SubmitRatingHumanUseCase>(),
        ));
    gh.factory<_i983.ConsultationHistoryDetailBloc>(
        () => _i983.ConsultationHistoryDetailBloc(
              gh<_i713.CreateConsultationCustomerProfileUseCase>(),
              gh<_i719.UpdateConsultationCustomerProfileUseCase>(),
              gh<_i638.EmployeeGetBranchSelectionUseCase>(),
            ));
    gh.factory<_i984.GeneralBloc>(() => _i984.GeneralBloc(
          gh<_i823.UploadRecordTakingCareCustomerUseCase>(),
          gh<_i799.CacheUserUseCase>(),
          gh<_i671.UploadFeedbackUseCase>(),
          gh<_i799.CacheUserUseCase>(),
          gh<_i748.SendFeedbackUseCase>(),
          gh<_i793.CacheQuickActionGetUseCase>(),
          gh<_i792.CacheQuickActionRemoveUseCase>(),
          gh<_i162.GetConversationByInviteIdChatListUseCase>(),
          gh<_i155.JoinGroupChatListUseCase>(),
          gh<_i377.UniversalQrScanUseCase>(),
        ));
    gh.factory<_i985.PxUnasignedBloc>(() => _i985.PxUnasignedBloc(
          gh<_i765.GetPxCustomerListUseCase>(),
          gh<_i766.SavePxUnasignedUseCase>(),
          gh<_i767.GetSavedPxUnasignedUseCase>(),
          gh<_i764.RemovePxUnasignedUseCase>(),
        ));
    gh.factory<_i986.DetailCrmCustomerBloc>(() => _i986.DetailCrmCustomerBloc(
          gh<_i799.CacheUserUseCase>(),
          gh<_i542.GetDetailCrmCustomerUseCase>(),
          gh<_i543.SaveDetailCrmCustomerUseCase>(),
          gh<_i548.GetSavedDetailCrmCustomerUseCase>(),
          gh<_i545.RemoveDetailCrmCustomerUseCase>(),
          gh<_i541.AdviceFetchDetailCrmCustomerUseCase>(),
          gh<_i540.ServiceFetchDetailCrmCustomerUseCase>(),
          gh<_i546.CallLogFetchDetailCrmCustomerUseCase>(),
          gh<_i555.MessageLogFetchDetailCrmCustomerUseCase>(),
          gh<_i549.BookingLogFetchDetailCrmCustomerUseCase>(),
          gh<_i553.AdviceTypeFetchDetailCrmCustomerUseCase>(),
          gh<_i552.AdviceUpdateDetailCrmCustomerUseCase>(),
          gh<_i547.BranchLoadDetailCrmCustomerUseCase>(),
          gh<_i539.PromotionLoadDetailCrmCustomerUseCase>(),
          gh<_i538.RoomLoadDetailCrmCustomerUseCase>(),
          gh<_i551.TimeLoadDetailCrmCustomerUseCase>(),
          gh<_i544.ServiceLoadDetailCrmCustomerUseCase>(),
          gh<_i556.NumberBookingLoadDetailCrmCustomerUseCase>(),
          gh<_i554.BookingDetailLoadDetailCrmCustomerUseCase>(),
          gh<_i550.BookDetailCrmCustomerUseCase>(),
          gh<_i557.BookingLoadDetailCrmCustomerUseCase>(),
        ));
    gh.factory<_i987.FeedbackBloc>(() => _i987.FeedbackBloc(
          gh<_i787.SubmitFeedbackUseCase>(),
          gh<_i790.UploadUseCase>(),
          gh<_i799.CacheUserUseCase>(),
        ));
    gh.factory<_i988.PxUnasignedUpdateBloc>(() => _i988.PxUnasignedUpdateBloc(
          gh<_i941.GetPxUnasignedUpdateUseCase>(),
          gh<_i942.SavePxUnasignedUpdateUseCase>(),
          gh<_i944.GetSavedPxUnasignedUpdateUseCase>(),
          gh<_i943.RemovePxUnasignedUpdateUseCase>(),
          gh<_i940.WorksFetchPxUnasignedUpdateUseCase>(),
          gh<_i938.EmployeesFetchPxUnasignedUpdateUseCase>(),
          gh<_i939.AssignPxUnasignedUpdateUseCase>(),
          gh<_i814.GetSectionTakingCareCustomerUseCase>(),
        ));
    gh.factory<_i989.MedicalProductCreationBloc>(
        () => _i989.MedicalProductCreationBloc(
              gh<_i523.MedicalProductCreationUseCase>(),
              gh<_i522.SaveMedicalProductCreationUseCase>(),
              gh<_i520.GetSavedMedicalProductCreationUseCase>(),
              gh<_i521.RemoveMedicalProductCreationUseCase>(),
              gh<_i519.ProductsMedicalProductCreationUseCase>(),
              gh<_i911.GetServiceAndProductActionsUseCase>(),
            ));
    gh.factory<_i990.ServiceDetailBloc>(() => _i990.ServiceDetailBloc(
          gh<_i814.GetSectionTakingCareCustomerUseCase>(),
          gh<_i323.GetTreatmentDetailUseCase>(),
          gh<_i303.CreateTreatmentDetailUseCase>(),
          gh<_i677.EmployeeFetchServiceDetailUseCase>(),
          gh<_i676.DoctorFetchServiceDetailUseCase>(),
          gh<_i308.GetTreatmentOMDetailUseCase>(),
          gh<_i295.CreateTreatmentOMDetailUseCase>(),
          gh<_i937.EmployeesInRoomUseCase>(),
          gh<_i305.GetTreatmentNoteUseCase>(),
          gh<_i315.UpdateTreatmentNoteUseCase>(),
          gh<_i309.GetResultOfFitUseCase>(),
          gh<_i317.UpdateResultOfFitUseCase>(),
          gh<_i296.DeleteResultOfFitUseCase>(),
          gh<_i941.GetPxUnasignedUpdateUseCase>(),
          gh<_i321.UpdateTreatmentDetailUseCase>(),
          gh<_i306.GetServiceUsageConsultationCustomerUseCase>(),
        ));
    gh.factory<_i991.ActionAttendanceBloc>(() => _i991.ActionAttendanceBloc(
          gh<_i864.GetCheckinTypesUseCase>(),
          gh<_i862.RequestUpdateHistoryCheckinUseCase>(),
          gh<_i863.GetBranchesUseCase>(),
          gh<_i487.GetChatSelectBranchUseCase>(),
        ));
    gh.factory<_i992.PxTaskListBloc>(() => _i992.PxTaskListBloc(
          gh<_i765.GetPxCustomerListUseCase>(),
          gh<_i530.SavePxTaskListUseCase>(),
          gh<_i529.GetSavedPxTaskListUseCase>(),
          gh<_i528.RemovePxTaskListUseCase>(),
          gh<_i814.GetSectionTakingCareCustomerUseCase>(),
          gh<_i904.AssignsFetchPxRecheckUseCase>(),
        ));
    gh.factory<_i993.TagImageBloc>(() => _i993.TagImageBloc(
          gh<_i838.GetRoomListCustomerUseCase>(),
          gh<_i846.GetComboTagUsecase>(),
          gh<_i847.GetImageByComboTagUsecae>(),
          gh<_i845.DeleteImageByComboTagUsecase>(),
          gh<_i844.DeleteTagByComboTagUsecase>(),
        ));
    gh.factory<_i994.CreateSupportRequestsBloc>(() =>
        _i994.CreateSupportRequestsBloc(gh<_i951.SendSupportRequestUseCase>()));
    gh.factory<_i995.MedicalLogDetailBloc>(() => _i995.MedicalLogDetailBloc(
          gh<_i898.GetMedicalLogDetailUseCase>(),
          gh<_i900.SaveMedicalLogDetailUseCase>(),
          gh<_i895.GetSavedMedicalLogDetailUseCase>(),
          gh<_i891.RemoveMedicalLogDetailUseCase>(),
          gh<_i896.CreateLogMedicalDetailUseCase>(),
          gh<_i890.UpdateLogMedicalDetailUseCase>(),
          gh<_i897.GetSkinMachineMedicalLogDetailUseCase>(),
          gh<_i899.GetPostSaiMedicalLogDetailUseCase>(),
          gh<_i790.UploadUseCase>(),
          gh<_i889.MedicineListGetMedicalLogDetailUseCase>(),
          gh<_i888.DosageListGetMedicalLogDetailUseCase>(),
          gh<_i885.HaPointListGetMedicalLogDetailUseCase>(),
          gh<_i893.KhacnhoListGetMedicalLogDetailUseCase>(),
          gh<_i638.EmployeeGetBranchSelectionUseCase>(),
          gh<_i886.DoctorListGetMedicalLogDetailUseCase>(),
          gh<_i894.GetOriginStatusMedicalLogDetailUseCase>(),
          gh<_i887.GetTattooColorMedicalLogDetailUseCase>(),
          gh<_i892.GetTattooTimeMedicalLogDetailUseCase>(),
          gh<_i727.CreateMedicineDetailUseCase>(),
          gh<_i728.UpdateMedicineDetailUseCase>(),
        ));
    gh.factory<_i996.SettingBloc>(() => _i996.SettingBloc(
          gh<_i682.UserDeletionUseCase>(),
          gh<_i789.ChangePasswordUseCase>(),
          gh<_i675.UploadKYCUseCase>(),
          gh<_i678.SendKycPhotosSettingUseCase>(),
        ));
    gh.factory<_i997.ConsultationCustomerBloc>(
        () => _i997.ConsultationCustomerBloc(
              gh<_i312.GetConsultationCustomerUseCase>(),
              gh<_i314.SaveConsultationCustomerUseCase>(),
              gh<_i310.GetSavedConsultationCustomerUseCase>(),
              gh<_i301.RemoveConsultationCustomerUseCase>(),
              gh<_i307.GetServiceConsultationCustomerUseCase>(),
              gh<_i316.GetActionConsultationCustomerUseCase>(),
              gh<_i319.CompleteConsultationCustomerUseCase>(),
              gh<_i302.ProductLoadConsultationCustomerUseCase>(),
              gh<_i295.CreateTreatmentOMDetailUseCase>(),
              gh<_i838.GetRoomListCustomerUseCase>(),
              gh<_i304.GetFitCustomerInfoUseCase>(),
              gh<_i313.UpdateFitCustomerInfoUseCase>(),
              gh<_i299.GetResultListOfFitUseCase>(),
              gh<_i297.GetServiceInsideTicketUseCase>(),
              gh<_i311.UpdateSkinCustomerInfoUseCase>(),
              gh<_i300.GetSkinCustomerInfoUseCase>(),
              gh<_i322.RemoveServiceConsultationCustomerUseCase>(),
              gh<_i320.EditServiceConsultationCustomerUseCase>(),
              gh<_i318.UpdateConsultationTTBDUseCase>(),
              gh<_i298.GetConsultationNDTVUseCase>(),
            ));
    gh.factory<_i998.MedicalServiceCreationBloc>(
        () => _i998.MedicalServiceCreationBloc(
              gh<_i746.MedicalServiceCreationUseCase>(),
              gh<_i745.SaveMedicalServiceCreationUseCase>(),
              gh<_i744.GetSavedMedicalServiceCreationUseCase>(),
              gh<_i741.RemoveMedicalServiceCreationUseCase>(),
              gh<_i742.ServicesMedicalServiceCreationUseCase>(),
              gh<_i743.MethodsMedicalServiceCreationUseCase>(),
              gh<_i911.GetServiceAndProductActionsUseCase>(),
            ));
    gh.factory<_i999.MoreBloc>(() => _i999.MoreBloc(
          gh<_i671.UploadFeedbackUseCase>(),
          gh<_i778.GetProfilesUseCase>(),
          gh<_i748.SendFeedbackUseCase>(),
        ));
    gh.factory<_i1000.PxRecheckBloc>(() => _i1000.PxRecheckBloc(
          gh<_i765.GetPxCustomerListUseCase>(),
          gh<_i766.SavePxUnasignedUseCase>(),
          gh<_i767.GetSavedPxUnasignedUseCase>(),
          gh<_i764.RemovePxUnasignedUseCase>(),
          gh<_i904.AssignsFetchPxRecheckUseCase>(),
          gh<_i902.NoteFinishPxRecheckUseCase>(),
          gh<_i938.EmployeesFetchPxUnasignedUpdateUseCase>(),
          gh<_i870.AssignPxRecheckUpdateUseCase>(),
          gh<_i903.WorkStatusUpdatePxRecheckUseCase>(),
        ));
    gh.factory<_i1001.PxListBloc>(() => _i1001.PxListBloc(
          gh<_i827.SavePxListUseCase>(),
          gh<_i825.GetSavedPxListUseCase>(),
          gh<_i828.RemovePxListUseCase>(),
        ));
    gh.factory<_i1002.HistoryCheckinBloc>(() => _i1002.HistoryCheckinBloc(
          gh<_i862.RequestUpdateHistoryCheckinUseCase>(),
          gh<_i493.FetchHistoryCheckinUseCase>(),
        ));
    gh.factory<_i1003.SupportRequestsBloc>(() =>
        _i1003.SupportRequestsBloc(gh<_i950.GetListSupportRequestUseCase>()));
    gh.factory<_i1004.AssignTaskBloc>(() => _i1004.AssignTaskBloc(
          gh<_i972.GetAssignTaskUseCase>(),
          gh<_i971.SaveAssignTaskUseCase>(),
          gh<_i969.GetSavedAssignTaskUseCase>(),
          gh<_i973.RemoveAssignTaskUseCase>(),
          gh<_i974.GetStaffAssignTaskUseCase>(),
          gh<_i970.CreateAssignTaskUseCase>(),
          gh<_i967.DeleteAssignTaskUseCase>(),
          gh<_i968.UpdateAssignTaskUseCase>(),
        ));
    gh.factory<_i1005.ServiceAndProductBloc>(() => _i1005.ServiceAndProductBloc(
          gh<_i912.GetServiceAndProductUseCase>(),
          gh<_i913.SaveServiceAndProductUseCase>(),
          gh<_i917.GetSavedServiceAndProductUseCase>(),
          gh<_i916.RemoveServiceAndProductUseCase>(),
          gh<_i918.GetCategoryServiceAndProductUseCase>(),
          gh<_i914.ServicesGetServiceAndProductUseCase>(),
          gh<_i915.ProductsGetServiceAndProductUseCase>(),
        ));
    gh.factory<_i1006.ConsultationManagerBloc>(
        () => _i1006.ConsultationManagerBloc(
              gh<_i926.GetConsultationManagerUseCase>(),
              gh<_i799.CacheUserUseCase>(),
              gh<_i826.GetPxListUseCase>(),
              gh<_i928.SaveConsultationManagerUseCase>(),
              gh<_i932.GetSavedConsultationManagerUseCase>(),
              gh<_i927.RemoveConsultationManagerUseCase>(),
              gh<_i935.GetCustomerConsultationManagerUseCase>(),
              gh<_i838.GetRoomListCustomerUseCase>(),
              gh<_i431.RoomChangeSelectPxRoomUseCase>(),
              gh<_i933.BedFetchConsultationManagerUseCase>(),
              gh<_i931.BedAssignConsultationManagerUseCase>(),
              gh<_i938.EmployeesFetchPxUnasignedUpdateUseCase>(),
              gh<_i930.AssignUpdateUseCase>(),
              gh<_i924.ListFetchByStaffConsultationManagerUseCase>(),
              gh<_i929.DeleteServiceAssignUseCase>(),
              gh<_i934.DeleteServiceCustomerUseCase>(),
            ));
    return this;
  }
}

class _$RegisterModule extends _i1007.RegisterModule {}
