part of 'tag_image_bloc.dart';

enum TagImageStatus {
  init,
  loading,
  success,
  refreshSuccess,
  loadingRefreshRoom,
  loadingGetMore,
  getMoreSuccess,
  comboTagSuccess,
  imageByComboTagSuccess,
  uploadSuccess,
  deleteImageSuccess,
  deleteTagSuccess,
  loadingDeleteImage,
  loadingDeleteTag,
  failure,
}

@immutable
class TagImageState extends Equatable {
  const TagImageState(
    this.status, {
    this.data,
    this.roomCode,
    this.idxImageByDate,
    this.idImageByDate,
  });
  final dynamic data;
  final TagImageStatus status;
  final String? roomCode;
  final int? idxImageByDate;
  final String? idImageByDate;

  TagImageState copyWith<T>(
    final TagImageStatus status, {
    final dynamic data,
    final String? roomCode,
    final int? idxImageByDate,
    final String? idImageByDate,
  }) {
    return TagImageState(
      status,
      data: data,
      roomCode: roomCode,
      idxImageByDate: idxImageByDate,
      idImageByDate: idImageByDate,
    );
  }

  @override
  List<Object?> get props => [
    status,
    data,
    roomCode,
    idxImageByDate,
    idImageByDate,
  ];
}
