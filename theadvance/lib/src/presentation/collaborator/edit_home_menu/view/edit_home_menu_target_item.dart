// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../data/models/api_models.dart';
import '../../../widgets/value_notifier_list.dart';

class EditHomeMenuTargetItem extends StatefulWidget {
  const EditHomeMenuTargetItem({
    super.key,
    required this.dragTarget,
    required this.index,
  });
  final int index;
  final ValueNotifierList<ServiceItemsModel> dragTarget;
  @override
  State<EditHomeMenuTargetItem> createState() => _EditHomeMenuTargetItemState();
}

class _EditHomeMenuTargetItemState extends State<EditHomeMenuTargetItem> {
  bool isAccept = false;
  @override
  Widget build(final BuildContext context) {
    return DragTarget<ServiceItemsModel>(
      builder: (final context, final candidateData, final rejectedData) =>
          Center(
            child: SizedBox.square(
              dimension: 75,
              child: ColoredBox(
                color: isAccept
                    ? Theme.of(context).primaryColor.withValues(alpha: .3)
                    : Colors.white,
                child: Center(
                  child: SizedBox.square(
                    dimension: 45,
                    child: Stack(
                      children: [
                        Positioned.fill(
                          child: EZResources.image(
                            ImageParams(name: AppIcons.icBorderDrag),
                          ),
                        ),
                        Center(
                          child: EZResources.image(
                            ImageParams(name: AppIcons.icAddGreen),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
      onAcceptWithDetails: (final details) {
        widget.dragTarget.updateValuebyIndex(widget.index, details.data);
      },
      onWillAcceptWithDetails: (final details) {
        setState(() {
          isAccept = true;
        });

        return true;
      },
      onLeave: (final data) {
        setState(() {
          isAccept = false;
        });
      },
    );
  }
}
