// ignore_for_file: public_member_api_docs, sort_constructors_first

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:extended_image/extended_image.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/params/alert_params.dart';
import '../../../core/utils/alert.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../product_confirm/view/product_confirm_page.dart';
import '../../story_list/widgets/story_image.dart';
import '../../widgets/value_notifier_list.dart';

enum TagByComboTagAction { remove, removeTag }

@RoutePage()
class PreviewImagePage extends StatefulWidget {
  const PreviewImagePage({
    final Key? key,
    required this.initIndex,
    required this.roomName,
    this.isShowAction = false,
    this.isShowbottom = false,
  }) : super(key: key);
  final int initIndex;
  final String roomName;
  final bool isShowAction;
  final bool isShowbottom;
  static ValueNotifierList<Widget> listMedia = ValueNotifierList([]);
  static List<ImageByComboTagImages?> images = [];
  static late PageController pageController;
  @override
  State<PreviewImagePage> createState() => _PreviewImagePageState();
}

class _PreviewImagePageState extends State<PreviewImagePage> {
  final ValueNotifierList<ComboTag> tags = ValueNotifierList([]);
  @override
  void initState() {
    StoryImage.currentIdx = widget.initIndex;
    PreviewImagePage.pageController = PageController(
      initialPage: widget.initIndex,
    );
    if (PreviewImagePage.images.isNotEmpty) {
      tags.setValue(PreviewImagePage.images[widget.initIndex]?.tags);
    }

    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actionsPadding: const EdgeInsets.all(8.0),
        backgroundColor: Colors.black,
        centerTitle: true,
        title: Text(
          widget.roomName,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          onPressed: () async {
            context.router.popForced();
          },
          icon: EZResources.image(
            ImageParams(name: AppIcons.icBack, color: Colors.white),
          ),
        ),
        actions: widget.isShowAction
            ? [
                GestureDetector(
                  onTapDown: (final details) => showMenu(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    menuPadding: const EdgeInsets.symmetric(horizontal: 4),
                    color: Colors.white,
                    context: context,
                    position: RelativeRect.fromLTRB(
                      MediaQuery.sizeOf(context).width / 2,
                      70 + 10,
                      10,
                      MediaQuery.sizeOf(context).height -
                          details.globalPosition.dy,
                    ),
                    items: [
                      PopupMenuItem(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        onTap: () {
                          Alert.showAlertConfirmV2(
                            AlertConfirmParams(
                              title: context.l10n.newUpdate,
                              onPressedCancel: () {
                                context.router.popForced();
                              },
                              context,
                              message: context.l10n.doYouDeleteTags,
                              onPressed: () {
                                context.router.popForced();
                                context.router.popForced(
                                  TagByComboTagAction.remove,
                                );
                              },
                              confirmText: context.l10n.confirm,
                              cancelButton: context.l10n.cancel,
                              image: EZResources.image(
                                ImageParams(name: AppIcons.icTrashGreen),
                              ),
                              gradient: gradient_bg2,
                            ),
                          );
                        },

                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(context.l10n.deleteTag),
                            EZResources.image(
                              ImageParams(
                                name: AppIcons.icRemoveTag,
                                size: const ImageSize.square(18),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // PopupMenuItem(
                      //   child: SizedBox(
                      //     width: 170,
                      //     child: Row(
                      //       mainAxisAlignment:
                      // MainAxisAlignment.spaceBetween,
                      //       children: [
                      //         const Text(
                      //           'Xoa tag',
                      //           style: TextStyle(color: Colors.red),
                      //         ),
                      //         EZResources.image(
                      //           ImageParams(
                      //             name: AppIcons.icTrashRed,
                      //             size: const ImageSize.square(18),
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      //   onTap: () {
                      //     context.router.popForced(
                      //       TagByComboTagAction.removeTag,
                      //     );
                      //   },
                      // ),
                    ],
                  ),
                  child: EZResources.image(
                    ImageParams(
                      name: AppIcons.icThreedot,
                      //    size: const ImageSize.square(18),
                      color: Colors.white,
                    ),
                  ),
                ),
              ]
            : null,
      ),
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          ValueListenableBuilder(
            valueListenable: PreviewImagePage.listMedia,
            builder: (final context, final vListAttachment, final child) {
              return Theme(
                data: ThemeData(
                  // ignore: deprecated_member_use
                  dialogBackgroundColor: Colors.black,
                ),
                child: ExtendedImageSlidePage(
                  slideAxis: SlideAxis.vertical,
                  slideType: SlideType.wholePage,
                  child: ExtendedImageGesturePageView.builder(
                    controller: ExtendedPageController(
                      initialPage: widget.initIndex,
                    ),
                    onPageChanged: (final val) {
                      StoryImage.currentIdx = val;
                      if (PreviewImagePage.images.isNotEmpty) {
                        tags.setValue(PreviewImagePage.images[val]?.tags);
                      }
                    },
                    itemCount: vListAttachment.length,
                    itemBuilder: (final context, final index) =>
                        vListAttachment[index],
                  ),
                ),
              );
            },
          ),
          if (widget.isShowbottom)
            Align(
              alignment: Alignment.bottomLeft,
              child: ValueListenableBuilder(
                valueListenable: tags,
                builder: (final context, final vTags, final child) {
                  return vTags.isNotEmpty
                      ? SizedBox(
                          width: double.infinity,
                          child: ColoredBox(
                            color: Colors.black.withValues(alpha: .8),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 16,
                              ),
                              child: Text(
                                maxLines: 2,
                                'Tags: ${_displayTagName(vTags)}',
                                style: Theme.of(context).textTheme.labelLarge
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontSize: 16,
                                    ),
                              ),
                            ),
                          ),
                        )
                      : const SizedBox.shrink();
                },
              ),
            ),
        ],
      ),
    );
  }

  String _displayTagName(final List<ComboTag> vTags) => vTags
      .where((final eW) => eW.tagId != null)
      .map((final tag) => tag.tagName)
      .join(', ');
}
