// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' show PreviewData;
import 'package:flutter_link_previewer/flutter_link_previewer.dart';
import 'package:linkfy_text/linkfy_text.dart';
import 'package:readmore/readmore.dart';

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../domain/entities/mention_entity.dart';
import '../../settings/fonts/fonts_bloc.dart';

class CommentDetectableText extends StatefulWidget {
  const CommentDetectableText({
    super.key,
    required this.content,
    required this.mentions,
    this.style,
    this.previewDataList = const <String, PreviewData>{},
    this.isCollapsed,
    this.isTicket = false,
  });
  final TextStyle? style;
  final String content;
  final List<MentionEntity> mentions;
  final Map<String, PreviewData> previewDataList;
  final ValueNotifier<bool>? isCollapsed;
  final bool isTicket;
  @override
  State<CommentDetectableText> createState() => _CommentDetectableTextState();
}

class _CommentDetectableTextState extends State<CommentDetectableText> {
  final Map<String, PreviewData> previewDataList = <String, PreviewData>{};
  late Iterable<Match> matches;
  final List<InlineSpan> textSpanChildren = <InlineSpan>[];
  @override
  void initState() {
    final RegExp pattern = RegExp(Strings.regExpUrl, caseSensitive: false);
    matches = pattern.allMatches(widget.content);
    widget.content.splitMapJoin(
      RegExp(
        '${Strings.regExpStory}|${Strings.regExpUrl}|'
        '${Strings.hotline}'
        '|(<em>.*?)(.*?)(.*?</em>)'
        '|(<(strong)>.*?)(.*?)(.*?</strong>)'
        '|(<u>.*?)(.*?)(.*?</u>)',
        unicode: true,
      ),
      onMatch: (final match) {
        final String? textPart = match.group(0);
        if (textPart == null) {
          return '';
        }
        if (RegExp(Strings.regExpStory).hasMatch(textPart)) {
          if (textPart.contains('@')) {
            if (widget.mentions.isEmpty) {
              textSpanChildren.add(
                TextSpan(
                  text: textPart,
                  style: const TextStyle(color: Colors.red),
                ),
              );
            } else {
              final MentionEntity mentionCurrent =
                  widget.mentions.firstWhereOrNull((final e) {
                    final name = e.label?.replaceAll(' ', '_');
                    return '@$name' == textPart;
                  }) ??
                  MentionEntity(
                    username: Strings.empty,
                    label: Strings.empty,
                    text: Strings.empty,
                  );
              if (mentionCurrent.label?.isEmpty ?? false) {
                textSpanChildren.add(
                  TextSpan(
                    text: textPart,
                    style: const TextStyle(color: Colors.red),
                  ),
                );
              } else {
                textSpanChildren.add(
                  TextSpan(
                    text: mentionCurrent.label,
                    style: widget.isTicket
                        ? TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          )
                        : const TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.blue,
                          ),
                  ),
                );
              }
            }
          }
          if (textPart.contains('#')) {
            textSpanChildren.add(
              TextSpan(
                text: textPart,
                style: const TextStyle(color: Colors.blue),
                recognizer: TapGestureRecognizer()
                  ..onTap = () async {
                    context.router.push(StorySearchRoute(text: textPart));
                  },
              ),
            );
          }
        }
        if (RegExp(
              '(<(strong)>.*?)(.*?)(.*?</strong>)',
              unicode: true,
            ).hasMatch(textPart) ||
            RegExp(
              '(<em>.*?)(.*?)(.*?</em>)',
              unicode: true,
            ).hasMatch(textPart) ||
            RegExp(
              '(<u>.*?)(.*?)(.*?</u>)',
              unicode: true,
            ).hasMatch(textPart)) {
          textSpanChildren.add(parseSimpleHtml(textPart));
          return '';
        }

        return '';
      },
      onNonMatch: (final match) {
        if (RegExp(Strings.regExpStory).hasMatch(match)) {
          textSpanChildren.add(
            TextSpan(
              text: match.replaceAll('@', '').replaceAll('_', ' '),
              style: const TextStyle(color: Colors.black),
            ),
          );
          return '';
        } else {
          textSpanChildren.add(
            TextSpan(
              text: match,
              style: const TextStyle(color: Colors.black),
            ),
          );
          return '';
        }
      },
    );
    super.initState();
  }

  TextSpan parseSimpleHtml(final String input, [TextStyle? baseStyle]) {
    baseStyle ??= const TextStyle();

    final List<TextSpan> stack = [];
    TextStyle currentStyle = baseStyle;
    final tagStack = <String>[];

    final tagRegex = RegExp(r'<(\/?)(\w+)>');
    int lastIndex = 0;

    final Iterable<RegExpMatch> matches = tagRegex.allMatches(input);

    for (final match in matches) {
      final textBefore = input.substring(lastIndex, match.start);
      if (textBefore.isNotEmpty) {
        stack.add(TextSpan(text: textBefore, style: currentStyle));
      }

      final isClosing = match.group(1) == '/';
      final tag = match.group(2);

      if (isClosing) {
        if (tagStack.isNotEmpty) {
          tagStack.removeLast();
        }

        currentStyle = baseStyle;
        for (final t in tagStack) {
          currentStyle = _applyStyle(currentStyle, t);
        }
      } else {
        tagStack.add(tag!);
        currentStyle = _applyStyle(currentStyle, tag);
      }

      lastIndex = match.end;
    }

    // Add remaining text
    if (lastIndex < input.length) {
      stack.add(
        TextSpan(text: input.substring(lastIndex), style: currentStyle),
      );
    }

    return TextSpan(children: stack);
  }

  TextStyle _applyStyle(final TextStyle style, final String tag) {
    switch (tag) {
      case 'strong':
      case 'b':
        return style.merge(const TextStyle(fontWeight: FontWeight.bold));
      case 'em':
      case 'i':
        return style.merge(const TextStyle(fontStyle: FontStyle.italic));
      case 'u':
        return style.merge(
          const TextStyle(decoration: TextDecoration.underline),
        );
      default:
        return style;
    }
  }

  @override
  Widget build(final BuildContext context) {
    return matches.length == 1
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.content
                  .replaceAll(matches.firstOrNull?.group(0) ?? '', '')
                  .isNotEmpty)
                ReadMoreText(
                  widget.content.replaceAll(
                    matches.firstOrNull?.group(0) ?? '',
                    '',
                  ),
                  isCollapsed: widget.isCollapsed,
                  trimMode: TrimMode.Line,
                  style: widget.style,
                  trimLines: 3,
                  trimCollapsedText: context.l10n.seeMore,
                  trimExpandedText: '',
                  annotations: [
                    Annotation(
                      regExp: RegExp(
                        r'<strong>\s*<em>(.*?)<\/em>\s*<\/strong>',
                        caseSensitive: false,
                        dotAll: true,
                      ),
                      spanBuilder:
                          ({
                            required final String text,
                            final TextStyle? textStyle,
                          }) {
                            return TextSpan(
                              text: text,
                              style: textStyle?.copyWith(color: Colors.blue),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {
                                  Utils.onTapDynamicLink(
                                    link: matches.firstOrNull?.group(0) ?? '',
                                    linkType: LinkType.phone,
                                  );
                                },
                            );
                          },
                    ),
                    Annotation(
                      regExp: RegExp(Strings.hotline),
                      spanBuilder:
                          ({
                            required final String text,
                            final TextStyle? textStyle,
                          }) => TextSpan(
                            text: text,
                            style: textStyle?.copyWith(color: Colors.blue),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () async {
                                Utils.onTapDynamicLink(
                                  link: matches.firstOrNull?.group(0) ?? '',
                                  linkType: LinkType.phone,
                                );
                              },
                          ),
                    ),
                    Annotation(
                      regExp: RegExp(Strings.regExpUserName),
                      spanBuilder:
                          ({
                            required final String text,
                            final TextStyle? textStyle,
                          }) {
                            final user =
                                widget.mentions.firstWhereOrNull(
                                  (final e) => e.text == text,
                                ) ??
                                MentionEntity(
                                  label: '',
                                  text: '',
                                  username: '',
                                );
                            return TextSpan(
                              text: widget.isTicket
                                  ? '${user.username} - ${user.label}'
                                  : user.label,
                              style: widget.isTicket
                                  ? textStyle?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context).primaryColor,
                                    )
                                  : textStyle?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.blue,
                                    ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {
                                  context.router.push(
                                    StoryPersonRoute(
                                      codeUser: text.replaceAll('@', ''),
                                    ),
                                  );
                                },
                            );
                          },
                    ),
                    Annotation(
                      regExp: RegExp(Strings.regExpHashTag),
                      spanBuilder:
                          ({
                            required final String text,
                            final TextStyle? textStyle,
                          }) => TextSpan(
                            text: text,
                            style: textStyle?.copyWith(color: Colors.blue),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () async {
                                context.router.push(
                                  StorySearchRoute(text: text),
                                );
                              },
                          ),
                    ),
                  ],
                  moreStyle: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
              GestureDetector(
                onTap: () async {
                  Utils.onTapDynamicLink(
                    link: matches.firstOrNull?.group(0) ?? '',
                    linkType: LinkType.url,
                  );
                },
                child: Card(
                  elevation: 8,
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: LinkPreview(
                      padding: EdgeInsets.zero,
                      enableAnimation: true,
                      textStyle: Theme.of(context).textTheme.bodyLarge
                          ?.copyWith(
                            fontSize: 16 * context.watch<FontsBloc>().textScale,
                          ),
                      previewBuilder: (final cxt, final data) {
                        return SizedBox(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 8),
                                child: Text(
                                  data.title ?? '',
                                  style: Theme.of(context).textTheme.titleSmall,
                                ),
                              ),
                              if (data.description != null)
                                Padding(
                                  padding: const EdgeInsets.only(left: 8),
                                  child: Text(data.description ?? ''),
                                ),
                              if (data.image?.url != null)
                                Padding(
                                  padding: const EdgeInsets.only(left: 12),
                                  child: Image.network(
                                    data.image?.url ?? '',
                                    width: data.image?.width,
                                    height: 140,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              // Text(data.description ?? ''),
                              if (data.link != null)
                                TextButton(
                                  onPressed: () {
                                    Utils.onTapDynamicLink(
                                      link: matches.firstOrNull?.group(0) ?? '',
                                      linkType: LinkType.url,
                                    );
                                  },
                                  child: Text(
                                    data.link ?? '',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                      onPreviewDataFetched: (final data) {
                        if (previewDataList[widget.content] == null) {
                          previewDataList[widget.content] = data;
                          setState(() {});
                        }
                      },
                      textWidget: GestureDetector(
                        onLongPress: () async {
                          showModalBottomSheet(
                            context: context,
                            builder: (final contextBottomSheet) => ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ListTile(
                                    title: Text(context.l10n.copyLink),
                                    onTap: () async {
                                      Navigator.of(context).pop();
                                      Clipboard.setData(
                                        ClipboardData(
                                          text:
                                              matches.firstOrNull?.group(0) ??
                                              '',
                                        ),
                                      ).whenComplete(() {
                                        if (context.mounted) {
                                          EzToast.showShortToast(
                                            message: context.l10n.copied,
                                          );
                                        }
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                        child: Text(
                          matches.firstOrNull?.group(0) ?? '',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w700,
                                decorationColor: Colors.blue,
                              ),
                        ),
                      ),
                      previewData: previewDataList[widget.content],
                      text: matches.firstOrNull?.group(0) ?? '',
                      width: MediaQuery.sizeOf(context).width,
                    ),
                  ),
                ),
              ),
            ],
          )
        : Text.rich(TextSpan(children: textSpanChildren));
  }
}
