// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:pdfrx/pdfrx.dart';
import 'package:timelines_plus/timelines_plus.dart';

// Project imports:
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/utils.dart';
import '../../../domain/entities/product_confirm.dart';
import '../../../services/nd_downloader/nd_downloader.dart';
import '../../story_list/widgets/story_image.dart';
import '../bloc/product_detail_confirm_bloc.dart';

const SPACING_COLUMN = 6.0;

class ProductDetailConfirmBody extends StatefulWidget {
  const ProductDetailConfirmBody({final Key? key}) : super(key: key);

  @override
  State<ProductDetailConfirmBody> createState() =>
      _ProductDetailConfirmBodyState();
}

class _ProductDetailConfirmBodyState extends State<ProductDetailConfirmBody> {
  ProductDetailConfirm? data;

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<ProductDetailConfirmBloc, ProductDetailConfirmState>(
      listener: (final context, final state) {
        if (state.status == ProductDetailConfirmStatus.success) {
          data = Utils.getData(state.data);
        }
        if (state.status == ProductDetailConfirmStatus.failure) {
          unawaited(
            ApiErrorDialog.show(
              ApiErrorParams(context, Utils.getData(state.data)),
            ),
          );
        }
      },
      builder: (final context, final state) {
        return state.status == ProductDetailConfirmStatus.success
            ? SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    spacing: 36,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfo(),
                      if (data?.content?.isNotEmpty ?? false) _buildContent(),
                      _buildPaid(),
                      if (data?.attachmentFiles?.isNotEmpty ?? false)
                        _buildAttachment(),
                      if (data?.suggests?.isNotEmpty ?? false) _buildSuggest(),
                      if (data?.histories?.isNotEmpty ?? false)
                        _buildProcessHistories(),
                    ],
                  ),
                ),
              )
            : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: _buildInfo(),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 12),
                      child: _buildPaid(),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: _buildAttachment(),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: _buildProcessHistories(),
                    ),
                    if (data?.suggests?.isNotEmpty ?? false)
                      Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: _buildSuggest(),
                      ),
                  ],
                ),
              );
      },
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: SPACING_COLUMN,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            context.l10n.content.toUpperCase(),
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
          ),
        ),
        SizedBox(
          width: double.infinity,
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  data?.content?.isEmpty ?? false
                      ? context.l10n.notContent
                      : data?.content ?? '',
                  style: data?.content?.isEmpty ?? false
                      ? Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.black54,
                        )
                      : const TextStyle(fontSize: 15),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfo() {
    return Column(
      spacing: SPACING_COLUMN,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                context.l10n.infoDocument.toUpperCase(),
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontSize: 16,
                ),
              ),
            ),
            if (data?.isUrgent ?? false)
              DecoratedBox(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xffFF3B30).withValues(alpha: .15),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  child: Text(
                    context.l10n.gap,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: const Color(0xffFF3B30),
                    ),
                  ),
                ),
              ),
          ],
        ),
        DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            //  spacing: 10,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoField(
                context.l10n.numberDocument,
                data?.transactionNo ?? '',
              ),
              _buildInfoField(
                context.l10n.dateDocument,
                data?.transactionDate ?? '',
              ),
              _buildInfoField(
                context.l10n.typeDocument,
                data?.contractTypeDeclareName ?? '',
              ),
              _buildInfoField(
                context.l10n.departmentRoom,
                data?.branchName ?? '',
              ),
              _buildInfoField(
                context.l10n.numberMoney,
                '${formatMoney(_onFormatMoney() ?? 0)} đ',
                true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  int? _onFormatMoney() => int.tryParse(data?.amount?.toStringAsFixed(0) ?? '');

  Widget _buildInfoField(
    final String title,
    final String value, [
    final bool isEndField = false,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.labelLarge?.copyWith(fontSize: 15),
                ),
                if (value.isNotEmpty)
                  Text(
                    value,
                    textAlign: TextAlign.start,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontSize: 15,
                      color: Colors.black45,
                    ),
                  )
                else
                  Text(
                    context.l10n.notUpdated,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: Colors.black54,
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (!isEndField) const Divider(thickness: .35),
        ],
      ),
    );
  }

  Widget _buildPaid() {
    const mainAlignColumnR = MainAxisAlignment.end;
    const mainAlignColumnL = MainAxisAlignment.start;
    // const mainAlignFRow = Alignment.center;
    const mainAlignRowL = Alignment.centerLeft;
    const mainAlignRowR = Alignment.centerRight;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: SPACING_COLUMN,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            context.l10n.reasonPaid.toUpperCase(),
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
          ),
        ),
        Scrollbar(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: ClipRRect(
              borderRadius: BorderRadiusGeometry.circular(8),
              child: DataTable(
                dividerThickness: .35,
                headingRowColor: WidgetStateColor.resolveWith((
                  final Set<WidgetState> states,
                ) {
                  return const Color(0xffffffff);
                }),
                columns: <DataColumn>[
                  DataColumn(
                    headingRowAlignment: mainAlignColumnL,
                    label: Text(context.l10n.titleReason),
                  ),
                  DataColumn(
                    headingRowAlignment: mainAlignColumnR,
                    label: Text(context.l10n.numberMoney),
                  ),
                  DataColumn(
                    headingRowAlignment: mainAlignColumnR,
                    label: Text(context.l10n.datePaid),
                  ),
                  DataColumn(
                    headingRowAlignment: mainAlignColumnR,
                    label: Text(context.l10n.notes),
                  ),
                ],
                rows: <DataRow>[
                  ...List.generate((data?.paymentDetails ?? []).length, (
                    final i,
                  ) {
                    final item = data?.paymentDetails?[i];
                    return DataRow(
                      color: WidgetStateColor.resolveWith((
                        final Set<WidgetState> states,
                      ) {
                        return i.isEven
                            ? const Color(0xfff7faeb)
                            : const Color(0xffffffff);
                      }),
                      cells: <DataCell>[
                        DataCell(
                          Align(
                            alignment: mainAlignRowL,
                            child: Text(item?.paymentReasonID ?? ''),
                          ),
                        ),
                        DataCell(
                          Align(
                            alignment: mainAlignRowR,
                            child: _buildPaymentAmount(item),
                          ),
                        ),
                        DataCell(
                          Align(
                            alignment: mainAlignRowR,
                            child: Text(item?.paymentDate ?? ''),
                          ),
                        ),
                        DataCell(
                          Align(
                            alignment: mainAlignRowR,
                            child: Text(item?.description ?? ''),
                          ),
                        ),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Text _buildPaymentAmount(final ProductDetailConfirmPaymentDetails? item) {
    return Text('${formatMoney(_onTryInt(item) ?? 0)} đ');
  }

  int? _onTryInt(final ProductDetailConfirmPaymentDetails? item) =>
      int.tryParse(item?.paymentAmount?.toStringAsFixed(0) ?? '');

  String formatMoney(final int money) =>
      NumberFormat('#,###', 'en_US').format(money);

  Widget _buildAttachment() {
    return Column(
      spacing: SPACING_COLUMN,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            context.l10n.attachedFile.toUpperCase(),
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
          ),
        ),
        DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              ...List.generate((data?.attachmentFiles ?? []).length, (final i) {
                final file = data?.attachmentFiles?[i];
                return Column(
                  children: [
                    ListTile(
                      leading: EZResources.image(
                        ImageParams(
                          name: file?.fileType == 'application/pdf'
                              ? AppIcons.icPdfExtension
                              : AppIcons.icFileGreen,
                          size: const ImageSize.square(36),
                        ),
                      ),
                      title: Text(file?.fileName ?? ''),
                      onTap: () {
                        if (file?.fileType == 'application/pdf' ||
                            (file?.fileType?.contains('image/') ?? false)) {
                          showModalBottomSheet(
                            isScrollControlled: true,
                            useSafeArea: true,
                            context: context,
                            builder: (final context) {
                              return Padding(
                                padding: EdgeInsets.only(
                                  top: MediaQuery.of(context).padding.top,
                                ),
                                child: Column(
                                  children: [
                                    _buildHeader(
                                      context,
                                      title: file?.fileName ?? '',
                                      actionWidget: const SizedBox(),
                                    ),
                                    if (file?.fileType == 'application/pdf')
                                      Expanded(
                                        child: PdfDocumentViewBuilder.uri(
                                          Uri.parse(file?.fileUrl ?? ''),
                                          builder: (final builder, final doc) {
                                            if (doc == null) {
                                              return const Center(
                                                child:
                                                    CircularProgressIndicator(),
                                              );
                                            }
                                            return ListView.builder(
                                              itemCount: doc.pages.length,
                                              itemBuilder:
                                                  (final context, final index) {
                                                    return SizedBox(
                                                      child: PdfPageView(
                                                        document: doc,
                                                        pageNumber: index + 1,
                                                      ),
                                                    );
                                                  },
                                            );
                                          },
                                        ),
                                      ),
                                    if (file?.fileType?.contains('image/') ??
                                        false)
                                      Expanded(
                                        child: ColoredBox(
                                          color: Colors.black87,
                                          child: Center(
                                            child: StoryImage(
                                              tags: file?.fileUrl ?? '',
                                              imageUrl: file?.fileUrl ?? '',
                                              fit: BoxFit.contain,
                                              originalWidth: 0,
                                              originalHeight: 0,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              );
                            },
                          );
                        } else {
                          ServiceDownloader.instance.onTapDownload(
                            context,
                            file?.fileUrl ?? '',
                            TaskInfo(name: file?.fileName),
                            file?.fileName,
                          );
                        }
                      },
                      trailing: DecoratedBox(
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(0xFFEBF6E5),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(6.0),
                          child: EZResources.image(
                            ImageParams(
                              name: AppIcons.icPreview,
                              color: Theme.of(context).primaryColor,
                              size: const ImageSize.square(24),
                            ),
                          ),
                        ),
                      ),
                    ),
                    if (i != (data?.attachmentFiles ?? []).length - 1)
                      const Divider(thickness: .35),
                  ],
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(
    final BuildContext context, {
    required final String title,
    required final Widget actionWidget,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
              },
              child: Text(context.l10n.close),
            ),
          ),
          Center(
            child: SizedBox(
              width: 200,
              child: title.length > 20
                  ? Row(
                      children: [
                        Expanded(
                          child: Text(
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.titleSmall,
                            maxLines: 1,
                            title.substring(0, title.lastIndexOf('.')),
                          ),
                        ),
                        Text(
                          title.substring(title.lastIndexOf('.')),
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ],
                    )
                  : Center(
                      child: Text(
                        overflow: TextOverflow.ellipsis,
                        title,
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                    ),
            ),
          ),
          Align(alignment: Alignment.centerRight, child: actionWidget),
        ],
      ),
    );
  }

  Widget _buildProcessHistories() {
    return Column(
      spacing: SPACING_COLUMN,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            context.l10n.processingHistory.toUpperCase(),
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
          ),
        ),
        DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: FixedTimeline.tileBuilder(
              mainAxisSize: MainAxisSize.min,
              theme: TimelineThemeData(
                indicatorTheme: const IndicatorThemeData(size: 32),
                indicatorPosition: 0,
                nodePosition: 0,
                connectorTheme: ConnectorThemeData(
                  color: Theme.of(context).dividerColor,
                  space: 1,
                ),
              ),
              builder: TimelineTileBuilder.connected(
                itemCount: data?.histories?.length ?? 0,
                connectorBuilder: (final context, final index, final type) =>
                    const VerticalDivider(thickness: 1),
                contentsBuilder: (final context, final index) {
                  final item = data?.histories?[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: Column(
                      spacing: 8,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          children: [
                            Text(
                              DateTime.parse(
                                item?.createdDate ?? '',
                              ).toLocal().formatDate5(),
                              style: Theme.of(context).textTheme.labelLarge
                                  ?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                  ),
                            ),
                          ],
                        ),
                        Text(
                          '${item?.createdBy}, ${item?.createdName}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          item?.content ?? '',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Theme.of(context).hintColor),
                        ),
                      ],
                    ),
                  );
                },
                lastConnectorBuilder: (final context) =>
                    const VerticalDivider(thickness: 1),
                indicatorBuilder: (final context, final i) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(
                        context,
                      ).primaryColor.withValues(alpha: .1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: DotIndicator(
                        color: Theme.of(context).primaryColor,
                        size: 8,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSuggest() {
    return Column(
      spacing: SPACING_COLUMN,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: Text(
            context.l10n.suggestList.toUpperCase(),
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
          ),
        ),
        for (final ProductDetailConfirmSugests item in data?.suggests ?? [])
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: _buildSuggestItem(item),
          ),
      ],
    );
  }

  Widget _buildSuggestItem(final ProductDetailConfirmSugests item) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          spacing: 6,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.transactionNo ?? '',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
            for (final ProductDetailConfirmSugestsItems device
                in item.suggestDetails ?? [])
              Column(
                spacing: 6,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        device.itemName ?? '',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '${device.quantity} ${device.unitName}',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).hintColor,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const Divider(),
                ],
              ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 24),
              child: Text(
                item.notes ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).hintColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
