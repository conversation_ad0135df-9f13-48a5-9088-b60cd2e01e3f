// Dart imports:
import 'dart:async';
import 'dart:convert';

// Flutter imports:
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:animations/animations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/services.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';

// Project imports:
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/params/create_combo_body.dart';
import '../../../core/utils/mappers.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../data/models/service_type_purchase_model.dart';
import '../../../domain/entities/consultation_customer_get_action.dart';
import '../../../domain/entities/service_type_purchase.dart';
import '../../../injector/injector.dart';
import '../../widgets/widgets.dart';
import '../bloc/service_type_purchase_bloc.dart';
import 'consultation_combo_sheet.dart';
import 'service_type_purchase_sheet.dart';

class ConsultationDealSheet extends StatefulWidget {
  const ConsultationDealSheet({super.key, required this.partnerId});
  final String partnerId;
  static List<ConsultationCustomerGetActionItem?> actionList = [];
  static Future<dynamic> show(
    final BuildContext context, {
    required final String partnerId,
    final Function(dynamic)? onSubmit,
  }) {
    return showModalBottomSheet(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (final context) => baseBottomLayout(
        ConsultationDealSheet(partnerId: partnerId),
        height: MediaQuery.sizeOf(context).height * 0.75,
      ),
    ).then((final val) {
      if (onSubmit != null) {
        onSubmit(val);
      }
    });
  }

  @override
  State<ConsultationDealSheet> createState() => _ConsultationDealSheetState();
}

class _ConsultationDealSheetState extends State<ConsultationDealSheet> {
  final ValueNotifier<ServiceTypePurchaseItems> servicePurchase = ValueNotifier(
    const ServiceTypePurchaseItems(servicePurchase: []),
  );
  @override
  Widget build(final BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: servicePurchase,
      builder: (final context, final vServicePurchase, final child) {
        return Column(
          spacing: 12,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 12),
              // padding: const EdgeInsets.all(12.0),
              child: Center(
                child: Text(
                  context.l10n.deal,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
            Divider(
              thickness: .35,
              color: Theme.of(context).primaryColor.withValues(alpha: .7),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  child: Column(
                    spacing: 16,
                    children: [
                      _buildFieldService(
                        context,
                        context.l10n.deal,
                        vServicePurchase,
                      ),

                      Row(
                        spacing: 12,
                        children: [
                          Expanded(
                            child: _buildServicePart(
                              isReadOnly: vServicePurchase.id == null,
                              context.l10n.titleTotal,
                              vServicePurchase,
                              vServicePurchase.id != null
                                  ? Row(
                                      spacing: 12,
                                      children: [
                                        DecoratedBox(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                            border: Border.all(
                                              width: .35,
                                              color: Theme.of(
                                                context,
                                              ).primaryColor,
                                            ),
                                          ),
                                          child: GestureDetector(
                                            onTap: () {
                                              if (vServicePurchase.volume ==
                                                  1) {
                                                return;
                                              }
                                              servicePurchase.value =
                                                  servicePurchase.value
                                                      .copyWith(
                                                        volume:
                                                            vServicePurchase
                                                                .volume! -
                                                            1,
                                                      );
                                            },
                                            child: const Icon(Icons.remove),
                                          ),
                                        ),
                                        Text(
                                          vServicePurchase.volume.toString(),
                                        ),
                                        DecoratedBox(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                            border: Border.all(
                                              width: .35,
                                              color: Theme.of(
                                                context,
                                              ).primaryColor,
                                            ),
                                          ),
                                          child: GestureDetector(
                                            onTap: () {
                                              servicePurchase.value =
                                                  servicePurchase.value
                                                      .copyWith(
                                                        volume:
                                                            vServicePurchase
                                                                .volume! +
                                                            1,
                                                      );
                                            },
                                            child: const Icon(Icons.add),
                                          ),
                                        ),
                                      ],
                                    )
                                  : const Text(''),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                // showModalBottomSheet(
                                //   backgroundColor: Colors.transparent,
                                //   isScrollControlled: true,
                                //   context: context,
                                //   builder: (final context)
                                //=> baseBottomLayout(
                                //     Column(
                                //       children: [
                                //         const Text('data'),
                                //         Expanded(
                                //           child: ListView.builder(
                                //             itemCount: ConsultationComboSheet
                                //                 .actionList
                                //                 .length,
                                //             itemBuilder:
                                //                 (final context,
                                //final index) {
                                //                   final item =
                                //                       ConsultationComboSheet
                                //                           .actionList[index];
                                //                   return ListTile(
                                //                     onTap: () {
                                //                       Navigator.of(
                                //                         context,
                                //                       ).pop(item
                                // ?.dataTypeName);
                                //                     },
                                //                     title: Text(
                                //                       item
                                //?.dataTypeName ?? '',
                                //                     ),
                                //                   );
                                //                 },
                                //           ),
                                //         ),
                                //       ],
                                //     ),
                                //     height:
                                //         MediaQuery.sizeOf(context).height *
                                //         0.75,
                                //   ),
                                // ).then((final val) {
                                //   if (val != null) {
                                //     servicePurchase.value = servicePurchase
                                //         .value
                                //         .copyWith(action: val);
                                //   }
                                // });
                              },
                              child: _buildServicePart(
                                isReadOnly: true,
                                context.l10n.action,
                                vServicePurchase,
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        vServicePurchase.action ?? '',
                                      ),
                                    ),
                                    // Icon(
                                    //   Icons.arrow_drop_down_outlined,
                                    //   color: Theme.of(context).hintColor,
                                    // ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        spacing: 12,
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                showModal(
                                  useRootNavigator: false,
                                  context: context,
                                  builder: (final builder) => GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      context.router.popForced();
                                    },
                                    child: _buildTextFieldNote(
                                      context.l10n.netFare,
                                      initialValue: vServicePurchase.price
                                          ?.toStringAsFixed(0),
                                    ),
                                  ),
                                ).then((final val) {
                                  if (val != null && val is String) {
                                    servicePurchase.value = servicePurchase
                                        .value
                                        .copyWith(
                                          price:
                                              double.tryParse(
                                                val.replaceAll('.', ''),
                                              ) ??
                                              1,
                                        );
                                  }
                                });
                              },
                              child: _buildServicePart(
                                isReadOnly: vServicePurchase.id == null,
                                context.l10n.netFare,
                                vServicePurchase,
                                Text(
                                  formatMoney(
                                    int.tryParse(
                                          vServicePurchase.price
                                                  ?.toStringAsFixed(0) ??
                                              '',
                                        ) ??
                                        0,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                showModal(
                                  useRootNavigator: false,
                                  context: context,
                                  builder: (final builder) => GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      context.router.popForced();
                                    },
                                    child: _buildTextFieldNote(
                                      context.l10n.discount,
                                    ),
                                  ),
                                ).then((final val) {
                                  if (val != null && val is String) {
                                    servicePurchase.value = servicePurchase
                                        .value
                                        .copyWith(
                                          discount:
                                              double.tryParse(
                                                val.replaceAll('.', ''),
                                              ) ??
                                              1,
                                        );
                                  }
                                });
                              },
                              child: _buildServicePart(
                                isReadOnly: vServicePurchase.id == null,
                                context.l10n.discount,
                                vServicePurchase,
                                Text(
                                  formatMoney(
                                    int.tryParse(
                                          vServicePurchase.discount
                                                  ?.toStringAsFixed(0) ??
                                              '',
                                        ) ??
                                        0,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      GestureDetector(
                        onTap: () {
                          showModal(
                            useRootNavigator: false,
                            context: context,
                            builder: (final builder) => GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus();
                                context.router.popForced();
                              },
                              child: _buildTextFieldContentNote(
                                context.l10n.content,
                              ),
                            ),
                          ).then((final val) {
                            if (val != null && val is String) {
                              servicePurchase.value = servicePurchase.value
                                  .copyWith(note: val);
                            }
                          });
                        },
                        child: _buildServicePart(
                          isReadOnly: vServicePurchase.id == null,
                          context.l10n.content,
                          vServicePurchase,
                          Text(vServicePurchase.note ?? ''),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            DecoratedBox(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Theme.of(context).dividerColor),
                ),
                color: Theme.of(context).colorScheme.surface,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  spacing: 12,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.l10n.finalCost,
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        Text(
                          _onTotalPriceFinal(
                            vServicePurchase,
                          ).toStringAsFixed(0),
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ],
                    ),
                    BlocProvider(
                      create: (final context) =>
                          getIt<ServiceTypePurchaseBloc>(),
                      child:
                          BlocConsumer<
                            ServiceTypePurchaseBloc,
                            ServiceTypePurchaseState
                          >(
                            listener: (final context, final state) {
                              if (state.status ==
                                  ServiceTypePurchaseStatus
                                      .createComboSuccess) {
                                unawaited(
                                  Alert.showAlertCollaborator(
                                    barrierDismissible: false,
                                    context,
                                    image: EZResources.image(
                                      ImageParams(
                                        name: AppIcons.icGreenSuccess,
                                        size: ImageSize.square(
                                          MediaQuery.sizeOf(context).width /
                                              2.8,
                                        ),
                                      ),
                                    ),
                                    message: context.l10n.updateSuccess,
                                  ),
                                );
                              }
                            },
                            builder: (final context, final state) {
                              return _buildButtonSave(
                                context,
                                state,
                                vServicePurchase,
                              );
                            },
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  BaseAppButton _buildButtonSave(
    final BuildContext context,
    final ServiceTypePurchaseState state,
    final ServiceTypePurchaseItems vServicePurchase,
  ) {
    return BaseAppButton(
      titleColor: Colors.white,
      title: context.l10n.save,
      enable: state.status != ServiceTypePurchaseStatus.loading,
      onPressed: () {
        context.read<ServiceTypePurchaseBloc>().add(
          CreateComboStarted(
            params: CreateComboBody(
              rowKey: 0,
              partnerID: widget.partnerId,
              itemID: vServicePurchase.id,
              volume: vServicePurchase.volume,
              unitPrice: vServicePurchase.price,
              transactionID: 'IB01',
              transactionDate: DateTime.now().toUtc().toLocal().formatDate2(),
              recordType: 'in',
              itemIDChildrens: servicePurchase.value.servicePurchase
                  ?.map((final e) => e.serviceCode ?? '')
                  .toList(),
              createdBy: EZCache.shared.getUserProfile()?.employeeId ?? '',
              totalAmount: _onTotalPriceFinal(vServicePurchase),
              discountAmount: vServicePurchase.discount,
              notes: vServicePurchase.note,
            ),
          ),
        );
      },
    );
  }

  AlertDialog _buildTextFieldContentNote(
    final String title, {

    final String? initialValue,
    final bool isReadOnly = false,
  }) {
    final TextEditingController controller = TextEditingController(
      text: initialValue ?? '',
    );
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(0)),
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 12),
      contentPadding: EdgeInsets.zero,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      content: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
          child: SizedBox(
            width: double.maxFinite,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(title, style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 12),
                TextField(
                  controller: controller,
                  maxLines: 5,
                  autofocus: true,
                  decoration: _decorationCommon(),
                  style: Theme.of(context).textTheme.bodyMedium,
                  readOnly: isReadOnly,
                ),
                const SizedBox(height: 16),
                const Divider(thickness: .35),
                IntrinsicHeight(
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: SizedBox(
                            child: Text(
                              context.l10n.cancel,
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(color: Colors.black54),
                            ),
                          ),
                        ),
                      ),
                      const VerticalDivider(thickness: .35),
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop(controller.text.trim());
                          },
                          child: SizedBox(child: Text(context.l10n.done)),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  double _onTotalPriceFinal(final ServiceTypePurchaseItems vServicePurchase) =>
      ((vServicePurchase.price ?? 0.0) *
          (vServicePurchase.volume?.toDouble() ?? 0)) -
      (vServicePurchase.discount ?? 0);

  AlertDialog _buildTextFieldNote(
    final String title, {

    final String? initialValue,
    final bool isReadOnly = false,
  }) {
    final TextEditingController controller = TextEditingController(
      text: initialValue ?? '',
    );
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(0)),
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 12),
      contentPadding: EdgeInsets.zero,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      content: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: controller,
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                autofocus: true,
                decoration: _decorationCommon(),
                style: Theme.of(context).textTheme.bodyMedium,
                readOnly: isReadOnly,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  CurrencyInputFormatter(
                    thousandSeparator: ThousandSeparator.Period,
                    mantissaLength: 0,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Divider(thickness: .35),
              IntrinsicHeight(
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: SizedBox(
                          child: Text(
                            context.l10n.cancel,
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(color: Colors.black54),
                          ),
                        ),
                      ),
                    ),
                    const VerticalDivider(thickness: .35),
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          Navigator.of(context).pop(controller.text.trim());
                        },
                        child: SizedBox(
                          child: Text(
                            context.l10n.done,
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(color: Colors.black54),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String formatMoney(final int money) =>
      NumberFormat('#,###', 'vi_VN').format(money);

  InputDecoration _decorationCommon() => const InputDecoration(
    enabledBorder: InputBorder.none,
    focusedBorder: InputBorder.none,
    border: InputBorder.none,
    disabledBorder: InputBorder.none,
    contentPadding: EdgeInsets.all(8),
    isDense: true,
  );
  Widget _buildServicePart(
    final String title,
    final ServiceTypePurchaseItems? vServicePurchase,
    final Widget body, {
    final bool isReadOnly = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).hintColor.withValues(alpha: .5),
            width: .35,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isReadOnly
              ? Colors.grey[300]
              : Theme.of(context).colorScheme.surface,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.maxFinite,
            child: Column(
              spacing: 8,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                body,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFieldService(
    final BuildContext context,
    final String title,
    final ServiceTypePurchaseItems? vServicePurchase,
  ) {
    return DecoratedBox(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).hintColor.withValues(alpha: .5),
          width: .35,
        ),
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.surface,
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            GestureDetector(
              onTap: () async {
                ServiceTypePurchaseSheet.show(
                  title: context.l10n.deal,
                  context,
                  typePurchase: ServiceTypePurchase.deal,
                  serviceSelected: [
                    ServiceSelected(
                      id: vServicePurchase?.id ?? '',
                      name: vServicePurchase?.name ?? '',
                    ),
                  ],
                  isDealService: true,
                ).then((final val) {
                  if (val != null) {
                    final DealPurchase data = getIt<Mapper>().convert(
                      DealPurchaseModel.fromJson(jsonDecode(val)),
                    );

                    servicePurchase.value =
                        vServicePurchase?.copyWith(
                          id: data.dealID,
                          name: data.dealName,
                          volume: 1,
                          action: 'Mua',
                        ) ??
                        const ServiceTypePurchaseItems(servicePurchase: []);
                    if (vServicePurchase?.servicePurchase?.isEmpty ?? false) {
                      if (context.mounted) {
                        ServiceTypePurchaseSheet.show(
                          title: context.l10n.addService,
                          context,
                          typePurchase: ServiceTypePurchase.service,
                          serviceSelected: [
                            ServiceSelected(
                              id: vServicePurchase?.id ?? '',
                              name: vServicePurchase?.name ?? '',
                            ),
                          ],
                          isDealService: true,
                        ).then((final val) {
                          if (val != null) {
                            final ServicePurchase data = getIt<Mapper>()
                                .convert(
                                  ServicePurchaseModel.fromJson(
                                    jsonDecode(val),
                                  ),
                                );
                            servicePurchase.value = servicePurchase.value
                                .copyWith(
                                  servicePurchase: [
                                    ...servicePurchase.value.servicePurchase ??
                                        [],
                                    ServicePurchase(
                                      serviceCode: data.serviceCode ?? '',
                                      serviceName: data.serviceName,
                                    ),
                                  ],
                                );
                          }
                        });
                      }
                    }
                  }
                });
              },
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Row(
                  children: [
                    Expanded(child: Text(vServicePurchase?.name ?? '')),
                    Icon(
                      Icons.arrow_drop_down_outlined,
                      color: Theme.of(context).hintColor,
                    ),
                  ],
                ),
              ),
            ),
            Column(
              children:
                  vServicePurchase?.servicePurchase
                      ?.mapIndexed(
                        (final i, final item) => _buildItemService(
                          i,
                          vServicePurchase,
                          item,
                          context,
                        ),
                      )
                      .toList() ??
                  [],
            ),
            if (vServicePurchase?.id != null)
              Padding(
                padding: const EdgeInsets.only(top: 12, bottom: 4),
                child: GestureDetector(
                  onTap: () {
                    ServiceTypePurchaseSheet.show(
                      title: context.l10n.addService,
                      context,
                      typePurchase: ServiceTypePurchase.service,
                      serviceSelected:
                          vServicePurchase?.servicePurchase
                              ?.map(
                                (final combo) => ServiceSelected(
                                  id: combo.serviceCode ?? '',
                                  name: combo.serviceName ?? '',
                                ),
                              )
                              .toList() ??
                          [],
                      isComboService: true,
                    ).then((final val) {
                      if (val != null) {
                        final ServicePurchase data = getIt<Mapper>().convert(
                          ServicePurchaseModel.fromJson(jsonDecode(val)),
                        );
                        servicePurchase.value = servicePurchase.value.copyWith(
                          servicePurchase: [
                            ...servicePurchase.value.servicePurchase ?? [],
                            ServicePurchase(
                              serviceCode: data.serviceCode ?? '',
                              serviceName: data.serviceName,
                              count: 1,
                            ),
                          ],
                        );
                      }
                    });
                  },
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Theme.of(context).primaryColor),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              context.l10n.addService,
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),

                          Row(
                            spacing: 4,
                            children: [
                              Text(context.l10n.select),
                              EZResources.image(
                                ImageParams(
                                  name: AppIcons.icAddGreen,
                                  size: const ImageSize.square(24),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Row _buildItemService(
    final int index,
    final ServiceTypePurchaseItems vServicePurchase,
    final ServicePurchase item,
    final BuildContext context,
  ) {
    return Row(
      spacing: 12,
      children: [
        InkWell(
          onTap: () {
            final combos = vServicePurchase.servicePurchase;
            combos?.remove(item);
            servicePurchase.value = servicePurchase.value.copyWith(
              servicePurchase: combos,
            );
          },
          child: EZResources.image(ImageParams(name: AppIcons.icRemoveService)),
        ),
        Expanded(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  children: [Expanded(child: Text(item.serviceName ?? ''))],
                ),
              ),
              if (index != (vServicePurchase.servicePurchase?.length ?? 0) - 1)
                Divider(thickness: .35, color: Theme.of(context).dividerColor),
            ],
          ),
        ),
      ],
    );
  }
}
