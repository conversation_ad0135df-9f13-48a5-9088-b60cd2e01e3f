// Dart imports:
import 'dart:async';
import 'dart:io';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

// Project imports:
import '../../../config/enums/erp_work_status.dart';
import '../../../core/enums/room_code.dart';
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/params/consultation_skin_ts_params.dart';
import '../../../core/params/get_consultation_ttbd_request_params.dart';
import '../../../core/params/request_params.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/consultation_service.dart';
import '../../../domain/entities/entities.dart';
import '../../../domain/entities/fit_customer_info.dart';
import '../../../domain/entities/get_consultation_ttbd.dart';
import '../../../domain/entities/result_of_fit_service_item.dart';
import '../../../domain/entities/skin_customer_info.dart';
import '../../../domain/usecases/taking_care_customer/upload_record_taking_care_customer_usecase.dart';
import '../../../injector/injector.dart';
import '../../customer_booking_info/customer_booking_info.dart';
import '../../taking_care_customer/taking_care_customer.dart';
import '../../widgets/widgets.dart';
import '../bloc/service_detail_bloc.dart';
import '../consultation_customer.dart';
import 'consultation_combo_sheet.dart';
import 'consultation_deal_sheet.dart';
import 'consultation_ndtv_init.dart';
import 'consultation_ndtv_tab.dart';
import 'consultation_ttbd_tab.dart';
import 'customer_info_body_skin.dart';

class ConsultationCustomerBody extends StatefulWidget {
  const ConsultationCustomerBody(
    this.customer, {
    final Key? key,
    this.service,
    this.isUnassigned,
  }) : super(key: key);
  static bool accessConsultationContentTab = true;
  static bool accessConsultationTab = true;
  final PxCustomer? customer;

  /// service push from customer booking info page
  final ConsultationService? service;
  final bool? isUnassigned;

  @override
  State<ConsultationCustomerBody> createState() =>
      _ConsultationCustomerBodyState();
}

class _ConsultationCustomerBodyState extends State<ConsultationCustomerBody>
    with TickerProviderStateMixin {
  List<ConsultationCustomerGetActionItem?> actionList = [];
  List<ConsultationCustomerGetServiceItem?> serviceList = [];
  List<ConsultationCustomerGetServiceUsageServiceDetail?> productList = [];
  List<ConsultationCustomerProductLoadItems?> erpProductList = [];
  ConsultationCustomer? customerData;

  final productTabKey = const GlobalObjectKey<ProductTabState>(
    Strings.productTabKey,
  );
  final consultationTabKey = const GlobalObjectKey<ConsultationTabState>(
    Strings.consultationTabKey,
  );
  var recordPath = '';
  late AudioRecorder record;
  bool sendFileToGroup = false;
  TakingCareCustomerCreateTreatmentDetailsRequestParams? tattooParams;
  late TabController tabController;
  ValueNotifierList<ConsultationService?> displayServiceList =
      ValueNotifierList([]);
  CustomerGetRoomList? roomList;
  ValueNotifier<FitCustomerInfoItems?> customerInfo = ValueNotifier(null);
  ValueNotifier<SkinCustomerInfoItems?> skinInfo = ValueNotifier(null);
  ValueNotifierList<ResultOfFitServiceItem> resultOfFitList = ValueNotifierList(
    [],
  );

  final ValueNotifier<int> indexTab = ValueNotifier(0);
  final List<ConsultationCustomerService> serviceL = [];
  final ValueNotifier<String> vTCField = ValueNotifier('');
  final ValueNotifier<String> vKTField = ValueNotifier('');
  final ValueNotifier<double> height = ValueNotifier(0);
  final ValueNotifier<bool> loadSkin = ValueNotifier(false);
  final ValueNotifier<String> dateSelected = ValueNotifier('');
  @override
  void initState() {
    super.initState();
    tabController = TabController(
      length: (ConsultationCustomerBody.accessConsultationTab ? 1 : 0) + 5,
      vsync: this,
    )..addListener(setIndexTab);

    if (widget.customer?.assignStatus == ErpWorkStatus.waiting.value) {
      context.read<TakingCareCustomerBloc>().add(
        TakingCareCustomerFinishedTask(
          TakingCareCustomerFinishTaskRequestParams(
            customerId: widget.customer?.customerId.toString(),
            customerName: widget.customer?.customerName,
            serviceId: '0',
            assignId: widget.customer?.assignId ?? '0',
            status: ErpWorkStatus.doing.value, // status = 2 is doing
            empId: EZCache.shared.getUserProfile()?.employeeId,
            roomCode: RoomCode.tuvan.name,
            dealType: '0',
            detailId: '0',
          ),
        ),
      );
    }
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      // ForegroundService().start();
      await _startRecording();
      if (widget.service != null) {
        tabController.animateTo(2);
      }
    });
  }

  void setIndexTab() {
    height.value = 0;
    indexTab.value = tabController.index;
    if (FocusScope.of(context).hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  Future<void> _startRecording() async {
    try {
      record = AudioRecorder();
      final tempDir = await getTemporaryDirectory();

      recordPath =
          '${tempDir.path}${Platform.pathSeparator}'
          '${widget.customer?.customerId}.m4a';

      await record.start(
        RecordConfig(
          bitRate: 64000,
          sampleRate: Platform.isAndroid ? 16000 : 44100,
          numChannels: 1,
        ),
        path: recordPath,
      );
    } catch (_) {
      if (mounted) {
        Alert.showAlert(AlertParams(context, context.l10n.unknown));
      }
    }
  }

  late String tVCLHtml;
  late String ndtvHtml;
  @override
  void dispose() {
    tabController.removeListener(setIndexTab);
    unawaited(_stopRecordAndSendApi(sendFileToGroup: sendFileToGroup));
    // ForegroundService().stop();
    super.dispose();
  }

  Future<void> _stopRecordAndSendApi({
    required final bool sendFileToGroup,
  }) async {
    final isRecording = await record.isRecording();
    if (isRecording) {
      await record.stop();
      await record.dispose();
      await callEventUploadRecord(sendFileToGroup: sendFileToGroup);
    }
  }

  GetConsultationTTBDParentGroup? data;
  GetConsultationTTBDParentGroup? ndtv;
  GetConsultationTTBDParentGroup? ttbd;
  ValueNotifierList<GetConsultationTTBDParent> ttbdCates = ValueNotifierList(
    [],
  );
  ValueNotifierList<GetConsultationTTBDParent> ndtvCates = ValueNotifierList(
    [],
  );
  final ValueNotifier<GetConsultationTTBDParent?> ndtvInfo = ValueNotifier(
    null,
  );
  final ValueNotifier<GetConsultationTTBDParent?> ndtvCustomer = ValueNotifier(
    null,
  );
  @override
  Widget build(final BuildContext context) {
    return BlocListener<ServiceDetailBloc, ServiceDetailState>(
      listener: (final context, final state) {
        if (state is ServiceDetailsCreateTreatmentDetailsSuccess) {
          if (state.isLast) {
            displayServiceList.value.clear();
            unawaited(
              Alert.showAlertConfirm(
                AlertConfirmParams(
                  context,
                  title: context.l10n.alert,
                  message: context.l10n.sendRecordQuestion,
                  confirmText: context.l10n.accept,
                  cancelButton: context.l10n.cancel,
                  onPressed: () async {
                    sendFileToGroup = true;
                    Navigator.of(context).pop();
                    Navigator.of(context).pop(widget.customer);
                  },
                  onPressedCancel: () async {
                    sendFileToGroup = false;
                    Navigator.of(context).pop(widget.customer);
                  },
                ),
              ),
            );
          }
        }
        if (state is ServiceDetailsFailure) {
          unawaited(
            ApiErrorDialog.show(ApiErrorParams(context, state.apiError)),
          );
        }
      },
      child: BlocConsumer<ConsultationCustomerBloc, ConsultationCustomerState>(
        listener: (final context, final state) {
          if (state is ConsultationNDTVLoadSuccess) {
            data = Utils.getData(state.consultationCustomer);
            if (data?.topicId == CategoryGroupType.NDTV.name) {
              ndtv = data;
              dateSelected.value = dateSelected.value.isEmpty
                  ? IntlHelper.dateFormatter8
                            .tryParse(ndtv?.transDates?.lastOrNull ?? '')
                            ?.formatDate2() ??
                        ''
                  : dateSelected.value;
              ndtvCates.setValue(ndtv?.categoryGroups);
              final Map<String, bool> mapInit = {};
              final topicInfo = TopicConsultationTTBDParent(
                transactionDate: ndtv?.transactionDate,
                employees: ndtv?.employees,
                managers: ndtv?.managers,
                incomeAmount: ndtv?.incomeAmount,
                surgeryAmount: ndtv?.surgeryAmount,
                debtAmount: ndtv?.debtAmount,
                doctorAmount: ndtv?.doctorAmount,
                pendingContent: ndtv?.pendingContent,
                content: ndtv?.content,
                companions: ndtv?.companions,
              );
              final cateInfo = categoryInfo(context, topicInfo);
              final cateCustomers = categoryCustomers(context, topicInfo);
              ndtvInfo.value = ndtv
                  ?.copyWith(categoryInfo: cateInfo)
                  .categoryInfo;
              ndtvCustomer.value = ndtv
                  ?.copyWith(categoryCustomers: cateCustomers)
                  .categoryCustomers;
              // thông tin
              mapInit.update(
                cateInfo.categoryGroupId ?? '-1',
                (final update) => false,
                ifAbsent: () => false,
              );
              setValueTextInit(cateInfo, ConsultationNDTVTab.categoryText);
              // người đi cùng
              mapInit.update(
                cateCustomers.categoryGroupId ?? '-1',
                (final update) => false,
                ifAbsent: () => false,
              );
              setValueTextInit(cateCustomers, ConsultationNDTVTab.categoryText);
              // khác
              ndtv?.categoryGroups?.forEach((final e) {
                setValueTextInit(e, ConsultationNDTVTab.categoryText);
                mapInit.update(
                  e.categoryGroupId ?? '-1',
                  (final update) => false,
                  ifAbsent: () => false,
                );
              });
              ConsultationNDTVTab.stateExpanded.value = mapInit;
            }
            if (data?.topicId == CategoryGroupType.TTBD.name) {
              ttbd = data;
              final Map<String, bool> mapInit = {};
              ttbdCates.setValue(ttbd?.categoryGroups);
              ttbd?.categoryGroups?.forEach((final e) {
                setValueTextInit(e, ConsultationTTBDTab.categoryText);
              });
              ConsultationTTBDTab.stateExpanded.value = mapInit;
            }
          }
          if (state is ConsultationUpdatedSuccess) {
            unawaited(
              Alert.showAlertCollaborator(
                barrierDismissible: false,
                context,
                image: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenSuccess,
                    size: ImageSize.square(
                      MediaQuery.sizeOf(context).width / 2.8,
                    ),
                  ),
                ),
                message: context.l10n.updateSuccess,
              ),
            );
            if (state.type == CategoryGroupType.NDTV) {
              context.read<ConsultationCustomerBloc>().add(
                ConsultationNDTVStarted(
                  GetConsultationTTBDRequestParams(
                    topicId: ndtv?.topicId ?? '',
                    partnerId: ndtv?.partnerId ?? '',
                    query: GetConsultationQueryRequestParams(
                      transDate: dateSelected.value,
                    ),
                  ),
                ),
              );
            }
            if (state.type == CategoryGroupType.TTBD) {
              context.read<ConsultationCustomerBloc>().add(
                ConsultationNDTVStarted(
                  GetConsultationTTBDRequestParams(
                    topicId: ttbd?.topicId ?? '',
                    partnerId: ttbd?.partnerId ?? '',
                    query: GetConsultationQueryRequestParams(),
                  ),
                ),
              );
            }
          }
          if (state is ConsultationCustomerLoadFailure) {
            unawaited(
              ApiErrorDialog.show(ApiErrorParams(context, state.apiError)),
            );
          }
          if (state is ConsultationCustomerGetActionSuccess) {
            actionList = state.consultationCustomerGetAction?.items ?? [];
            ConsultationComboSheet.actionList = actionList;
            ConsultationDealSheet.actionList = actionList;
          }
          if (state is ConsultationCustomerGetServiceSuccess) {
            serviceList = state.consultationCustomerGetService?.items ?? [];
          }
          if (state is ConsultationCustomerGetProductSuccess) {
            erpProductList = state.consultationCustomerProductLoad?.items ?? [];
            productList =
                state.consultationCustomerProductLoad?.items
                    .map(
                      (final e) =>
                          ConsultationCustomerGetServiceUsageServiceDetail(
                            serviceName: e?.productName,
                            serviceItemID: e?.productCode,
                            serviceItemName: e?.productName,
                            note: e?.note,
                            propertyInfos: [],
                          ),
                    )
                    .toList() ??
                [];
          }
          if (state is ConsultationCustomerRoomListSuccess) {
            roomList = state.consultationCustomerRoomList;
          }
          if (state is GetFitCustomerInfoEventSuccess) {
            customerInfo.value = state.data;
          }
          if (state is ConsultationCustomerInProgress) {
            loadSkin.value = false;
          }
          if (state is GetSkinCustomerInfoEventSuccess) {
            final SkinCustomerInfo data = Utils.getData(state.data);
            skinInfo.value = data.items.firstOrNull;
            CustomerInfoBodySkin.params = ConsultationSkinTSParams(
              khquantam: skinInfo.value?.khquantam,
              datungdtda: skinInfo.value?.datungdtda,
              benhlydacbiet: skinInfo.value?.benhlydacbiet,
              diung: skinInfo.value?.diung,
              spgaybongtroc: skinInfo.value?.spgaybongtroc,
              tgdentmv: skinInfo.value?.tgdentmv,
              tiepxucnang: skinInfo.value?.tiepxucnang,
              spdangsudung: skinInfo.value?.spdangsudung,
              dtgiamcan: skinInfo.value?.dtgiamcan,
              loaida: skinInfo.value?.loaida,
              doday: skinInfo.value?.doday,
              mauda: skinInfo.value?.mauda,
              klttda: skinInfo.value?.klttda,
              kldt: skinInfo.value?.kldt,
              klnn: skinInfo.value?.klnn,
              chuky: skinInfo.value?.chuky,
              nguoitao: skinInfo.value?.nguoitao,
            );
            loadSkin.value = true;
          }
          if (state is UpdateSkinCustomerInfoEventSuccess) {
            final SkinCustomerInfo data = Utils.getData(state.data);
            unawaited(
              Alert.showAlertCollaborator(
                context,
                image: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenSuccess,
                    size: ImageSize.square(
                      MediaQuery.sizeOf(context).width / 2.8,
                    ),
                  ),
                ),
                message: context.l10n.updateSuccess,
              ),
            );
            skinInfo.value = data.items.firstOrNull;
            CustomerInfoBodySkin.params = ConsultationSkinTSParams(
              khquantam: skinInfo.value?.khquantam,
              datungdtda: skinInfo.value?.datungdtda,
              benhlydacbiet: skinInfo.value?.benhlydacbiet,
              diung: skinInfo.value?.diung,
              spgaybongtroc: skinInfo.value?.spgaybongtroc,
              tgdentmv: skinInfo.value?.tgdentmv,
              tiepxucnang: skinInfo.value?.tiepxucnang,
              spdangsudung: skinInfo.value?.spdangsudung,
              dtgiamcan: skinInfo.value?.dtgiamcan,
              loaida: skinInfo.value?.loaida,
              doday: skinInfo.value?.doday,
              mauda: skinInfo.value?.mauda,
              klttda: skinInfo.value?.klttda,
              kldt: skinInfo.value?.kldt,
              klnn: skinInfo.value?.klnn,
              chuky: skinInfo.value?.chuky,
              nguoitao: skinInfo.value?.nguoitao,
            );
            loadSkin.value = true;
          }
          if (state is UpdateFitCustomerInfoEventSuccess) {
            unawaited(
              Alert.showAlertCollaborator(
                context,
                image: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenSuccess,
                    size: ImageSize.square(
                      MediaQuery.sizeOf(context).width / 2.8,
                    ),
                  ),
                ),
                message: context.l10n.updateSuccess,
              ),
            );
            customerInfo.value = state.data;
          }
          if (state is GetResultListOfFitEventSuccess) {
            resultOfFitList.setValue(
              state.data
                  ?.map(
                    (final e) => ResultOfFitServiceItem(
                      rowId: e.rowID,
                      customerId: e.customerID,
                      lsServiceId: e.lSServiceID,
                      resultNo: e.resultNo,
                      resultDate: e.resultDate,
                      resultStatus: e.resultStatus,
                      serviceName: e.serviceName,
                      treatmentArea: e.treatmentArea,
                      mhs: e.mhs,
                      mhsEmpID: e.mHSEmpID,
                      mhsByEmpName: e.mHSByEmpName,
                      weight: e.weight,
                      result: e.result,
                      detail: e.detail,
                      method: e.method,
                      loseWeight: e.loseWeight,
                    ),
                  )
                  .toList(),
            );
          }
          if (state is ConsultationCustomerLoadSuccess) {
            customerData = state.consultationCustomer;
            serviceL.addAll(
              (customerData ??
                      ConsultationCustomer(service: [], product: [], bld: []))
                  .service
                  .map((final e) => e ?? ConsultationCustomerService()),
            );
            displayServiceList.setValue(
              customerData?.service
                  .map(
                    (final e) => ConsultationService(
                      id: e?.iD,
                      action: e?.action,
                      netPrice: e?.netPrice,
                      serviceID: e?.serviceId,
                      serviceName: e?.serviceName,
                      departmentCode: e?.departmentCode,
                      quantity: e?.quantity,
                      discount: e?.discount,
                      total: e?.total,
                      notes: e?.notes,
                      dealDate: e?.dealDate,
                      serviceCode: e?.serviceCode,
                    ),
                  )
                  .toList(),
            );
          }

          if (state is ConsultationCustomerCompleteSuccess) {
            if (displayServiceList.value.isEmpty) {
              unawaited(
                Alert.showAlertConfirm(
                  AlertConfirmParams(
                    context,
                    title: context.l10n.alert,
                    message: context.l10n.sendRecordQuestion,
                    confirmText: context.l10n.accept,
                    cancelButton: context.l10n.cancel,
                    onPressed: () async {
                      sendFileToGroup = true;
                      Navigator.of(context).pop();
                      Navigator.of(context).pop(widget.customer);
                    },
                    onPressedCancel: () async {
                      sendFileToGroup = false;
                      Navigator.of(context).pop(widget.customer);
                    },
                  ),
                ),
              );
            } else {
              final createListNull = displayServiceList.value
                  .map(
                    (final e) => ConsultationService(
                      id: e?.id,
                      serviceID: e?.serviceID,
                      action: e?.action,
                      quantity: e?.quantity,
                      discount: e?.discount,
                      netPrice: e?.netPrice,
                      total: e?.total,
                      notes: e?.notes,
                      omPart: e?.omPart,
                    ),
                  )
                  .toList();
              final listCheck = createListNull
                  .where((final e) => e.id == 0)
                  .toList();
              if (listCheck.isEmpty) {
                unawaited(
                  Alert.showAlertConfirm(
                    AlertConfirmParams(
                      context,
                      title: context.l10n.alert,
                      message: context.l10n.sendRecordQuestion,
                      confirmText: context.l10n.accept,
                      cancelButton: context.l10n.cancel,
                      onPressed: () async {
                        sendFileToGroup = true;
                        Navigator.of(context).pop();
                        Navigator.of(context).pop(widget.customer);
                      },
                      onPressedCancel: () async {
                        sendFileToGroup = false;
                        Navigator.of(context).pop(widget.customer);
                      },
                    ),
                  ),
                );
              } else {
                bool hasUpdate = false;

                displayServiceList.value.forEachIndexed((
                  final i,
                  final element,
                ) {
                  ConsultationTreatmentDetailsRequestParamsV2? params;
                  params = element?.treatmentDetail;
                  if (params?.serviceDetail?.isNotEmpty ?? false) {
                    hasUpdate = true;
                    if (params?.serviceInfo?.partnerId == null) {
                      params = params?.copyWith(
                        serviceInfo: ServiceInfo(
                          partnerId: customerData?.customerCode,
                        ),
                      );
                    }
                    _requestUpdateTattooService(
                      index: i,
                      service: element,
                      treatmentParam: params!,
                    );
                  }
                });
                if (!hasUpdate) {
                  unawaited(
                    Alert.showAlertConfirm(
                      AlertConfirmParams(
                        context,
                        title: context.l10n.alert,
                        message: context.l10n.sendRecordQuestion,
                        confirmText: context.l10n.accept,
                        cancelButton: context.l10n.cancel,
                        onPressed: () async {
                          sendFileToGroup = true;
                          Navigator.of(context).pop();
                          Navigator.of(context).pop(widget.customer);
                        },
                        onPressedCancel: () async {
                          sendFileToGroup = false;
                          Navigator.of(context).pop(widget.customer);
                        },
                      ),
                    ),
                  );
                }
              }
            }
          }
        },
        builder: (final context, final state) {
          return Stack(
            children: [
              GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  height.value = 0;
                  // consultationController.unFocus();
                  // technicalController.unFocus();
                },
                behavior: HitTestBehavior.translucent,
                child: CustomerBookingInfoScaffold(
                  headerHeight: 250,
                  childBackgroundColor: Theme.of(context).cardColor,
                  headerWidget: ConsultationCustomerHeader(
                    customer: widget.customer,
                  ),
                  child: DefaultTabController(
                    length: 5,
                    child: Column(
                      children: <Widget>[
                        _buildTabBar(context),
                        _buildTabBarView(customerData),
                        if (ConsultationCustomerBody.accessConsultationTab)
                          ValueListenableBuilder(
                            valueListenable: indexTab,
                            builder:
                                (final context, final vIndexTab, final child) {
                                  return vIndexTab == 1 ||
                                          vIndexTab == 2 ||
                                          vIndexTab == 3
                                      ? const SizedBox()
                                      : _buildCompleteButton(context);
                                },
                          )
                        else
                          ValueListenableBuilder(
                            valueListenable: indexTab,
                            builder:
                                (final context, final vIndexTab, final child) {
                                  return vIndexTab == 1 || vIndexTab == 2
                                      ? const SizedBox()
                                      : _buildCompleteButton(context);
                                },
                          ),
                        ValueListenableBuilder(
                          valueListenable: height,
                          builder: (final context, final vHeight, final child) {
                            return SizedBox(height: vHeight);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (state is ConsultationCustomerInProgress)
                const LoadingWidget(),
            ],
          );
        },
      ),
    );
  }

  void setValueTextInit(
    final GetConsultationTTBDParent e,
    final Map<String, String> categoryText,
  ) {
    return e.categorys?.forEach((final e2) {
      e2.categoryDetails
          ?.where(
            (final e3) =>
                e3.fieldType == FieldType.TEXT.name ||
                e3.fieldType == FieldType.NUMBER.name ||
                e3.fieldType == FieldType.CHECKBOX.name ||
                e3.fieldType == FieldType.TEXTAREA.name,
          )
          .forEach((final e4) {
            if (e4.children?.isNotEmpty ?? false) {
              e4.children
                  ?.where(
                    (final eW) =>
                        eW.fieldType == FieldType.TEXT.name ||
                        eW.fieldType == FieldType.TEXTAREA.name,
                  )
                  .forEach((final e5) {
                    categoryText.update(
                      e5.categoryDetailCode ?? '-1',
                      (final update) => e5.valueText ?? '',
                      ifAbsent: () => e5.valueText ?? '',
                    );
                  });
            }
            if (e4.fieldType == FieldType.TEXT.name ||
                e4.fieldType == FieldType.TEXTAREA.name) {
              categoryText.update(
                e4.categoryDetailCode ?? '-1',
                (final update) => e4.valueText ?? '',
                ifAbsent: () => e4.valueText ?? '',
              );
            }
            if (e4.fieldType == FieldType.NUMBER.name) {
              categoryText.update(
                e4.categoryDetailCode ?? '-1',
                (final update) => e4.valueText ?? '',
                ifAbsent: () => e4.valueText ?? '',
              );
            }
          });
    });
  }

  bool checkShowButton(final int vIndexTab) {
    if (ConsultationCustomerBody.accessConsultationTab &&
        (vIndexTab == 1 || vIndexTab == 2)) {
      return false;
    } else {
      if (vIndexTab > 2) {
        return true;
      } else {
        return false;
      }
    }
  }

  Padding _buildCompleteButton(final BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: CustomSizeButton(
        content: Text(
          context.l10n.complete,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w700,
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
        onTap: () async {
          List<ConsultationService>? filtedList;
          List<ServiceRequestParam>? serviceParamList;
          tVCLHtml = vKTField.value;
          ndtvHtml = vTCField.value;
          if (context.mounted) {
            if (widget.isUnassigned ?? false) {
              final createListNull = displayServiceList.value
                  .map(
                    (final e) => ConsultationService(
                      id: e?.id,
                      serviceID: e?.serviceID,
                      action: e?.action,
                      quantity: e?.quantity,
                      discount: e?.discount,
                      netPrice: e?.netPrice,
                      total: e?.total,
                      notes: e?.notes,
                      omPart: e?.omPart,
                    ),
                  )
                  .toList();
              filtedList = createListNull
                  .where((final e) => e.id == 0)
                  .toList();

              serviceParamList = filtedList
                  .map(
                    (final e) => ServiceRequestParam(
                      serviceID: e.serviceID,
                      action: e.action,
                      quantity: e.quantity,
                      discount: e.discount,
                      netPrice: e.netPrice,
                      total: e.total,
                      notes: e.notes,
                    ),
                  )
                  .toList();
            } else {
              serviceParamList = displayServiceList.value
                  .map(
                    (final e) => ServiceRequestParam(
                      serviceID: e?.serviceID,
                      action: e?.action,
                      quantity: e?.quantity,
                      discount: e?.discount,
                      netPrice: e?.netPrice,
                      total: e?.total,
                      notes: e?.notes,
                    ),
                  )
                  .toList();
            }

            context.read<ConsultationCustomerBloc>().add(
              ConsultationCustomerCompleted(
                ConsultationCustomerCompleteRequestParams(
                  noteID: customerData?.noteID ?? 0,
                  customerID: customerData?.customerID,
                  lsLevel1ID: customerData?.lSLevel1ID.toString(),
                  ndtv: ndtvHtml.isNotEmpty
                      ? _formatHtmlContent(ndtvHtml)
                      : customerData?.nDTVHtml,
                  tvcl: tVCLHtml.isNotEmpty
                      ? _formatHtmlContent(tVCLHtml)
                      : customerData?.tVCLHtml,
                  noteMoney: int.tryParse(
                    Utils.defaultOnEmpty(
                      consultationTabKey.currentState?.tipController.text
                          .replaceAll(' ', ''),
                    ),
                  ),
                  advisoryBy: consultationTabKey
                      .currentState
                      ?.staffController
                      .text
                      .trim(),
                  isNoti: consultationTabKey.currentState?.isCheckNotification,
                  service: serviceParamList,
                  product:
                      productTabKey.currentState?.selectedProductList ?? [],
                  bld:
                      consultationTabKey.currentState?.selectedBldList
                          .map(
                            (final e) =>
                                BLDRequestParams(empcode: e?.employeeId),
                          )
                          .toList() ??
                      [],
                ),
              ),
            );
          }
        },
        width: double.maxFinite,
        height: 48,
      ),
    );
  }

  String _formatHtmlContent(final String html) {
    return html;
  }

  Expanded _buildTabBarView(final ConsultationCustomer? consultationCustomer) {
    return Expanded(
      child: TabBarView(
        controller: tabController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          ConsultationTab(
            key: consultationTabKey,
            //   htmlController: consultationController,
            htmlContent: consultationCustomer?.tVCLHtml,
            // controller: technicalController,
            vTCField: vTCField,
            vKTField: vKTField,
            vHeight: height,
          ),
          if (ConsultationCustomerBody.accessConsultationTab)
            ConsultationNDTVTab(
              customerId: widget.customer?.customerId ?? '',
              topic: ndtv?.topicId,
              partId: ndtv?.partnerId,
              dates: ndtv?.transDates,
              info: ndtvInfo,
              ndtv: ndtvCates,
              customers: ndtvCustomer,
              dateSelected: dateSelected,
            ),
          ConsultationTTBDTab(
            ttbd: ttbdCates,
            topic: ttbd?.topicId,
            partId: ttbd?.partnerId,
          ),
          CustomerInfoTab(
            loadSkin: loadSkin,
            customerId: widget.customer?.customerId,
            roomList: roomList,
            roomCode: RoomCode.OM.name,
            vCustomerInfo: customerInfo,
            vSkinInfo: skinInfo,
            serviceList: serviceL,
            resultList: resultOfFitList,
            isUnassigned: widget.isUnassigned,
          ),
          ServiceTab(
            actionList: actionList,
            serviceList: serviceList,
            productList: productList,
            service: widget.service,
            consumerId: widget.customer?.customerId,
            customerCode: widget.customer?.customerCode,
            fullCustomerCode: customerData?.customerCode,
            isUnassigned: widget.isUnassigned,
            displayServiceList: displayServiceList,
          ),
          ProductTab(
            key: productTabKey,
            productList: erpProductList,
            usedProductList: consultationCustomer?.product ?? [],
            actionList: actionList,
            customerId: widget.customer?.customerId,
          ),
        ],
      ),
    );
  }

  Container _buildTabBar(final BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
        color: Theme.of(context).primaryColor,
      ),
      height: 44,
      child: Stack(
        children: [
          TabBar(
            tabAlignment: tabController.length > 4 ? TabAlignment.start : null,
            isScrollable: tabController.length > 4,
            controller: tabController,
            padding: const EdgeInsets.only(top: 6),
            labelPadding: tabController.length > 4
                ? null
                : EdgeInsets.only(
                    top: MediaQuery.sizeOf(context).height * 4 / 812,
                  ),
            labelStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
            unselectedLabelStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
            unselectedLabelColor: Theme.of(
              context,
            ).colorScheme.surface.withValues(alpha: 80),
            labelColor: Theme.of(context).primaryColor,
            indicator: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            tabs: [
              _buildTabBarItem(context.l10n.titleNDTV),
              if (ConsultationCustomerBody.accessConsultationTab)
                _buildTabBarItem('${context.l10n.titleNDTV} ( New )'),
              _buildTabBarItem(context.l10n.originStatus),
              _buildTabBarItem(context.l10n.titleTabTTKH),
              _buildTabBarItem(context.l10n.service),
              _buildTabBarItem(context.l10n.product),
            ],
          ),
        ],
      ),
    );
  }

  Tab _buildTabBarItem(final String text) {
    return Tab(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        child: Text(text, maxLines: 1, style: const TextStyle(fontSize: 14)),
      ),
    );
  }

  Future<void> callEventUploadRecord({
    required final bool sendFileToGroup,
  }) async {
    final user = EZCache.shared.getUserProfile();
    await getIt<UploadRecordTakingCareCustomerUseCase>().call(
      params: TakingCareCustomerUploadRecordRequestParams(
        services: displayServiceList.value
            .map((final e) => e?.serviceID.toString())
            .toList(),
        products: productTabKey.currentState?.selectedProductList
            .map((final e) => e?.productID.toString())
            .toList(),
        file: MultipartFile.fromFileSync(Utils.defaultOnEmpty(recordPath)),
        customerId: Utils.defaultOnEmpty(customerData?.customerID.toString()),
        roomCode: RoomCode.tuvan.name,
        branchId: user?.branchId,
        customerCode: customerData?.customerCode,
        customerName: widget.customer?.customerName,
        sendFileToGroup: sendFileToGroup,
        employeeId: user?.employeeId,
        employeeName: user?.name,
        customerBranchId: widget.customer?.lsLevel1ID,
        jobTitleId: user?.jobTitleId,
        jobTitleName: user?.jobTitleName,
        departmentId: user?.departmentId,
        departmentName: user?.departmentName,
        org: user?.organizationId,
        serviceId: widget.service?.serviceID?.toString(),
        serviceCode: widget.service?.serviceCode,
      ),
    );
  }

  void _requestUpdateTattooService({
    required final int index,
    final ConsultationService? service,
    required final ConsultationTreatmentDetailsRequestParamsV2 treatmentParam,
  }) {
    if (widget.isUnassigned ?? false) {
      context.read<ServiceDetailBloc>().add(
        ServiceDetailCreatedTreatmentDetailsV2(
          treatmentParam,
          isLast: index == ((displayServiceList.value.length) - 1),
        ),
      );

      // if (index == ((displayServiceList.value.length) - 1)) {
      //   unawaited(
      //     Alert.showAlertConfirm(
      //       AlertConfirmParams(
      //         context,
      //         title: context.l10n.alert,
      //         message: context.l10n.sendRecordQuestion,
      //         confirmText: context.l10n.accept,
      //         cancelButton: context.l10n.cancel,
      //         onPressed: () async {
      //           sendFileToGroup = true;
      //           Navigator.of(context).pop();
      //           Navigator.of(context).pop(widget.customer);
      //         },
      //         onPressedCancel: () async {
      //           sendFileToGroup = false;
      //           Navigator.of(context).pop(widget.customer);
      //         },
      //       ),
      //     ),
      //   );
      // }
    } else {
      if (treatmentParam.serviceInfo?.usageId == null) {
        context.read<ServiceDetailBloc>().add(
          ServiceDetailCreatedTreatmentDetailsV2(
            treatmentParam,
            isLast: index == ((displayServiceList.value.length) - 1),
          ),
        );
      } else {
        context.read<ServiceDetailBloc>().add(
          ServiceDetailUpdatedTreatmentDetailsV2(
            treatmentParam,
            isLast: index == ((displayServiceList.value.length) - 1),
          ),
        );
      }
      // if (index == (displayServiceList.value.length - 1)) {
      //   unawaited(
      //     Alert.showAlertConfirm(
      //       AlertConfirmParams(
      //         context,
      //         title: context.l10n.alert,
      //         message: context.l10n.sendRecordQuestion,
      //         confirmText: context.l10n.accept,
      //         cancelButton: context.l10n.cancel,
      //         onPressed: () async {
      //           sendFileToGroup = true;
      //           Navigator.of(context).pop();
      //           Navigator.of(context).pop(widget.customer);
      //         },
      //         onPressedCancel: () async {
      //           sendFileToGroup = false;
      //           Navigator.of(context).pop(widget.customer);
      //         },
      //       ),
      //     ),
      //   );
      // }
    }
  }
}
