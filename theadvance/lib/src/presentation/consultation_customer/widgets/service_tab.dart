// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/enums/room_code.dart';
import '../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../core/params/request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/consultation_service.dart';
import '../../../domain/entities/entities.dart';
import '../../../injector/injector.dart';
import '../../widgets/widgets.dart';
import '../bloc/bloc.dart';
import '../bloc/service_detail_bloc.dart';
import 'consultation_combo_sheet.dart';
import 'consultation_deal_sheet.dart';
import 'service_type_purchase_detail_sheet.dart';
import 'service_type_purchase_sheet.dart';
import 'treatment_note_service.dart';
import 'treatment_om_service.dart';
import 'widgets.dart';

class ServiceTab extends StatefulWidget {
  const ServiceTab({
    super.key,
    required this.actionList,
    required this.serviceList,
    required this.productList,
    this.service,
    this.consumerId,
    this.customerCode,
    required this.fullCustomerCode,
    this.isUnassigned,
    required this.displayServiceList,
  });
  final bool? isUnassigned;
  final String? consumerId;
  final String? customerCode;
  final String? fullCustomerCode;
  final List<ConsultationCustomerGetActionItem?> actionList;
  final List<ConsultationCustomerGetServiceItem?> serviceList;
  final List<ConsultationCustomerGetServiceUsageServiceDetail?> productList;
  final ValueNotifierList<ConsultationService?> displayServiceList;

  /// service push from customer booking info page
  final ConsultationService? service;

  @override
  State<ServiceTab> createState() => ServiceTabState();
}

class ServiceTabState extends State<ServiceTab>
    with AutomaticKeepAliveClientMixin<ServiceTab> {
  TextEditingController serviceController = TextEditingController();
  TextEditingController actionController = TextEditingController();
  TextEditingController quantityController = TextEditingController();
  TextEditingController netFareController = TextEditingController();
  TextEditingController discountController = TextEditingController();
  TextEditingController noteController = TextEditingController();
  TextEditingController totalPriceController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  String? selectingValue;
  Timer? timer;
  ConsultationCustomerGetServiceItem? selectedService;
  ConsultationCustomerGetActionItem? selectedAction;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      if (widget.service != null) {
        _showTreatmentDetail(context, widget.service, isCreateNew: true);
      }
    });
  }

  @override
  Widget build(final BuildContext context) {
    super.build(context);

    return BlocListener<ConsultationCustomerBloc, ConsultationCustomerState>(
      listener: (final context, final state) {
        if (state is RemoveServiceSuccess) {
          widget.displayServiceList.value.removeWhere(
            (final element) => element?.id == state.id,
          );
        }
      },
      child: BlocBuilder<ConsultationCustomerBloc, ConsultationCustomerState>(
        buildWhen: (final previous, final current) =>
            current is ConsultationCustomerGetActionSuccess ||
            current is ConsultationCustomerGetServiceSuccess,
        builder: (final context, final state) {
          return Column(
            children: <Widget>[
              Expanded(
                child: Scaffold(
                  body: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemBuilder: (final _, final index) => ServiceTile(
                      widget.displayServiceList.getItem(index),
                      widget.displayServiceList,
                      index,
                      widget.productList,
                      onTapTreatMent: () async => _showTreatmentDetail(
                        context,
                        widget.displayServiceList.getItem(index),
                      ),
                      onTapProfile: () async => _showProfileDetail(
                        context,
                        widget.displayServiceList.getItem(index),
                      ),
                      onTapNDTV: () async => _showNDTV(
                        context,
                        widget.displayServiceList.getItem(index),
                      ),
                      onTapRemove: (final service) => _onTapRemove(service),
                      onTapEdit: (final service) async => _onTapEdit(service),
                    ),
                    separatorBuilder: (final context, final index) =>
                        const SizedBox.square(dimension: 16),
                    itemCount: widget.displayServiceList.value.length,
                  ),
                  backgroundColor: Theme.of(context).cardColor,
                  floatingActionButton: GestureDetector(
                    onTap: () async =>
                        // TODO(lthung): service type purchase
                        // ElevatedButton(onPressed: (){
                        //   ServiceTypePurchaseSheet.show(context,
                        //   typePurchase: ServiceTypePurchase.combo,
                        //    onSubmit: (final val) {
                        //     print(val);
                        //   });
                        // },
                        //  child: Text('xong'))
                        // TODO(lthung): service type purchase
                        // ConsultationComboSheet.show(
                        //   partnerId: widget.fullCustomerCode ?? '',
                        //   context,
                        //   onSubmit: (final val) {},
                        // ),
                        // TODO(lthung): service type purchase detail
                        ServiceTypePurchaseDetailSheet.show(
                          title: '',
                          partnerId: '0090000208830',
                         //  widget.fullCustomerCode ?? '',
                          itemId: 'DV002',
                          context,
                          typePurchase: ServiceTypePurchase.combo,
                          onSubmit: (final val) {},
                        ),

                    //  _onTapFloatingButton(context),
                    child: EZResources.image(
                      ImageParams(
                        name: AppIcons.icCart,
                        size: const ImageSize.square(55),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _showProfileDetail(
    final BuildContext context,
    final ConsultationService? service, {
    final bool isCreateNew = false,
  }) async {
    return ConsultatonOMServiceSheet.show(
      context,
      service: service,
      productList: widget.productList,
      isUnassigned: widget.isUnassigned,
      isCreateNew: isCreateNew,
      customerId: widget.consumerId,
      customerCode: widget.customerCode,
      fullCustomerCode: widget.fullCustomerCode,
      serviceList: widget.displayServiceList,
      onSubmit: (final updatedService) {},
    );
  }

  Future<void> _showTreatmentDetail(
    final BuildContext context,
    final ConsultationService? service, {
    final bool isCreateNew = false,
  }) async {
    return showModalBottomSheet(
      backgroundColor: Theme.of(context).primaryColorDark.withValues(alpha: 0),
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      builder: (final context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: BlocProvider(
            create: _createTreatMentProvider(
              widget.consumerId,
              service,
              widget.isUnassigned,
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              child: GestureDetector(
                onTap: FocusScope.of(context).unfocus,
                child: SizedBox(
                  height: MediaQuery.sizeOf(context).height * .8,
                  width: double.infinity,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          height: 5,
                          width: 30,
                        ),
                      ),
                      Expanded(
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          child: _buildTreatMentBody(
                            widget.consumerId,
                            service,
                            isCreateNew: isCreateNew,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    ).whenComplete(() {
      if (isCreateNew) {
        if (context.mounted) {
          context.router.popUntilRouteWithName(CustomerBookingInfoRoute.name);
        }
      }
    });
  }

  ServiceDetailBloc Function(BuildContext) _createTreatMentProvider(
    final String? consumerId,
    final ConsultationService? service,
    final bool? isUnassigned,
  ) {
    final departmentCode = service?.departmentCode;
    if (departmentCode == RoomCode.OM.name) {
      return TreatmentOMService.createProvider(
        customerId: consumerId,
        service: service,
        isUnassigned: isUnassigned,
      );
    }
    return (final context) => getIt<ServiceDetailBloc>();
  }

  Widget _buildTreatMentBody(
    final String? consumerId,
    final ConsultationService? service, {
    final bool isCreateNew = false,
  }) {
    final departmentCode = service?.departmentCode;
    if (departmentCode == RoomCode.OM.name) {
      return TreatmentOMService(
        serviceList: widget.displayServiceList,
        title: service?.serviceName ?? '',
        service: service,
        isUnassigned: widget.isUnassigned,
        isCreateNew: isCreateNew,
        customerId: widget.consumerId,
      );
    }
    return const SizedBox();
  }

  void _onTapEdit(final ConsultationService? service) {
    _onTapFloatingButton(context, editedService: service).whenComplete(() {
      _clearAllTextController();
    });
  }

  void _onTapRemove(final ConsultationService? service) {
    Alert.showAlertConfirm(
      AlertConfirmParams(
        context,
        confirmText: context.l10n.delete,
        cancelButton: context.l10n.cancel,
        message: context.l10n.deleteServiceQuestion,
        onPressed: () {
          if (service?.id != null && service?.id != 0) {
            context.read<ConsultationCustomerBloc>().add(
              ConsultationCustomerRemovedService(
                ConsultationCustomerRemoveServiceRequestParams(
                  productID: 0,
                  id: service?.id,
                  customerID: int.tryParse(widget.consumerId ?? '0'),
                  serviceID: service?.serviceID,
                ),
              ),
            );
          } else {
            widget.displayServiceList.value.removeWhere(
              (final element) =>
                  element?.id == 0 && element?.serviceID == service?.serviceID,
            );
            setState(() {});
          }
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Future<void> _showNDTV(
    final BuildContext context,
    final ConsultationService? service, {
    final bool isCreateNew = false,
  }) async {
    showBaseBottomSheet(
      context: context,
      title: context.l10n.consultationContentShort,
      height: MediaQuery.of(context).size.height * 0.8,
      child: BlocProvider(
        create: TreatmentNoteService.createProvider(
          customerId: widget.consumerId,
          service: service,
          isUnassigned: widget.isUnassigned,
        ),
        child: TreatmentNoteService(
          serviceList: widget.displayServiceList,
          title: service?.serviceName ?? '',
          service: service,
          isUnassigned: widget.isUnassigned,
          isCreateNew: isCreateNew,
          customerId: widget.consumerId,
        ),
      ),
    ).whenComplete(() {
      if (isCreateNew) {
        if (context.mounted) {
          context.router.popUntilRouteWithName(CustomerBookingInfoRoute.name);
        }
      }
    });
  }

  Future<void> _onTapFloatingButton(
    final BuildContext context, {
    final ConsultationService? editedService,
  }) async {
    if (editedService != null) {
      serviceController.text = editedService.serviceName ?? '';
      actionController.text =
          widget.actionList
              .firstWhereOrNull(
                (final e) => e?.dataTypeCode == editedService.action,
              )
              ?.dataTypeName ??
          '';
      quantityController.text = editedService.quantity.toString();
      discountController.text =
          editedService.discount.toString().toCurrency()?.replaceAll(
            '.',
            ' ',
          ) ??
          '';
      noteController.text = editedService.notes ?? '';
      netFareController.text =
          editedService.netPrice.toString().toCurrency()?.replaceAll(
            '.',
            ' ',
          ) ??
          '';
      totalPriceController.text =
          editedService.total.toString().toCurrency()?.replaceAll('.', ' ') ??
          '';
    }

    final focusNode = FocusNode();

    return showBaseBottomSheet(
      context: context,
      height: MediaQuery.sizeOf(context).height * 0.8,
      child: StatefulBuilder(
        builder: (final sheetContext, final sheetState) {
          focusNode.addListener(() {
            if (!focusNode.hasFocus) {
              if (selectedService?.isNotAllowMinPrice ?? false) {
                if ((int.tryParse(netFareController.text.removeSeparators()) ??
                        0) <
                    (selectedService?.price ?? 0)) {
                  netFareController.text =
                      selectedService?.price.toString() ?? '';
                  netFareController.text =
                      netFareController.text.toCurrency()?.replaceAll(
                        '.',
                        ' ',
                      ) ??
                      '';
                  _calculateTotalPrice();
                  if (sheetContext.mounted) {
                    sheetState(() {});
                  }
                }
              }
            }
          });

          return Container(
            padding: const EdgeInsets.all(16),
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  IgnorePointer(
                    ignoring: editedService != null,
                    child: Opacity(
                      opacity: editedService != null ? 0.5 : 1,
                      child: ServiceInputField(
                        title: context.l10n.service,
                        controller: serviceController,
                        onTap: () async => _showServiceBottomSheet(
                          title: context.l10n.serviceSelectionStep,
                          currentValue: serviceController.text,
                          onSelect: (final value) {
                            selectedService = widget.serviceList
                                .firstWhereOrNull(
                                  (final e) => e?.serviceName == value,
                                );
                            serviceController.text = value;
                            netFareController.text = Utils.defaultOnEmpty(
                              selectedService?.price
                                  .toString()
                                  .toCurrency()
                                  ?.replaceAll('.', ' '),
                            );
                            quantityController.text = '1';
                            sheetState(() {});

                            _calculateTotalPrice();
                          },
                        ),
                      ),
                    ),
                  ),
                  ServiceInputField(
                    title: context.l10n.action,
                    controller: actionController,
                    onTap: () async => _showActionBottomSheet(
                      data: widget.actionList
                          .map((final e) => e?.dataTypeName)
                          .toList(),
                      title: context.l10n.action,
                      currentValue: actionController.text,
                      onSelect: (final value) {
                        actionController.text = value;
                      },
                    ),
                  ),
                  ServiceInputField(
                    title: context.l10n.amount,
                    controller: quantityController,
                    onChanged: (final _) => _calculateTotalPrice(),
                  ),
                  ServiceInputField(
                    title: context.l10n.netFare,
                    controller: netFareController,
                    errorText: _checkNetFare(
                      netFareController.text.removeSeparators(),
                    ),
                    focusNode: focusNode,
                    onChanged: (final _) {
                      _calculateTotalPrice();
                      sheetState(() {});
                    },
                  ),
                  ServiceInputField(
                    title: context.l10n.discount,
                    controller: discountController,
                    onChanged: (final _) => _calculateTotalPrice(),
                  ),
                  ServiceInputField(
                    title: context.l10n.finalCost,
                    controller: totalPriceController,
                    readOnly: true,
                  ),
                  ServiceInputField(
                    title: context.l10n.notes,
                    controller: noteController,
                    isNote: true,
                  ),
                  _buildSaveServiceButton(editedService: editedService),
                ],
              ),
            ),
          );
        },
      ),
      title: context.l10n.service,
    ).whenComplete(() => focusNode.dispose());
  }

  String? _checkNetFare(final String value) {
    if (selectedService?.isNotAllowMinPrice ?? false) {
      if ((int.tryParse(value) ?? 0) < (selectedService?.price ?? 0)) {
        return context.l10n.minNetFareError;
      }
    }

    return null;
  }

  void _calculateTotalPrice() {
    totalPriceController.text = Utils.defaultOnEmpty(
      ((int.tryParse(netFareController.text.removeSeparators()) ?? 0) *
                  (int.tryParse(quantityController.text.removeSeparators()) ??
                      0) -
              (int.tryParse(discountController.text.removeSeparators()) ?? 0))
          .toString(),
    );
    if (totalPriceController.text.removeSeparators().contains('-')) {
      totalPriceController.text = '0';
    }
    totalPriceController.text =
        totalPriceController.text.toCurrency()?.replaceAll('.', ' ') ?? '';
  }

  Future<void> _showServiceBottomSheet({
    required final String? title,
    required final String? currentValue,
    required final void Function(String) onSelect,
  }) async {
    selectingValue = currentValue;
    searchController.clear();

    return showBaseBottomSheet(
      context: context,
      height: MediaQuery.sizeOf(context).height * 0.7,
      title: Utils.defaultOnEmpty(title),
      child: StatefulBuilder(
        builder: (final _, final sheetState) {
          return BlocProvider<ConsultationCustomerBloc>.value(
            value: context.read<ConsultationCustomerBloc>(),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: <Widget>[
                  if (title != context.l10n.selectGender)
                    BaseSearchBar(
                      controller: searchController,
                      onClear: () {
                        searchController.clear();
                        _onlineSearch(searchController.text);
                      },
                      hintText: context.l10n.findByName,
                      onChanged: (final key) {
                        _onlineSearch(key);
                      },
                    ),
                  const SizedBox(height: 16),
                  BlocBuilder<
                    ConsultationCustomerBloc,
                    ConsultationCustomerState
                  >(
                    builder: (final context, final state) {
                      if (state is ConsultationCustomerGetServiceSuccess) {
                        return _buildListItem(
                          state.consultationCustomerGetService?.items
                                  .map((final e) => e?.serviceName)
                                  .toList() ??
                              [],
                          sheetState: sheetState,
                        );
                      }
                      if (state is ConsultationCustomerInProgress) {
                        return const Expanded(child: LoadingWidget());
                      }

                      return const Expanded(child: SizedBox.expand());
                    },
                  ),
                  _buildSelectButton(onSelect),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSelectButton(final void Function(String) onSelect) {
    return CustomSizeButton(
      content: Text(
        context.l10n.choose,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.surface,
        ),
      ),
      onTap: () {
        if (selectingValue?.isNotEmpty ?? false) {
          onSelect(Utils.defaultOnEmpty(selectingValue));
        }
        Navigator.of(context).pop();
      },
      width: double.maxFinite,
      height: 50,
    );
  }

  Widget _buildSaveServiceButton({final ConsultationService? editedService}) {
    return CustomSizeButton(
      content: Text(
        context.l10n.save,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.surface,
        ),
      ),
      onTap: () {
        final item = ConsultationService(
          id: editedService?.id ?? 0,
          serviceID: editedService?.serviceID ?? selectedService?.serviceID,
          serviceName:
              editedService?.serviceName ?? selectedService?.serviceName,
          serviceCode:
              editedService?.serviceCode ?? selectedService?.serviceCode,
          action: widget.actionList
              .firstWhereOrNull(
                (final e) => e?.dataTypeName == actionController.text,
              )
              ?.dataTypeCode,
          quantity:
              int.tryParse(quantityController.text.removeSeparators()) ?? 0,
          netPrice:
              int.tryParse(netFareController.text.removeSeparators()) ?? 0,
          discount:
              int.tryParse(discountController.text.removeSeparators()) ?? 0,
          total:
              int.tryParse(totalPriceController.text.removeSeparators()) ?? 0,
          notes: noteController.text,
          departmentCode:
              editedService?.departmentCode ?? selectedService?.departmentCode,
        );
        // flow edit
        if (editedService != null) {
          if (item.id != null && item.id != 0) {
            context.read<ConsultationCustomerBloc>().add(
              ConsultationCustomerEditedService(
                ConsultationCustomerEditServiceRequestParams(
                  id: item.id,
                  customerID: int.tryParse(widget.consumerId ?? '0'),
                  serviceID: item.serviceID,
                  action: item.action,
                  quantity: item.quantity,
                  netPrice: item.netPrice?.toDouble(),
                  discount: item.discount?.toDouble(),
                  total: item.total?.toDouble(),
                  notes: item.notes,
                  empCode: EZCache.shared.getUserProfile()?.employeeId,
                ),
              ),
            );
          }
          final index = widget.displayServiceList.value.indexWhere(
            (final element) => element?.id == editedService.id,
          );
          widget.displayServiceList.removeIndex(index);
          widget.displayServiceList.value.insert(index, item);
        } else {
          widget.displayServiceList.add(item);
        }
        setState(() {});
        Navigator.of(context).pop();
        _clearAllTextController();
      },
      width: double.maxFinite,
      height: 50,
    );
  }

  void _clearAllTextController() {
    serviceController.clear();
    actionController.clear();
    quantityController.clear();
    netFareController.clear();
    discountController.clear();
    noteController.clear();
    totalPriceController.clear();
    searchController.clear();
    selectingValue = null;
    selectedService = null;
    selectedAction = null;
  }

  Future<void> _showActionBottomSheet({
    required final String? title,
    required final String? currentValue,
    required final List<String?> data,
    required final void Function(String) onSelect,
  }) async {
    selectingValue = currentValue;
    List<String?> displayData = data;
    searchController.clear();

    return showBaseBottomSheet(
      context: context,
      height: MediaQuery.sizeOf(context).height * 0.7,
      title: Utils.defaultOnEmpty(title),
      child: StatefulBuilder(
        builder: (final _, final sheetState) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: <Widget>[
                if (title != context.l10n.selectGender)
                  BaseSearchBar(
                    controller: searchController,
                    onClear: () => sheetState(() {
                      searchController.clear();
                      displayData = data;
                    }),
                    hintText: context.l10n.findByName,
                    onChanged: (final key) {
                      displayData = data
                          .where(
                            (final element) =>
                                element?.toLowerCase().contains(
                                  key.toLowerCase(),
                                ) ??
                                false,
                          )
                          .toList();
                      sheetState(() {});
                    },
                  ),
                const SizedBox(height: 16),
                _buildListItem(displayData, sheetState: sheetState),
                _buildSelectButton(onSelect),
              ],
            ),
          );
        },
      ),
    );
  }

  void _onlineSearch(final String key) {
    if (key.length < 3) {
      return;
    }
    timer?.cancel();
    timer = Timer(const Duration(milliseconds: 800), () async {
      context.read<ConsultationCustomerBloc>().add(
        ConsultationCustomerGotService(
          ConsultationCustomerGetConsultationService(search: key),
        ),
      );
    });
  }

  Expanded _buildListItem(
    final List<String?> data, {
    required final StateSetter sheetState,
  }) {
    return Expanded(
      child: ListView.separated(
        itemBuilder: (final _, final index) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              selectingValue = data[index];
              sheetState(() {});
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      Utils.defaultOnEmpty(data[index]),
                      style: selectingValue == data[index]
                          ? Theme.of(context).textTheme.labelLarge?.copyWith(
                              color: Theme.of(context).primaryColor,
                            )
                          : Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  if (selectingValue == data[index])
                    Icon(
                      Icons.check,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                ],
              ),
            ),
          );
        },
        separatorBuilder: (final _, final __) {
          return const Divider(height: 16);
        },
        itemCount: data.length,
      ),
    );
  }

  String serviceTotalCost(final ConsultationService? service) =>
      Utils.defaultOnEmpty(
        service?.total?.toString().toCurrency()?.replaceAll('.', ' '),
        fallback: '0',
      );
}
