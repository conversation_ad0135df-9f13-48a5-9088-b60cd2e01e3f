// Flutter imports:
import 'package:ez_core/ez_core.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../core/params/service_type_purchase_detail_request_params.dart';
import '../../../core/utils/utils.dart';
import '../../../domain/entities/service_type_purchase_detail.dart';
import '../../../injector/injector.dart';
import '../bloc/service_type_purchase_bloc.dart';
import 'consultation_combo_sheet.dart';
import 'service_type_purchase_sheet.dart';

class ServiceTypePurchaseDetailSheet extends StatefulWidget {
  const ServiceTypePurchaseDetailSheet({
    super.key,
    required this.partnerId,
    required this.itemId,
    required this.title,
    required this.typePurchase,
  });

  final String partnerId;
  final String itemId;
  final String title;
  final ServiceTypePurchase typePurchase;

  static Future<String?> show(
    final BuildContext context, {
    required final String title,
    required final String partnerId,
    required final String itemId,
    required final ServiceTypePurchase typePurchase,
    final Function(String)? onSubmit,
  }) {
    return showModalBottomSheet<String?>(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (final context) => baseBottomLayout(
        height: MediaQuery.sizeOf(context).height * 0.75,
        ServiceTypePurchaseDetailSheet(
          typePurchase: typePurchase,
          partnerId: partnerId,
          itemId: itemId,
          title: title,
        ),
      ),
    ).then((final String? val) {
      return val;
    });
  }

  @override
  State<ServiceTypePurchaseDetailSheet> createState() =>
      _ServiceTypePurchaseDetailSheetState();
}

class _ServiceTypePurchaseDetailSheetState
    extends State<ServiceTypePurchaseDetailSheet> {
  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<ServiceTypePurchaseBloc>()
        ..add(
          ServiceTypePurchaseDetailStarted(
            ServiceTypePurchaseDetailRequestParams(
              partnerId: widget.partnerId,
              itemId: widget.itemId,
            ),
          ),
        ),
      child: _buildFieldService(
        context,
        widget.title,
        vServicePurchase: serviceDetail,
      ),
    );
  }

  ServiceTypePurchaseDetail? serviceDetail;

  Widget _buildFieldService(
    final BuildContext context,
    final String title, {
    final ServiceTypePurchaseDetail? vServicePurchase,
  }) {
    return BlocConsumer<ServiceTypePurchaseBloc, ServiceTypePurchaseState>(
      listener: (final context, final state) {
        if (state.status == ServiceTypePurchaseStatus.detailSuccess) {
          serviceDetail = Utils.getData(state.data);
        }
      },
      builder: (final context, final state) {
        if (state.status == ServiceTypePurchaseStatus.loading) {
          return const Center(child: LoadingWidget());
        }
        return DecoratedBox(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              vServicePurchase?.itemName ?? '',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w700,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${context.l10n.totalCount}: ',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Theme.of(context).hintColor),
                          ),
                          Text(
                            '123',
                            // vServicePurchase?.total?.toString()??'',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Theme.of(context).hintColor),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (widget.typePurchase == ServiceTypePurchase.combo)
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children:
                            vServicePurchase?.itemIDChildrens
                                ?.map(
                                  (final item) => Column(
                                    spacing: 4,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 8.0,
                                        ),
                                        child: Column(
                                          spacing: 4,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            _buildService(
                                              item.itemName ?? '',
                                              context,
                                            ),
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  '${context.l10n.con}: ',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        color: Theme.of(
                                                          context,
                                                        ).hintColor,
                                                      ),
                                                ),
                                                Text(
                                                  item.totalRow.toString(),
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        color: Theme.of(
                                                          context,
                                                        ).hintColor,
                                                      ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),

                                      const Divider(thickness: .35),
                                    ],
                                  ),
                                )
                                .toList() ??
                            [],
                      ),
                    ),
                  ),
                if (widget.typePurchase == ServiceTypePurchase.deal)
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children:
                            vServicePurchase?.itemIDChildrens
                                ?.map(
                                  (final item) => Column(
                                    spacing: 4,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 8.0,
                                        ),
                                        child: Column(
                                          spacing: 4,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            _buildService(
                                              item.itemName ?? '',
                                              context,
                                            ),
                                          ],
                                        ),
                                      ),

                                      const Divider(thickness: .35),
                                    ],
                                  ),
                                )
                                .toList() ??
                            [],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Row _buildService(final String name, final BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 12.0),
            child: Text(name, style: Theme.of(context).textTheme.bodyLarge),
          ),
        ),
        //  GestureDetector(
        //   onTapDown: (final details) {
        //     showMenu(
        //       context: context,
        //       position: RelativeRect.fromRect(
        //         Rect.fromLTWH(
        //           details.globalPosition.dx+10,
        //           details.globalPosition.dy+10,
        //           0,
        //           0,
        //         ),
        //         Offset.zero & Size.infinite,
        //       ),
        //       items: [
        //         PopupMenuItem(
        //           onTap: () {},
        //         value: 1,
        //           child: const Text('xoa'),
        //         ),
        //         PopupMenuItem(
        //            onTap: () {},
        //         value: 1,
        //           child: const Text('xoa'),
        //         ),
        //       ],
        //     );
        //   },
        //   child: const Padding(
        //     padding:
        //EdgeInsets.symmetric(horizontal: 8.0),
        //     child: Icon(Icons.abc),
        //   ))
      ],
    );
  }
}
