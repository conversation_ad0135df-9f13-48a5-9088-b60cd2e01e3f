// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../core/params/service_acc_params.dart';
import '../../../core/params/service_combo_request_params.dart';
import '../../../core/params/service_deal_request_params.dart';
import '../../../core/utils/utils.dart';
import '../../../domain/entities/service_from_acc.dart';
import '../../../domain/entities/service_type_purchase.dart';
import '../../../injector/injector.dart';
import '../../story_list/widgets/story_loading_more.dart';
import '../../widgets/widgets.dart';
import '../bloc/service_type_purchase_bloc.dart';
import 'consultation_combo_sheet.dart';

enum ServiceTypePurchase { combo, deal, service }

class ServiceTypePurchaseSheet extends StatefulWidget {
  const ServiceTypePurchaseSheet({
    super.key,
    required this.typePurchase,
    required this.serviceSelected,
    this.isComboService = false,
    this.isDealService = false,
    required this.title,
  });
  static List<ComboPurchaseItems>? items = [];
  static List<ComboPurchaseItems>? itemDeals = [];
  // static List<DealPurchaseItems>? itemDeals = [];
  final ServiceTypePurchase typePurchase;
  final List<ServiceSelected> serviceSelected;
  final bool isComboService;
  final bool isDealService;
  final String title;

  static Future<String?> show(
    final BuildContext context, {
    required final String? title,
    required final ServiceTypePurchase typePurchase,
    required final List<ServiceSelected> serviceSelected,
    final bool isComboService = false,
    final bool isDealService = false,
    final Function(String)? onSubmit,
  }) {
    return showModalBottomSheet<String?>(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (final context) => baseBottomLayout(
        height: MediaQuery.sizeOf(context).height * 0.75,
        ServiceTypePurchaseSheet(
          title: title ?? '',
          typePurchase: typePurchase,
          serviceSelected: serviceSelected,
          isComboService: isComboService,
          isDealService: isDealService,
        ),
      ),
    ).then((final String? val) {
      return val;
    });
  }

  @override
  State<ServiceTypePurchaseSheet> createState() =>
      _ServiceTypePurchaseSheetState();
}

class _ServiceTypePurchaseSheetState extends State<ServiceTypePurchaseSheet> {
  final ValueNotifierList<ServiceSelected> serviceSelected = ValueNotifierList(
    [],
  );
  final ValueNotifierList<ServiceSelected> typesPurchase = ValueNotifierList(
    [],
  );
  //final ValueNotifierList<DealPurchase> dealPurchase = ValueNotifierList([]);
  final List<ComboPurchaseItems> items = [];
  late ScrollController _scrollController;
  int page = 1;
  @override
  void initState() {
    super.initState();
    serviceSelected.value = widget.serviceSelected;
    _scrollController = ScrollController();
  }

  final ValueNotifier<bool> isLoadingMore = ValueNotifier(false);
  final ValueNotifier<bool> isEndList = ValueNotifier(false);
  void _lazyStoryAll(
    final BuildContext context, {
    required final double maxScroll,
    required final double currentScroll,
  }) {
    if (!isEndList.value) {
      if (!isLoadingMore.value) {
        if (currentScroll >= (maxScroll * 0.9)) {
          page++;
          context.read<ServiceTypePurchaseBloc>().add(
            ComboServiceGetMore(ServiceAccParams(pageIndex: page)),
          );
        }
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: widget.typePurchase == ServiceTypePurchase.combo
          ? (final context) => getIt<ServiceTypePurchaseBloc>()
              ..add(
                ComboServiceStarted(
                  ComboServiceRequestParams(
                    widget.typePurchase.name,
                    widget.typePurchase.name.toUpperCase(),
                  ),
                ),
              )
          : widget.typePurchase == ServiceTypePurchase.deal
          ? (final context) => getIt<ServiceTypePurchaseBloc>()
              ..add(
                DealServiceStarted(
                  DealServiceRequestParams(
                    widget.typePurchase.name,
                    widget.typePurchase.name.toUpperCase(),
                  ),
                ),
              )
          : (final context) =>
                getIt<ServiceTypePurchaseBloc>()
                  ..add(ServiceStarted(ServiceAccParams())),
      child: BlocConsumer<ServiceTypePurchaseBloc, ServiceTypePurchaseState>(
        listener: (final context, final state) {
          if (state.status == ServiceTypePurchaseStatus.loadingMore) {
            isLoadingMore.value = true;
          }
          if (state.status == ServiceTypePurchaseStatus.loadMoreSuccess) {
            final ServiceFromAcc dataMore = Utils.getData(state.data);
            if (dataMore.items.isEmpty) {
              isEndList.value = true;
            }
            final listSafety = dataMore.items
                .map((final e) => e ?? ServiceFromAccItems())
                .toList();
            typesPurchase.addLastList(
              listSafety
                  .map(
                    (final e) => ServiceSelected(
                      id: e.itemID ?? '',
                      name: e.itemName ?? '',
                    ),
                  )
                  .toList(),
            );
            isLoadingMore.value = false;
          }
          if (state.status == ServiceTypePurchaseStatus.success) {
            final ServiceFromAcc data = Utils.getData(state.data);
            typesPurchase.setValue([
              if (widget.isComboService)
                ...(ServiceTypePurchaseSheet.items ?? []).map(
                  (final el) => ServiceSelected(
                    id: el.itemID ?? '',
                    name: el.itemName ?? '',
                  ),
                ),
              if (widget.isDealService)
                ...ServiceTypePurchaseSheet.itemDeals?.map(
                      (final el) => ServiceSelected(
                        id: el.itemID ?? '',
                        name: el.itemID ?? '',
                      ),
                    ) ??
                    [],
              ...data.items.map(
                (final e) => ServiceSelected(
                  id: e?.itemID ?? '',
                  name: e?.itemName ?? '',
                ),
              ),
            ]);
          }
          if (state.status == ServiceTypePurchaseStatus.comboSuccess) {
            final List<ComboPurchase> combos = state.data;
            typesPurchase.setValue(
              combos
                  .map(
                    (final e) => ServiceSelected(
                      id: e.comboID ?? '',
                      name: e.comboName ?? '',
                      items: e.items,
                    ),
                  )
                  .toList(),
            );
          }
          if (state.status == ServiceTypePurchaseStatus.dealSuccess) {
            final List<DealPurchase> deal = state.data;
            typesPurchase.setValue(
              deal
                  .map(
                    (final e) => ServiceSelected(
                      id: e.dealID ?? '',
                      name: e.dealName ?? '',
                    ),
                  )
                  .toList(),
            );
          }
        },
        builder: (final context, final state) {
          if (state.status == ServiceTypePurchaseStatus.init) {
            if (widget.typePurchase == ServiceTypePurchase.service) {
              _scrollController = ScrollController()
                ..addListener(() {
                  final maxScroll = _scrollController.position.maxScrollExtent;
                  final currentScroll = _scrollController.offset;
                  if (maxScroll > 300) {
                    _lazyStoryAll(
                      context,
                      maxScroll: maxScroll,
                      currentScroll: currentScroll,
                    );
                  }
                });
            }
            return const LoadingWidget();
          }
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 12),
                // padding: const EdgeInsets.all(12.0),
                child: Center(
                  child: Text(
                    widget.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontSize: 18,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Divider(
                thickness: .35,
                color: Theme.of(context).primaryColor.withValues(alpha: .7),
              ),

              _buildServiceList(),
            ],
          );
        },
      ),
    );
  }

  ValueListenableBuilder<List<ServiceSelected>> _buildServiceList() {
    return ValueListenableBuilder(
      valueListenable: typesPurchase,
      builder: (final context, final vCombos, final child) {
        return vCombos.isEmpty
            ? Center(child: Text(context.l10n.listIsEmpty))
            : ValueListenableBuilder<bool>(
                valueListenable: isEndList,
                builder: (final context, final vEndList, final child) {
                  return ValueListenableBuilder<bool>(
                    valueListenable: isLoadingMore,
                    builder: (final context, final vLoadingMore, final child) {
                      return ValueListenableBuilder(
                        valueListenable: serviceSelected,
                        builder: (final context, final vSelected, final child) {
                          return Expanded(
                            child: ListView.builder(
                              controller:
                                  widget.typePurchase ==
                                      ServiceTypePurchase.service
                                  ? _scrollController
                                  : null,
                              itemCount: (vLoadingMore || vEndList)
                                  ? vCombos.length + 1
                                  : vCombos.length,
                              itemBuilder: (_, final i) {
                                if (i == vCombos.length && vLoadingMore) {
                                  return const StoryLoadingMore();
                                } else if (i == vCombos.length && vEndList) {
                                  return Center(
                                    child: Text(
                                      context.l10n.isEnd,
                                      style: TextStyle(
                                        color: Theme.of(
                                          context,
                                        ).hintColor.withValues(alpha: .7),
                                      ),
                                    ),
                                  );
                                } else {
                                  final item = vCombos[i];
                                  return ListTile(
                                    onTap: () {
                                      String? data;
                                      if (widget.typePurchase ==
                                          ServiceTypePurchase.combo) {
                                        data = item.toComboString();
                                        ServiceTypePurchaseSheet.items =
                                            item.items;
                                      }
                                      if (widget.typePurchase ==
                                          ServiceTypePurchase.deal) {
                                        data = item.toDealString();
                                      }
                                      if (widget.typePurchase ==
                                          ServiceTypePurchase.service) {
                                        data = item.toServiceString();
                                      }

                                      Navigator.of(context).pop(data);
                                    },
                                    title: Text(item.name),
                                  );
                                }
                              },
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              );
      },
    );
  }
}

class ServiceSelected {
  ServiceSelected({required this.id, required this.name, this.items});

  final String id;
  final String name;
  final List<ComboPurchaseItems>? items;
  Map<String, dynamic> toComboJson() {
    return <String, dynamic>{'ComboID': id, 'ComboName': name};
  }

  Map<String, dynamic> toDealJson() {
    return <String, dynamic>{'DealID': id, 'DealName': name};
  }

  Map<String, dynamic> toServiceJson() {
    return <String, dynamic>{'ServiceCode': id, 'ServiceName': name};
  }

  String toComboString() {
    return json.encode(toComboJson());
  }

  String toDealString() {
    return json.encode(toDealJson());
  }

  String toServiceString() {
    return json.encode(toServiceJson());
  }
}
