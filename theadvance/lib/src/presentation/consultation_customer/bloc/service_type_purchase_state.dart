part of 'service_type_purchase_bloc.dart';

enum ServiceTypePurchaseStatus {
  init,
  loading,
  loadingMore,
  loadMoreSuccess,
  success,
  detailSuccess,
  comboSuccess,
  dealSuccess,
  getBranchsuccess,
  createComboSuccess,
  updateComboSuccess,
  getSaveSuccess,
  removeSuccess,
  failure,
}

@immutable
class ServiceTypePurchaseState extends Equatable {
  const ServiceTypePurchaseState(this.status, {this.data, this.isLast = false});
  final dynamic data;
  final ServiceTypePurchaseStatus status;
  final bool isLast;

  ServiceTypePurchaseState copyWith<T>(
    final ServiceTypePurchaseStatus status, {
    final dynamic data,
    final bool isLast = false,
  }) {
    return ServiceTypePurchaseState(status, data: data, isLast: isLast);
  }

  @override
  List<Object?> get props => [status, data, isLast];
}
