// Dart imports:
import 'dart:async';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

// Project imports:
import '../../../core/params/create_combo_body.dart';
import '../../../core/params/service_acc_params.dart';
import '../../../core/params/service_combo_request_params.dart';
import '../../../core/params/service_deal_request_params.dart';
import '../../../core/params/service_type_purchase_detail_request_params.dart';
import '../../../domain/usecases/consultation_customer/create_combo_usecase.dart';
import '../../../domain/usecases/consultation_customer/get_combo_service_usecase.dart';
import '../../../domain/usecases/consultation_customer/get_deal_service_usecase.dart';
import '../../../domain/usecases/consultation_customer/get_service_from_acc_usecase.dart';
import '../../../domain/usecases/consultation_customer/update_combo_usecase.dart';

part 'service_type_purchase_event.dart';
part 'service_type_purchase_state.dart';

@injectable
class ServiceTypePurchaseBloc
    extends Bloc<ServiceTypePurchaseEvent, ServiceTypePurchaseState> {
  ServiceTypePurchaseBloc(
    this._getComboServiceUseCase,
    this._getDealServiceUseCase,
    this._getServiceConsultationCustomerUseCase,
    this._createComboUseCase,
    this._updateComboUseCase,
    this._getServiceFromAccUseCase,
  ) : super(const ServiceTypePurchaseState(ServiceTypePurchaseStatus.init)) {
    on<ComboServiceStarted>(_onComboServiceStarted);
    on<DealServiceStarted>(_onDealServiceStarted);
    on<ServiceStarted>(_onServiceStarted);
    on<CreateComboStarted>(_onCreateComboStarted);
    on<UpdateComboStarted>(_onUpdateComboStarted);
    on<ComboServiceGetMore>(_onComboServiceGetMore);
  }
  final GetComboServiceUseCase _getComboServiceUseCase;
  final GetDealServiceUseCase _getDealServiceUseCase;
  final GetServiceFromAccUseCase _getServiceConsultationCustomerUseCase;
  final CreateComboServiceUseCase _createComboUseCase;
  final UpdateComboServiceUseCase _updateComboUseCase;
  final GetServiceFromAccUseCase _getServiceFromAccUseCase;

  FutureOr<void> _onComboServiceStarted(
    final ComboServiceStarted event,
    final Emitter<ServiceTypePurchaseState> emit,
  ) async {
    emit(state.copyWith(ServiceTypePurchaseStatus.loading));
    final dataState = await _getComboServiceUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.comboSuccess,
          data: dataState.data,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.failure,
          data: dataState.error,
        ),
      );
    }
  }

  FutureOr<void> _onDealServiceStarted(
    final DealServiceStarted event,
    final Emitter<ServiceTypePurchaseState> emit,
  ) async {
    emit(state.copyWith(ServiceTypePurchaseStatus.loading));
    final dataState = await _getDealServiceUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.dealSuccess,
          data: dataState.data,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.failure,
          data: dataState.error,
        ),
      );
    }
  }

  FutureOr<void> _onServiceStarted(
    final ServiceStarted event,
    final Emitter<ServiceTypePurchaseState> emit,
  ) async {
    emit(state.copyWith(ServiceTypePurchaseStatus.loading));
    final dataState = await _getServiceConsultationCustomerUseCase(
      params: event.params,
    );
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(ServiceTypePurchaseStatus.success, data: dataState.data),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.failure,
          data: dataState.error,
        ),
      );
    }
  }

  FutureOr<void> _onCreateComboStarted(
    final CreateComboStarted event,
    final Emitter<ServiceTypePurchaseState> emit,
  ) async {
    emit(state.copyWith(ServiceTypePurchaseStatus.loading));
    final dataState = await _createComboUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.createComboSuccess,
          data: dataState.data,
          isLast: event.isLast,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.failure,
          data: dataState.error,
        ),
      );
    }
  }

  FutureOr<void> _onUpdateComboStarted(
    final UpdateComboStarted event,
    final Emitter<ServiceTypePurchaseState> emit,
  ) async {
    emit(state.copyWith(ServiceTypePurchaseStatus.loading));
    final dataState = await _updateComboUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.updateComboSuccess,
          data: dataState.data,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.failure,
          data: dataState.error,
        ),
      );
    }
  }

  FutureOr<void> _onComboServiceGetMore(
    final ComboServiceGetMore event,
    final Emitter<ServiceTypePurchaseState> emit,
  ) async {
    emit(state.copyWith(ServiceTypePurchaseStatus.loadingMore));
    final dataState = await _getServiceFromAccUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.loadMoreSuccess,
          data: dataState.data,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(
          ServiceTypePurchaseStatus.failure,
          data: dataState.error,
        ),
      );
    }
  }
}
