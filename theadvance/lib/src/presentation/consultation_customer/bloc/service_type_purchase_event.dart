part of 'service_type_purchase_bloc.dart';

@immutable
abstract class ServiceTypePurchaseEvent {}

class ComboServiceStarted extends ServiceTypePurchaseEvent {
  ComboServiceStarted(this.params);

  final ComboServiceRequestParams params;
}

class DealServiceStarted extends ServiceTypePurchaseEvent {
  DealServiceStarted(this.params);

  final DealServiceRequestParams params;
}

class ServiceStarted extends ServiceTypePurchaseEvent {
  ServiceStarted(this.params);
  final ServiceAccParams params;
}

class ServiceTypePurchaseDetailStarted extends ServiceTypePurchaseEvent {
  ServiceTypePurchaseDetailStarted(this.params);
  final ServiceTypePurchaseDetailRequestParams params;
}

class CreateComboStarted extends ServiceTypePurchaseEvent {
  CreateComboStarted({required this.params, this.isLast = false});
  final CreateComboBody params;
  final bool isLast;
}

class UpdateComboStarted extends ServiceTypePurchaseEvent {
  UpdateComboStarted(this.params);
  final CreateComboBody params;
}

class ComboServiceGetMore extends ServiceTypePurchaseEvent {
  ComboServiceGetMore(this.params);
  final ServiceAccParams params;
}
