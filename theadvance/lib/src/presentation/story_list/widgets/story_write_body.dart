// Dart imports:
// ignore_for_file: avoid_redundant_argument_values

// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_permission/ez_permission.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter_quill_delta_from_html/parser/html_to_delta.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:image_size_getter/file_input.dart';
import 'package:image_size_getter/image_size_getter.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart' as quill;

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/params/address_location_google_request_params.dart';
import '../../../core/params/address_near_location_google_request_params.dart';
import '../../../core/params/address_search_request_params.dart';
import '../../../core/params/emoji_list_request_params.dart';
import '../../../core/params/mention_params.dart';
import '../../../core/params/poll_request_params.dart';
import '../../../core/params/request_params.dart';
import '../../../core/params/social_upload_file_request_params.dart';
import '../../../core/params/story_write_info_request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/address_location_google.dart';
import '../../../domain/entities/address_near_location_google.dart';
import '../../../domain/entities/address_search_location_google.dart';
import '../../../domain/entities/emoji_list.dart';
import '../../../domain/entities/entities.dart';
import '../../../domain/entities/file_info.dart';
import '../../../domain/entities/story_rule.dart';
import '../../../injector/injector.dart';
import '../../account/widgets/account_field.dart';
import '../../chat/widgets/hiden_cursor.dart';
import '../../collaborator/profile/view/profile_gallery_page.dart';
import '../../comment_list/bloc/comment_list_bloc.dart';
import '../../create_chat_group/create_chat_group.dart';
import '../../settings/fonts/fonts_bloc.dart';
import '../../widgets/widgets.dart';
import '../bloc/story_write_bloc.dart';
import 'background_color_page.dart';
import 'draggble_comment.dart';
import 'emoji.dart';
import 'poll_card.dart';
import 'story_body.dart';
import 'story_write_file_card.dart';
import 'story_write_media_card.dart';
import 'story_write_overlay_body.dart';

class _WReplace {
  _WReplace(this.start, this.wOld, this.wNew);

  final int start;
  final String wOld;
  final String wNew;
}

class StoryWriteBody extends StatefulWidget {
  const StoryWriteBody({super.key, this.isStoryPersonBody = false});
  static ValueNotifierList<File> imageSelected = ValueNotifierList([]);
  static ValueNotifierList<XFile> xfile = ValueNotifierList([]);
  static ValueNotifier<bool?> isCompressDone = ValueNotifier(null);
  final bool isStoryPersonBody;
  @override
  State<StoryWriteBody> createState() => _StoryWriteBodyState();
}

class _StoryWriteBodyState extends State<StoryWriteBody> {
  OverlayEntry? _overlayEntry;
  bool _isOverlayVisible = false;
  final _layerLink = LayerLink();

  var _start = '';
  var _query = '';
  var _last = '';
  late FocusNode storyFocusNode;
  late final QuillController _controller;
  final ValueNotifier<OptionContent> isOptionContent = ValueNotifier(
    OptionContent.none,
  );
  OptionStory optionStory = OptionStory.none;
  CreateChatGroupUserLoad? data;
  final ValueNotifierList<Tags> tagList = ValueNotifierList([]);
  final ValueNotifier<bool> isValuePermission = ValueNotifier(false);
  final ValueNotifier<bool> isSubmit = ValueNotifier(false);
  final ValueNotifier<bool> isSearchLocation = ValueNotifier(false);
  final ValueNotifier<bool> isGetLatLngSuccess = ValueNotifier(false);
  final ValueNotifierList<AddressNearLocationGoogleResults> locationNearList =
      ValueNotifierList([]);
  final ValueNotifierList<AddressSearchLocationGooglePredictions>
  locationSearchList = ValueNotifierList([]);
  final staffSearch = TextEditingController(text: Strings.empty);
  final locationSearch = TextEditingController(text: Strings.empty);
  final TextEditingController titlePollController = TextEditingController(
    text: Strings.empty,
  );
  String locationString = '';
  double? lat = 0.0;
  double? lng = 0.0;
  TagList? dataTagList;
  final ValueNotifierList<TagListItems> mentionList = ValueNotifierList([]);
  final ValueNotifier<bool> stateMentionList = ValueNotifier(false);
  List<TagListItems> mentionTemp = [];
  final ValueNotifier<double> oDy = ValueNotifier(0.0);
  final ValueNotifier<String> query = ValueNotifier(Strings.empty);
  final ValueNotifier<String> content = ValueNotifier(Strings.empty);
  final ValueNotifierList<FileInfo> heightList = ValueNotifierList([]);
  PollRequestParams? poll;
  final ValueNotifier<double> scaleHeight = ValueNotifier(0);
  late BuildContext contexTemp;
  final GlobalKey<QuillRawEditorState> editorKey = GlobalKey();
  final Pattern combinedPattern = RegExp(Strings.regExpStory);
  final TextPartStyleDefinitions styles = TextPartStyleDefinitions(
    definitionList: <TextPartStyleDefinition>[
      TextPartStyleDefinition(
        style: TextStyle(
          color: Colors.blue,
          backgroundColor: Colors.blue.withValues(alpha: 0.1),
        ),
        pattern: Strings.regExpComment,
      ),
    ],
  );
  @override
  void initState() {
    const initialJson = r'''
    [
      {"insert": "\n"}
    ]
    ''';
    WidgetsBinding.instance.addPostFrameCallback((final timeStamp) async {
      EzLocation.hasPermission().then((final isPermission) {
        if (isPermission) {
          EzLocation.getLocation().then((final vLocation) {
            lat = vLocation?.latitude;
            lng = vLocation?.longitude;
            locationString = '$lat,$lng';
            isGetLatLngSuccess.value = true;
          });
          isValuePermission.value = true;
        }
      });
    });
    _controller =
        QuillController(
          document: Document.fromJson(jsonDecode(initialJson)),
          selection: const TextSelection.collapsed(offset: 0),
        )..addListener(() {
          _onShowToolBar();
          _onChanged(contexTemp);
          _updateCaretOffset(_controller.plainTextEditingValue.text.trim());
        });
    storyFocusNode = FocusNode();
    storyFocusNode.requestFocus();
    locationSearch.addListener(() {
      isSearchLocation.value = locationSearch.text.isNotEmpty;
    });
    super.initState();
  }

  void _onShowToolBar() {
    if (_controller.selection.extentOffset - _controller.selection.baseOffset >
        2) {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }
      _debounce = Timer(const Duration(milliseconds: 500), () {
        editorKey.currentState?.showToolbar();
      });
    } else {
      editorKey.currentState?.hideToolbar();
    }
  }
  // String _previousText = '';

  @override
  void dispose() {
    _controller.removeListener(() {
      _onShowToolBar();
      _onChanged(contexTemp);
      _updateCaretOffset(_controller.plainTextEditingValue.text.trim());
    });
    _debounce?.cancel();
    storyFocusNode.dispose();
    _controller.dispose();
    StoryWriteBody.imageSelected.setValue([]);
    StoryWriteBody.xfile.setValue([]);
    super.dispose();
  }

  bool blockReplace = false;
  StoryRule? dataRule;
  EmojiList? dataEmojiList;
  String titleRule = Strings.titleRule;
  String contentRule = Strings.contentRule;
  ValueNotifier<StoryWriteInfoRequestParams> storyInfo = ValueNotifier(
    StoryWriteInfoRequestParams(
      content: Strings.empty,
      attachment: [],
      location: Location(
        long: Strings.empty,
        lat: Strings.empty,
        address: Strings.empty,
        name: Strings.empty,
      ),
      emoji: EmojiParams(text: Strings.empty, icon: Strings.empty),
      tags: [],
      theme: [],
      mention: [],
    ),
  );
  AddressLocationGoogle? dataAddress;
  AddressNearLocationGoogle? dataNearLocation;
  AddressSearchLocationGoogle? dataAddressSearch;
  Timer? _debounce;
  int page = 1;
  void _onSearchStaffChanged(final BuildContext context, final String query) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }
    _debounce = Timer(const Duration(milliseconds: 800), () {
      page = 1;
      context.read<CreateChatGroupBloc>().add(
        CreateChatGroupUserLoaded(
          CreateChatGroupUserLoadRequestParams(search: query, page: page),
        ),
      );
    });
  }

  void _onSearchLocationChanged(
    final BuildContext context,
    final String query,
  ) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }
    _debounce = Timer(const Duration(milliseconds: 800), () {
      page = 1;
      context.read<StoryWriteBloc>().add(
        GetAddressSearchLocationStoryWriteEvent(
          AddressSearchLocationGoogleRequestParams(input: query),
        ),
      );
    });
  }

  bool isAttributesNotEmpty(final List<Map<String, dynamic>> dataList) {
    for (final item in dataList) {
      if (item.containsKey('attributes') &&
          item['attributes'] is Map &&
          // ignore: avoid_dynamic_calls
          item['attributes'].isNotEmpty) {
        return true;
      }
    }
    return false;
  }

  List<_WReplace> wReplace = [];
  void handleWithRegex(
    String content,
    final RegExp reg,
    final String startTag,
    final String endTag,
  ) {
    for (final match in reg.allMatches(content)) {
      final splitText = match
          .group(0)
          ?.replaceAll(RegExp('^$startTag|$endTag\$'), '')
          .split(' ');
      String tJoin = '';
      splitText?.forEachIndexed((final i, final t) {
        if (t.contains('@')) {
          tJoin += ' $t';
        } else {
          tJoin += '$startTag${i == 0 ? '' : ' '}$t$endTag';
        }
      });

      content = content.replaceAll(match.group(0) ?? '', tJoin);
    }
  }

  void handleHtml() {
    String content = '';
    final obs = _controller.document.toDelta().toJson();
    final result = isAttributesNotEmpty(obs);
    if (result) {
      final filteredDeltaJson = obs.expand((final op) {
        if (op.containsKey('insert') &&
            op['attributes'] != null &&
            // ignore: avoid_dynamic_calls
            op['attributes']['link'] != null) {
          // ignore: avoid_dynamic_calls
          final link = op['attributes']['link'].toString();
          // ignore: avoid_dynamic_calls
          final checkBold = op['attributes']['bold'] != null;
          // ignore: avoid_dynamic_calls
          final checkItalic = op['attributes']['italic'] != null;
          if (link.startsWith('https')) {
            return [
              {
                if (checkBold) 'attributes': {'bold': true},
                if (checkItalic) 'attributes': {'italic': true},
                'insert': link,
              },
            ];
          }
        }
        return [op];
      }).toList();
      final converter = quill.QuillDeltaToHtmlConverter(
        filteredDeltaJson,
        quill.ConverterOptions(),
      );

      content = converter
          .convert()
          .replaceFirst(RegExp('<p>'), '')
          .replaceFirst(RegExp('</p>'), '', converter.convert().length - 4);
    } else {
      content = _controller.plainTextEditingValue.text.trim();
    }
    final clearContent = content.replaceAll('&#47;', '/');
    final iLast = clearContent.lastIndexOf(RegExp('</p>'));
    content = clearContent.substring(0, iLast != -1 ? iLast : null);
    final RegExp regex1 = RegExp(r'(<strong>(?:[^<]*?@\S+[^<]*?)</strong>)');
    final RegExp regex2 = RegExp(r'(<em>(?:[^<]*?@\S+[^<]*?)</em>)');
    final RegExp regex3 = RegExp(r'(<u>(?:[^<]*?@\S+[^<]*?)</u>)');
    final RegExp regex4 = RegExp(
      r'(<strong><em>(?:[^<]*?@\S+[^<]*?)</em></strong>)',
    );
    final RegExp regex5 = RegExp(
      r'(<strong><u>(?:[^<]*?@\S+[^<]*?)</u></strong>)',
    );

    for (final match in regex1.allMatches(content)) {
      final splitText = match
          .group(0)
          ?.replaceAll(RegExp(r'^<strong>|</strong>$'), '')
          .split(' ');
      String tJoin = '';
      splitText?.forEachIndexed((final i, final t) {
        if (t.contains('@')) {
          tJoin += ' $t';
        } else {
          tJoin += '<strong>${i == 0 ? '' : ' '}$t</strong>';
        }
      });

      content = content.replaceAll(match.group(0) ?? '', tJoin);
    }
    for (final match in regex4.allMatches(content)) {
      final splitText = match
          .group(0)
          ?.replaceAll(RegExp(r'^<strong><em>|</em></strong>$'), '')
          .split(' ');
      String tJoin = '';
      splitText?.forEachIndexed((final i, final t) {
        if (t.contains('@')) {
          tJoin += ' $t';
        } else {
          tJoin += '<strong><em>${i == 0 ? '' : ' '}$t</em></strong>';
        }
      });

      content = content.replaceAll(match.group(0) ?? '', tJoin);
    }
    for (final match in regex5.allMatches(content)) {
      final splitText = match
          .group(0)
          ?.replaceAll(RegExp(r'^<strong><u>|</u></strong>$'), '')
          .split(' ');
      String tJoin = '';
      splitText?.forEachIndexed((final i, final t) {
        if (t.contains('@')) {
          tJoin += ' $t';
        } else {
          tJoin += '<strong><u>${i == 0 ? '' : ' '}$t</u></strong>';
        }
      });

      content = content.replaceAll(match.group(0) ?? '', tJoin);
    }

    for (final match in regex2.allMatches(content)) {
      final splitText = match
          .group(0)
          ?.replaceAll(RegExp(r'^<em>|</em>$'), '')
          .split(' ');
      String tJoin = '';
      splitText?.forEachIndexed((final i, final t) {
        if (t.contains('@')) {
          tJoin += ' $t';
        } else {
          tJoin += '<em>${i == 0 ? '' : ' '}$t</em>';
        }
      });

      content = content.replaceAll(match.group(0) ?? '', tJoin);
    }
    for (final match in regex3.allMatches(content)) {
      final splitText = match
          .group(0)
          ?.replaceAll(RegExp(r'^<u>|</u>$'), '')
          .split(' ');
      String tJoin = '';
      splitText?.forEachIndexed((final i, final t) {
        if (t.contains('@')) {
          tJoin += ' $t';
        } else {
          tJoin += '<u>${i == 0 ? '' : ' '}$t</u>';
        }
      });

      content = content.replaceAll(match.group(0) ?? '', tJoin);
    }
    storyInfo.value = storyInfo.value.copyWith(content: content);
  }

  void handleMention() {
    String content = _controller.plainTextEditingValue.text.trim();
    final RegExp pattern = RegExp(Strings.regExpComment, unicode: true);
    final Iterable<Match> matches = pattern.allMatches(content);
    final List<String> resultList = [];
    for (final Match match in matches) {
      resultList.add(match.group(0) ?? '');
    }
    final mentionSet = mentionTemp.toSet();
    final listUser = mentionSet
        .where(
          (final e) => resultList.contains('@${e.name?.replaceAll(' ', '_')}'),
        )
        .toList();
    final listMetion = listUser
        .map(
          (final e) => Mention(
            username: e.username,
            label: e.name,
            text: '@${e.username}',
          ),
        )
        .toList();
    for (final Mention mention in listMetion) {
      content = content.replaceAll(
        RegExp(
          '@${mention.label}'
              .replaceAll(' ', '_')
              .replaceAllMapped(
                RegExp(r'[^\w\s]'),
                (final match) => '\\${match.group(0)}',
              ),
        ),
        mention.text ?? '',
      );
    }
    storyInfo.value = storyInfo.value.copyWith(
      mention: listMetion,
      content: content,
      poll: storyInfo.value.poll,
    );
  }

  void _updateCaretOffset(final String text) {
    final TextPainter painter = TextPainter(
      textDirection: ui.TextDirection.ltr,
      text: TextSpan(text: text),
    );
    painter.layout();

    final TextPosition cursorTextPosition = _controller.selection.base;
    final Offset caretOffset = painter.getOffsetForCaret(
      cursorTextPosition,
      Rect.zero,
    );
    oDy.value = caretOffset.dy;
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<StoryWriteBloc, StoryWriteState>(
      listener: (final context, final state) {
        if (state.status == StoryWriteStatus.failure) {
          unawaited(
            ApiErrorDialog.show(
              ApiErrorParams(context, Utils.getData(state.data)),
            ),
          );
        }
      },
      builder: (final context, final state) {
        return Column(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  isOptionContent.value = OptionContent.none;
                  storyFocusNode.requestFocus();
                },
                child: SizedBox(
                  child: ColoredBox(
                    color: Theme.of(context).colorScheme.surface,
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          child: Column(
                            children: [
                              _buildHeader(),
                              SingleChildScrollView(
                                child: ValueListenableBuilder(
                                  valueListenable: StoryWriteBody.xfile,
                                  builder:
                                      (
                                        final context,
                                        final vXfile,
                                        final child,
                                      ) {
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            _buildTagLocationUI(),
                                            _buildContentBody(),
                                            if (vXfile.isNotEmpty)
                                              _buildFileBody(),
                                          ],
                                        );
                                      },
                                ),
                              ),
                            ],
                          ),
                        ),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: _buildOptionFoot(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: isOptionContent,
              builder: (final context, final vIsOpenImage, final child) {
                return vIsOpenImage == OptionContent.backgroundColor
                    ? BackgroundColorPage(storyInfo: storyInfo)
                    : vIsOpenImage == OptionContent.emoji
                    ? EmojiPage(
                        onSelected: (final emoji) {
                          var selection = _controller.selection;
                          if (!selection.isValid) {
                            selection = TextSelection.fromPosition(
                              const TextPosition(offset: 0),
                            );
                          }
                          _controller.replaceText(
                            selection.start,
                            selection.end - selection.start,
                            emoji,
                            null,
                          );
                          final newOffset = selection.start + emoji.length;
                          _controller.updateSelection(
                            TextSelection.collapsed(offset: newOffset),
                            ChangeSource.local,
                          );
                        },
                      )
                    : const SizedBox();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildTagLocationUI() {
    return ValueListenableBuilder(
      valueListenable: storyInfo,
      builder: (final builder, final vStoryInfo, final child) {
        final checkLocation = vStoryInfo.location?.name?.isEmpty ?? false;
        final checkEmoji = vStoryInfo.emoji?.text?.isEmpty ?? false;
        return ValueListenableBuilder(
          valueListenable: tagList,
          builder: (final context, final vTagList, final child) {
            final checkTags = vTagList.isEmpty;
            return AnimatedSize(
              duration: const Duration(milliseconds: 500),
              child: checkTags && checkLocation && checkEmoji
                  ? const SizedBox()
                  : _buildTagsUI(vTagList, vStoryInfo),
            );
          },
        );
      },
    );
  }

  Widget _buildFileBody() {
    return const StoryWriteFileCard();
  }

  Widget _buildContentBody() {
    return ValueListenableBuilder(
      valueListenable: StoryWriteBody.imageSelected,
      builder: (final context, final vImage, final child) {
        heightList.setValue([]);
        if (vImage.isNotEmpty) {
          SchedulerBinding.instance.addPostFrameCallback((final time) async {
            for (int i = 0; i < vImage.length; i++) {
              final item = vImage[i];
              if (StoryWriteMediaCard.checkLocalFileImage(item.path)) {
                heightList.addItem(
                  FileInfo(
                    item.path,
                    i,
                    ImageSizeGetter.getSizeResult(FileInput(item)).size.height,
                  ),
                );
              }
              if (StoryWriteMediaCard.checkLocalFileVideo(item.path)) {
                VideoThumbnail.thumbnailData(
                  video: item.path,
                  imageFormat: ImageFormat.JPEG,
                  maxWidth: 350,
                  quality: 1,
                  timeMs: 20,
                ).then((final unit) {
                  heightList.addItem(
                    FileInfo(
                      item.path,
                      i,
                      ImageSizeGetter.getSizeResult(
                        MemoryInput(unit ?? Uint8List(0)),
                      ).size.height,
                    ),
                  );
                });
              }
            }
          });
        }
        return (vImage.isEmpty)
            ? ValueListenableBuilder(
                valueListenable: storyInfo,
                builder: (final _, final vStoryInfo, final child) {
                  return (vStoryInfo.poll != null)
                      ? Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [buildTextField(), _buildDisplayPoll()],
                        )
                      : vStoryInfo.theme?.isNotEmpty ?? false
                      ? _buildTheme(
                          '${vStoryInfo.theme?[0]}',
                          '${vStoryInfo.theme?[1]}',
                        )
                      : buildTextField();
                },
              )
            : ValueListenableBuilder(
                valueListenable: heightList,
                builder: (final context, final vHeights, final child) {
                  final fileMedia = vImage
                      .where(
                        (final e) =>
                            StoryWriteMediaCard.checkLocalFileImage(e.path) ||
                            StoryWriteMediaCard.checkLocalFileVideo(e.path),
                      )
                      .toList();
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      buildTextField(),
                      StoryWriteMediaCard(
                        files: fileMedia,
                        heightPost: factoryHeight(vHeights),
                      ),
                    ],
                  );
                },
              );
      },
    );
  }

  int factoryHeight(final List<FileInfo>? item) {
    return item?.isEmpty ?? false
        ? 0
        : ((item?.length == 1 || item?.length == 3 || item?.length == 4)
                  ? item?.firstOrNull?.height
                  : item?.length == 2
                  ? ((item
                                    ?.map((final e) => e.height)
                                    .toList()
                                    .minOrNull
                                    ?.toDouble() ??
                                0) <=
                            200
                        ? 200
                        : 350)
                  : 320) ??
              0;
  }

  Widget _buildDisplayPoll() {
    final pollOption = storyInfo.value.poll?.options;
    final isAnonymous = storyInfo.value.poll?.isAnonymous;
    final title = storyInfo.value.poll?.title;
    return Stack(
      children: [
        PollCard(
          title: title,
          isAnonymous: isAnonymous ?? false,
          pollOption: pollOption,
        ),
        Positioned(
          top: 0,
          right: 5,
          child: IconButton(
            onPressed: () {
              storyInfo.value = storyInfo.value.copyWith(poll: null);
            },
            icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
          ),
        ),
      ],
    );
  }

  Widget _buildTheme(final String firstColor, final String sencondColor) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(int.parse(firstColor)),
            Color(int.parse(sencondColor)),
          ],
        ),
      ),
      height: 340,
      width: double.infinity,
      child: Center(child: buildTextField(isTheme: true)),
    );
  }

  Future<void> showEmoji() async {
    FocusScope.of(context).unfocus();
    Timer(const Duration(milliseconds: 200), () {
      isOptionContent.value = OptionContent.emoji;
    });
  }

  Future<void> showBackgroundColor() async {
    FocusScope.of(context).unfocus();
    Timer(const Duration(milliseconds: 200), () {
      isOptionContent.value = OptionContent.backgroundColor;
    });
  }

  void _addTextSpan(
    final List<InlineSpan> textSpanChildren,
    final String? textToBeStyled,
    final TextStyle? style,
  ) {
    textSpanChildren.add(TextSpan(text: textToBeStyled, style: style));
  }

  Widget buildTextField({final bool isTheme = false}) {
    return BlocProvider(
      create: (final context) => getIt<CommentListBloc>(),
      child: SizedBox(
        child: Theme(
          data: Theme.of(context).copyWith(
            inputDecorationTheme: InputDecorationTheme(
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 14.0,
                vertical: 5.0,
              ),
              hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).hintColor.withValues(alpha: .9),
              ),
              filled: !isTheme,
              fillColor: Colors.white,
            ),
          ),
          child: BlocConsumer<CommentListBloc, CommentListState>(
            listener: (final context, final state) {
              if (state.status == CommentListStatus.loadingTag) {
                stateMentionList.value = false;
              }
              if (state.status == CommentListStatus.getTagsuccess) {
                dataTagList = Utils.getData(state.data);
                final listSafe = dataTagList?.items
                    .where(
                      (final e) =>
                          e?.username !=
                          EZCache.shared.getUserProfile()?.employeeId,
                    )
                    .map((final e) => e ?? const TagListItems())
                    .toList();
                stateMentionList.value = true;
                mentionList.setValue(listSafe);
                mentionTemp.addAll(listSafe ?? []);
              }
            },
            builder: (final context, final state) {
              contexTemp = context;
              return CompositedTransformTarget(
                link: _layerLink,
                child: QuillEditor.basic(
                  focusNode: storyFocusNode,
                  config: QuillEditorConfig(
                    textSpanBuilder:
                        (
                          final context,
                          final node,
                          final nodeOffset,
                          final text,
                          final style,
                          final recognizer,
                        ) {
                          final List<InlineSpan> textSpanChildren =
                              <InlineSpan>[];
                          text.splitMapJoin(
                            combinedPattern,
                            onMatch: (final Match match) {
                              final String? textPart = match.group(0);
                              if (textPart == null) {
                                return '';
                              }
                              if (textPart.contains('@')) {
                                final TextPartStyleDefinition? styleDefinition =
                                    styles.getStyleOfTextPart(textPart, text);

                                if (styleDefinition == null) {
                                  return '';
                                }
                                _addTextSpan(
                                  textSpanChildren,
                                  textPart,
                                  TextStyle(
                                    color: Colors.blue,
                                    backgroundColor: Colors.blue.withValues(
                                      alpha: .1,
                                    ),
                                  ),
                                );
                                return '';
                              }
                              if (textPart.contains('#')) {
                                _addTextSpan(
                                  textSpanChildren,
                                  textPart,
                                  TextStyle(
                                    color: Colors.blue,
                                    backgroundColor: Colors.blue.withValues(
                                      alpha: .1,
                                    ),
                                  ),
                                );
                                return '';
                              }

                              return '';
                            },
                            onNonMatch: (final String text) {
                              _addTextSpan(
                                textSpanChildren,
                                text,
                                TextStyle(
                                  fontWeight: node.style.containsKey('bold')
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  decoration:
                                      node.style.containsKey('underline')
                                      ? TextDecoration.underline
                                      : TextDecoration.none,
                                  fontStyle: node.style.containsKey('italic')
                                      ? FontStyle.italic
                                      : FontStyle.normal,
                                ),
                              );
                              return '';
                            },
                          );
                          return TextSpan(children: textSpanChildren);
                        },

                    textSelectionControls: NoHandlesTextSelectionControls(
                      editorKey,
                    ),
                    textSelectionThemeData: TextSelectionThemeData(
                      cursorColor: Theme.of(context).primaryColor,
                      selectionColor: Theme.of(
                        context,
                      ).primaryColor.withValues(alpha: .5),
                    ),
                    editorKey: editorKey,
                    padding: const EdgeInsets.only(
                      left: 10,
                      right: 10,
                      top: 8,
                      bottom: 8,
                    ),
                    placeholder: context.l10n.hintContentStory,
                    customStyles: const DefaultStyles(
                      placeHolder: DefaultTextBlockStyle(
                        TextStyle(fontSize: 16, color: Colors.black45),
                        HorizontalSpacing(8, 8),
                        VerticalSpacing.zero,
                        VerticalSpacing.zero,
                        BoxDecoration(),
                      ),
                    ),
                    contextMenuBuilder:
                        (final context, final editableTextState) {
                          final Offset anchorAbove = editableTextState
                              .contextMenuAnchors
                              .primaryAnchor;
                          final Offset anchorBelow =
                              editableTextState
                                  .contextMenuAnchors
                                  .secondaryAnchor ??
                              Offset.zero;
                          return CupertinoTextSelectionToolbar(
                            anchorAbove: anchorAbove,
                            anchorBelow: anchorBelow,
                            children: [
                              Container(
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    bottomLeft: Radius.circular(8),
                                  ),
                                  color: Colors.white,
                                ),
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    editableTextState.copySelection(
                                      SelectionChangedCause.toolbar,
                                    );
                                    editableTextState.hideToolbar();
                                  },
                                  child: Text(context.l10n.copy),
                                ),
                              ),
                              Container(
                                color: Colors.white,
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    editableTextState
                                        .pasteText(
                                          SelectionChangedCause.toolbar,
                                        )
                                        .whenComplete(() {
                                          final content = _controller
                                              .plainTextEditingValue
                                              .text;
                                          if (Utils.isHtml(content)) {
                                            final delta = HtmlToDelta().convert(
                                              content,
                                            );
                                            final document = Document.fromDelta(
                                              delta,
                                            );
                                            _controller.document = document;
                                            _controller.moveCursorToEnd();
                                          }
                                        });
                                    editableTextState.hideToolbar();
                                  },
                                  child: Text(context.l10n.paste),
                                ),
                              ),
                              Container(
                                color: Colors.white,
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    editableTextState.selectAll(
                                      SelectionChangedCause.toolbar,
                                    );
                                    Timer(
                                      const Duration(milliseconds: 300),
                                      () {
                                        editorKey.currentState?.hideToolbar();
                                        editorKey.currentState?.showToolbar();
                                      },
                                    );
                                  },
                                  child: Text(context.l10n.selectionAll),
                                ),
                              ),
                              Container(
                                color: Colors.white,
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    final currentStyle = editableTextState
                                        .controller
                                        .getSelectionStyle();
                                    final isCurrentlyBold = currentStyle
                                        .attributes
                                        .containsKey('bold');
                                    if (isCurrentlyBold) {
                                      editableTextState.controller
                                          .formatSelection(
                                            Attribute.clone(
                                              Attribute.bold,
                                              null,
                                            ),
                                          );
                                    } else {
                                      editableTextState.controller
                                          .formatSelection(Attribute.bold);
                                    }
                                    editableTextState.hideToolbar();
                                  },
                                  child: Text(
                                    context.l10n.bold,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                color: Colors.white,
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    final currentStyle = editableTextState
                                        .controller
                                        .getSelectionStyle();
                                    final isItalic = currentStyle.attributes
                                        .containsKey('italic');

                                    if (isItalic) {
                                      editableTextState.controller
                                          .formatSelection(
                                            Attribute.clone(
                                              Attribute.italic,
                                              null,
                                            ),
                                          );
                                    } else {
                                      editableTextState.controller
                                          .formatSelection(Attribute.italic);
                                    }
                                    editableTextState.hideToolbar();
                                  },
                                  child: Text(
                                    context.l10n.italic,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.black,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    bottomLeft: Radius.circular(8),
                                  ),
                                  color: Colors.white,
                                ),
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    final currentStyle = editableTextState
                                        .controller
                                        .getSelectionStyle();
                                    final isItalic = currentStyle.attributes
                                        .containsKey('underline');

                                    if (isItalic) {
                                      editableTextState.controller
                                          .formatSelection(
                                            Attribute.clone(
                                              Attribute.underline,
                                              null,
                                            ),
                                          );
                                    } else {
                                      editableTextState.controller
                                          .formatSelection(Attribute.underline);
                                    }
                                    editableTextState.hideToolbar();
                                  },
                                  child: Text(
                                    context.l10n.underline,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.black,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                  ),
                  controller: _controller,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isOverlayVisible = false;
    });
  }

  void _showOverlay(final bool isMention) {
    final renderBox = context.findRenderObject() as RenderBox?;
    final size = renderBox?.size;

    _overlayEntry = OverlayEntry(
      builder: (final context) => ValueListenableBuilder(
        valueListenable: oDy,
        builder: (final context, final vDy, final child) {
          return StoryWriteOverlayBody(
            width: size?.width,
            layerLink: _layerLink,
            hideOverlay: () async {
              _hideOverlay();
              return true;
            },
            controller: _controller,
            query: query,
            mentionList: mentionList,
            stateList: stateMentionList,
            onCancel: _hideOverlay,
            start: _start,
            last: _last,
            dy: 30 + vDy + (vDy > 16 ? ((vDy / 32) * 16) : 0),
          );
        },
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOverlayVisible = true;
    });
  }

  void replaceCharAtIndex(final int index, final String newChar) {
    final String text = _controller.plainTextEditingValue.text;
    if (index >= 0 && index < text.length) {
      final String newText =
          text.substring(0, index) + newChar + text.substring(index + 1);
      _controller.clear();

      _controller.document.insert(0, newText);
      _controller.moveCursorToPosition(
        (text.substring(0, index) + newChar).length + 1,
      );
    }
  }

  void _onChanged(final BuildContext context) {
    final listText = _controller.plainTextEditingValue.text.trim().split('');
    final value = _controller.plainTextEditingValue.text;
    content.value = value;
    final cursorPosition = _controller.selection.baseOffset;

    final triggervalue = value.substring(0, cursorPosition);

    var match = false;
    var isMention = false;
    if (listText.isEmpty) {
      _hideOverlay();
    }
    if (RegExp(Strings.regExpSingleSymbol).hasMatch(triggervalue)) {
      isMention = true;
      match = true;
    }
    if (match) {
      final startindex = triggervalue.lastIndexOf(isMention ? '@' : '') + 1;
      final lastindex = value.indexOf(' ', cursorPosition);

      _start = triggervalue.substring(0, startindex);
      _query = value.substring(startindex, lastindex > 0 ? lastindex : null);
      _last = lastindex > 0 ? value.substring(lastindex).trimLeft() : '';

      if (_isOverlayVisible) {
        _overlayEntry?.remove();
      }
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }
      _debounce = Timer(const Duration(milliseconds: 200), () {
        query.value = _query.replaceAll('_', ' ');
        context.read<CommentListBloc>().add(
          GetTagCommentEvent(
            TagListRequestParams(
              tag: _query.trim().isEmpty
                  ? Strings.space
                  : _query.replaceAll('_', ' ').trim(),
            ),
          ),
        );
      });

      _showOverlay(isMention);
    } else if (!match && _isOverlayVisible) {
      if (listText[cursorPosition - 1] == ' ' &&
          !blockReplace &&
          triggervalue.contains('@')) {
        replaceCharAtIndex(cursorPosition - 1, '_');
      }
      _hideOverlay();
    }
  }

  Widget _buildHeader() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ColoredBox(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: IconButton(
                onPressed: () {
                  if (_overlayEntry != null) {
                    _overlayEntry?.remove();
                    _overlayEntry = null;
                  }
                  context.router.popForced();
                },
                icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
              ),
            ),
            GestureDetector(
              onTap: () async {},
              child: Align(
                child: Text(
                  context.l10n.titleNewPost,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: StoryWriteBody.isCompressDone,
              builder: (final context, final vCompress, final child) {
                return vCompress ?? false || vCompress == null
                    ? ValueListenableBuilder(
                        valueListenable: content,
                        builder: (final context, final vContent, final child) {
                          return ValueListenableBuilder(
                            valueListenable: StoryWriteBody.imageSelected,
                            builder:
                                (
                                  final context,
                                  final vImageSelected,
                                  final child,
                                ) {
                                  return ValueListenableBuilder(
                                    valueListenable: StoryWriteBody.xfile,
                                    builder:
                                        (final context, final vF, final chilf) {
                                          return Align(
                                            alignment: Alignment.centerRight,
                                            child: TextButton(
                                              onPressed:
                                                  (vImageSelected.isEmpty &&
                                                      vContent
                                                          .trimRight()
                                                          .isEmpty &&
                                                      vF.isEmpty)
                                                  ? null
                                                  : () async {
                                                      StoryWriteBody
                                                          .imageSelected
                                                          .addLastList(
                                                            StoryWriteBody
                                                                .xfile
                                                                .value
                                                                .map(
                                                                  (
                                                                    final xfile,
                                                                  ) => File(
                                                                    xfile.path,
                                                                  ),
                                                                )
                                                                .toList(),
                                                          );
                                                      StoryBody.countFile =
                                                          StoryWriteBody
                                                              .imageSelected
                                                              .value
                                                              .length;
                                                      storyInfo
                                                          .value = storyInfo
                                                          .value
                                                          .copyWith(
                                                            tags: tagList.value
                                                                .map(
                                                                  (final e) =>
                                                                      e.id ??
                                                                      '',
                                                                )
                                                                .toList(),
                                                            poll: storyInfo
                                                                .value
                                                                .poll,
                                                          );

                                                      handleMention();
                                                      handleMediaPollTheme();
                                                      handleHtml();

                                                      _onUploadFile(context);
                                                      StoryWriteBody
                                                          .imageSelected
                                                          .setValue([]);
                                                      context.router
                                                          .popForced();
                                                    },
                                              child: Text(
                                                context.l10n.postStory,
                                              ),
                                            ),
                                          );
                                        },
                                  );
                                },
                          );
                        },
                      )
                    : Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {},
                          child: const CircularProgressIndicator.adaptive(),
                        ),
                      );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _onUploadFile(final BuildContext context) {
    context.read<StoryWriteBloc>().add(
      SocialUploadFileStarted(
        SocialUploadFileRequestParams(
          files: StoryWriteBody.imageSelected.value,
          storyWriteInfoRequestParams: storyInfo.value,
        ),
      ),
    );
  }

  void handleMediaPollTheme() {
    if (StoryWriteBody.imageSelected.isNotEmpty()) {
      storyInfo.value = storyInfo.value.copyWith(theme: []);
    } else if (storyInfo.value.poll != null) {
      storyInfo.value = storyInfo.value.copyWith(
        theme: [],
        poll: storyInfo.value.poll,
      );
    }
  }

  Widget _buildOptionStoryHeader(final String title, {final Widget? action}) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ColoredBox(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: GestureDetector(
                onTap: () {
                  context.router.popForced();
                },
                child: IconButton(
                  onPressed: () {
                    context.router.popForced();
                  },
                  icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
                ),
              ),
            ),
            Align(child: Text(title)),
            if (action != null) action,
          ],
        ),
      ),
    );
  }

  Widget _buildOptionFoot() {
    return SizedBox(
      width: double.infinity,
      child: ColoredBox(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              GestureDetector(
                onTap: () async {
                  context.router
                      .push(
                        ProfileGalleryRoute(
                          isFromProfile: FromPage.createStory,
                          isMultiple: true,
                        ),
                      )
                      .whenComplete(
                        () => isOptionContent.value = OptionContent.none,
                      );
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenImage,
                    size: const ImageSize.square(32),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  final FilePickerResult? result = await FilePicker.platform
                      .pickFiles(allowMultiple: true);
                  StoryWriteBody.xfile.addLastList(result?.xFiles);
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icFile,
                    size: const ImageSize.square(28),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  final safeTop = MediaQueryData.fromView(
                    ui.PlatformDispatcher.instance.implicitView ??
                        View.of(context),
                  ).padding.top;
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (final _) => Scaffold(
                      body: BlocProvider(
                        create: (final context) =>
                            getIt<CreateChatGroupBloc>()..add(
                              CreateChatGroupUserLoaded(
                                const CreateChatGroupUserLoadRequestParams(
                                  search: Strings.empty,
                                  page: 1,
                                ),
                              ),
                            ),
                        child: _buildTagBody(safeTop),
                      ),
                    ),
                  ).whenComplete(
                    () => isOptionContent.value = OptionContent.none,
                  );
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icTagBlue,
                    size: const ImageSize.square(32),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  final safeTop = MediaQueryData.fromView(
                    ui.PlatformDispatcher.instance.implicitView ??
                        View.of(context),
                  ).padding.top;
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (final _) => Scaffold(
                      body: SizedBox.expand(
                        child: ColoredBox(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          child: Padding(
                            padding: EdgeInsets.only(top: safeTop),
                            child: Column(
                              children: [
                                _buildOptionStoryHeader(
                                  context.l10n.titleEmoji,
                                ),
                                const SizedBox(height: 8),
                                ValueListenableBuilder(
                                  valueListenable: storyInfo,
                                  builder:
                                      (
                                        final builder,
                                        final vStoryInfo,
                                        final child,
                                      ) {
                                        return vStoryInfo
                                                    .emoji
                                                    ?.text
                                                    ?.isNotEmpty ??
                                                false
                                            ? ListTile(
                                                trailing: TextButton(
                                                  onPressed: () {
                                                    storyInfo.value = storyInfo
                                                        .value
                                                        .copyWith(
                                                          emoji: EmojiParams(
                                                            text: Strings.empty,
                                                            icon: Strings.empty,
                                                          ),
                                                        );
                                                  },
                                                  child: Text(
                                                    context.l10n.unSelect,
                                                  ),
                                                ),
                                                title: Text(
                                                  '${context.l10n.feeling} '
                                                  '${vStoryInfo.emoji?.icon} '
                                                  '${vStoryInfo.emoji?.text}',
                                                  style: Theme.of(
                                                    context,
                                                  ).textTheme.titleSmall,
                                                ),
                                              )
                                            : const SizedBox();
                                      },
                                ),
                                _buildEmojiList(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ).whenComplete(
                    () => isOptionContent.value = OptionContent.none,
                  );
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icEmojiYellow,
                    size: const ImageSize.square(32),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  final safeTop = MediaQueryData.fromView(
                    ui.PlatformDispatcher.instance.implicitView ??
                        View.of(context),
                  ).padding.top;
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (final _) => Scaffold(
                      body: SizedBox.expand(
                        child: ColoredBox(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          child: Padding(
                            padding: EdgeInsets.only(top: safeTop),
                            child: Column(
                              children: [
                                _buildOptionStoryHeader(
                                  context.l10n.shareAddress,
                                ),
                                _buildLocationSearch(
                                  context,
                                  context.l10n.hintFindAddress,
                                ),
                                ValueListenableBuilder(
                                  valueListenable: storyInfo,
                                  builder:
                                      (
                                        final builder,
                                        final vStoryInfo,
                                        final child,
                                      ) {
                                        final checkLocation =
                                            vStoryInfo
                                                .location
                                                ?.name
                                                ?.isEmpty ??
                                            false;
                                        return AnimatedSize(
                                          duration: const Duration(
                                            milliseconds: 300,
                                          ),
                                          child: checkLocation
                                              ? const SizedBox()
                                              : _buildSelectedAddress(
                                                  vStoryInfo.location?.name,
                                                ),
                                        );
                                      },
                                ),
                                const SizedBox(height: 8),
                                ValueListenableBuilder(
                                  valueListenable: isSearchLocation,
                                  builder:
                                      (
                                        final context,
                                        final vIsSearch,
                                        final child,
                                      ) {
                                        return vIsSearch
                                            ? _buildAddressSearch()
                                            : _buildLocationCurrent();
                                      },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ).whenComplete(
                    () => isOptionContent.value = OptionContent.none,
                  );
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icLocationRed,
                    size: const ImageSize.square(32),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  showBackgroundColor();
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icTextBlue,
                    size: const ImageSize.square(32),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  ImagePicker().pickImage(source: ImageSource.camera).then((
                    final value,
                  ) {
                    if (value != null) {
                      return StoryWriteBody.imageSelected.value = [
                        File(value.path),
                      ];
                    }
                  });
                },
                child: EZResources.image(
                  ImageParams(
                    name: AppIcons.icCameraBlue,
                    size: const ImageSize.square(32),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  final safeTop = MediaQueryData.fromView(
                    ui.PlatformDispatcher.instance.implicitView ??
                        View.of(context),
                  ).padding.top;
                  if (storyInfo.value.poll == null) {
                    scaleHeight.value = 105;
                    poll = PollRequestParams(
                      title: Strings.empty,
                      options: [
                        PollOptions(text: Strings.empty, count: 0),
                        PollOptions(text: Strings.empty, count: 0),
                      ],
                    );
                  }
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (final _) => Scaffold(
                      body: SizedBox.expand(
                        child: ColoredBox(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          child: Padding(
                            padding: EdgeInsets.only(top: safeTop),
                            child: Column(
                              children: [
                                _buildOptionStoryHeader(
                                  context.l10n.createVote,
                                  action: Align(
                                    alignment: Alignment.centerRight,
                                    child: TextButton(
                                      onPressed: () {
                                        final fList = poll?.options
                                            .where(
                                              (final e) =>
                                                  (e.text ?? '').isNotEmpty,
                                            )
                                            .toList();
                                        if (fList?.isNotEmpty ?? false) {
                                          final pollParam = poll?.copyWith(
                                            options: fList,
                                          );

                                          storyInfo.value = storyInfo.value
                                              .copyWith(poll: pollParam);
                                        }
                                        context.router.popForced();
                                      },
                                      child: Text(context.l10n.create),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                _buildPoll(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ).then((final val) {}).whenComplete(() {
                    isOptionContent.value = OptionContent.none;
                  });
                },
                child: const Icon(Icons.poll, size: 32),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPoll() {
    return Expanded(
      child: SizedBox(
        child: StatefulBuilder(
          builder: (final context, final rebuild) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: AccountField(
                      isOnlyReady: false,
                      filled: false,
                      controller: titlePollController,
                      label: context.l10n.titlePoll,
                      hintText: context.l10n.inputTitlePoll,
                      onChanged: (final val) {
                        poll = poll?.copyWith(title: val);
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: const Color(0xffDEE3ED)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ReorderableListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: (poll ?? PollRequestParams(options: []))
                            .options
                            .length,
                        itemBuilder: (final _, final i) {
                          final item = poll?.options[i];
                          return _buildOptionItem(i, item, rebuild);
                        },
                        onReorder: (final int oldIndex, int newIndex) {
                          if (oldIndex < newIndex) {
                            newIndex -= 1;
                          }
                          poll = poll?.copyWith(
                            options: [
                              ...poll!.options..swap(oldIndex, newIndex),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                  if ((poll?.options.length ?? 0) <= 7)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: GestureDetector(
                        onTap: () async {
                          if ((poll?.options.length ?? 0) <= 7) {
                            poll = poll?.copyWith(
                              options: [
                                ...poll?.options ?? [],
                                PollOptions(text: '', count: 0),
                              ],
                            );
                            rebuild(() {});
                          }
                        },
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: const Color(0xffDEE3ED)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SizedBox(
                            width: double.maxFinite,
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Text(context.l10n.inputCreateOption),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  const SizedBox(height: 8),
                  if ((poll?.options.length ?? 0) <= 7)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Row(
                        children: [
                          Text(
                            context.l10n.max8Option,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Theme.of(context).hintColor),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: const Color(0xffDEE3ED)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          DecoratedBox(
                            decoration: const BoxDecoration(
                              border: Border(
                                bottom: BorderSide(color: Color(0xffDEE3ED)),
                              ),
                            ),
                            child: SizedBox(
                              width: double.maxFinite,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 14,
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(context.l10n.hiddenVoted),
                                    Switch(
                                      value: poll?.isAnonymous ?? false,
                                      onChanged: (final value) {
                                        poll = poll?.copyWith(
                                          isAnonymous: value,
                                        );
                                        rebuild(() {});
                                      },
                                      activeColor: Theme.of(
                                        context,
                                      ).primaryColor,
                                      activeTrackColor: Theme.of(
                                        context,
                                      ).primaryColor.withValues(alpha: .1),
                                      inactiveTrackColor: Theme.of(
                                        context,
                                      ).scaffoldBackgroundColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          DecoratedBox(
                            decoration: const BoxDecoration(
                              border: Border(
                                bottom: BorderSide(color: Color(0xffDEE3ED)),
                              ),
                            ),
                            child: SizedBox(
                              width: double.maxFinite,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 14,
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(context.l10n.multipleVoted),
                                    Switch(
                                      value: poll?.isMultiple ?? false,
                                      onChanged: (final value) {
                                        poll = poll?.copyWith(
                                          isMultiple: value,
                                        );
                                        rebuild(() {});
                                      },
                                      activeColor: Theme.of(
                                        context,
                                      ).primaryColor,
                                      activeTrackColor: Theme.of(
                                        context,
                                      ).primaryColor.withValues(alpha: .1),
                                      inactiveTrackColor: Theme.of(
                                        context,
                                      ).scaffoldBackgroundColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOptionItem(
    final int i,
    final PollOptions? item,
    final void Function(void Function()) rebuild,
  ) {
    return DecoratedBox(
      key: ObjectKey(item),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: const Color(0xffDEE3ED)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SizedBox(
              child: Theme(
                data: Theme.of(context).copyWith(
                  inputDecorationTheme: const InputDecorationTheme(
                    contentPadding: EdgeInsets.all(14.0),
                  ),
                ),
                child: TextFormField(
                  initialValue: item?.text,
                  onChanged: (final val) {
                    final currentItem = item;
                    final currentList = poll?.options;
                    currentList?[i] =
                        (currentItem ?? PollOptions(text: '', count: 0))
                            .copyWith(text: val, count: 0);
                    poll = poll?.copyWith(options: currentList);
                  },
                  decoration: InputDecoration(
                    hintText: '${context.l10n.option} ${i + 1}',
                    hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).hintColor,
                    ),
                    suffixIcon: IconButton(
                      onPressed: () {
                        if ((poll?.options.length ?? 0) > 2) {
                          poll = poll?.copyWith(
                            options: [...poll!.options..removeAt(i)],
                          );
                          scaleHeight.value -= 57;
                          rebuild(() {});
                        }
                      },
                      icon: EZResources.image(
                        ImageParams(
                          name: AppIcons.icCancel,
                          size: const ImageSize.square(32),
                        ),
                      ),
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: EZResources.image(
              ImageParams(
                name: AppIcons.icDotSix,
                size: const ImageSize.square(32),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmojiList() {
    return Expanded(
      child: BlocProvider(
        create: (final context) =>
            getIt<StoryWriteBloc>()
              ..add(const StoryWriteEmojiListStarted(EmojiListRequestParams())),
        child: BlocConsumer<StoryWriteBloc, StoryWriteState>(
          listener: (final context, final state) {
            if (state.status == StoryWriteStatus.emojiSuccess) {
              dataEmojiList = Utils.getData(state.data);
            }
            if (state.status == StoryWriteStatus.failure) {
              unawaited(
                ApiErrorDialog.show(
                  ApiErrorParams(context, Utils.getData(state.data)),
                ),
              );
            }
          },
          builder: (final context, final state) {
            if (state.status == StoryWriteStatus.loading) {
              return const Center(child: CircularProgressIndicator());
            }
            if (state.status == StoryWriteStatus.emojiSuccess) {
              return BlocBuilder<FontsBloc, FontsState>(
                builder: (final context, final state) {
                  return GridView.builder(
                    itemCount: dataEmojiList?.items.length,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 36 / (state.textScale == 1.4 ? 12 : 9),
                    ),
                    itemBuilder: (final context, final index) {
                      final item = dataEmojiList?.items[index];
                      return GestureDetector(
                        onTap: () {
                          storyInfo.value = storyInfo.value.copyWith(
                            emoji: EmojiParams(
                              text: item?.text,
                              icon: item?.icon,
                            ),
                          );
                          context.router.popForced();
                        },
                        child: ListTile(
                          leading: Text(
                            item?.icon ?? '',
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                          title: Text(
                            item?.text ?? '',
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                        ),
                      );
                    },
                  );
                },
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }

  Widget _buildStaffSearch(final BuildContext context, final String hintText) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: AccountField(
        filled: false,
        controller: staffSearch,
        label: '',
        hintText: hintText,
        isOnlyReady: false,
        maxLines: 1,
        iconRight: Padding(
          padding: const EdgeInsets.all(8.0),
          child: EZResources.image(ImageParams(name: AppIcons.icSearch)),
        ),
        bottomPadding: 0,
        onChanged: (final value) {
          _onSearchStaffChanged(context, value);
        },
      ),
    );
  }

  Widget _buildLocationSearch(
    final BuildContext context,
    final String hintText,
  ) {
    return BlocProvider(
      create: (final context) => getIt<StoryWriteBloc>(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocConsumer<StoryWriteBloc, StoryWriteState>(
          listener: (final context, final state) {
            if (state.status == StoryWriteStatus.addressSeachSuccess) {
              dataAddressSearch = Utils.getData(state.data);
              final listSafety = dataAddressSearch?.predictions
                  .map(
                    (final e) =>
                        e ??
                        AddressSearchLocationGooglePredictions(
                          matchedSubstrings: [],
                          terms: [],
                          types: [],
                        ),
                  )
                  .toList();
              locationSearchList.setValue(listSafety);
            }
          },
          builder: (final context, final state) {
            return AccountField(
              filled: false,
              controller: locationSearch,
              label: '',
              hintText: hintText,
              isOnlyReady: false,
              maxLines: 1,
              iconRight: Padding(
                padding: const EdgeInsets.all(8.0),
                child: EZResources.image(ImageParams(name: AppIcons.icSearch)),
              ),
              bottomPadding: 0,
              onChanged: (final value) {
                _onSearchLocationChanged(context, value);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildTagList(final List<Tags>? vTagList) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        height: 120,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.selectTag,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Expanded(
              child: ListView.separated(
                itemCount: vTagList?.length ?? 0,
                separatorBuilder: (final context, final index) =>
                    const SizedBox(width: 16),
                scrollDirection: Axis.horizontal,
                itemBuilder: (final itemBuilder, final i) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Badge(
                        backgroundColor: Colors.white,
                        label: SizedBox(
                          width: 10,
                          height: 40,
                          child: InkWell(
                            onTap: () {
                              tagList.removeIndex(i);
                            },
                            child: EZResources.image(
                              ImageParams(
                                name: AppIcons.icCancel,
                                size: const ImageSize.square(10),
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                        alignment: Alignment.topRight,
                        offset: const Offset(5, 5),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24.0),
                          child: SizedBox(
                            width: 40,
                            height: 40,
                            child: ColoredBox(
                              color: Colors.grey,
                              child: EzCachedNetworkImage(
                                imageUrl: vTagList?[i].avatar ?? Strings.empty,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                      ),
                      ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 110),
                        child: Text(
                          vTagList?[i].name ?? '',
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedAddress(final String? name) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            context.l10n.selectAddress,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: EZResources.image(ImageParams(name: AppIcons.icMapMarker)),
          title: Text(name ?? ''),
          trailing: TextButton(
            onPressed: () {
              storyInfo.value = storyInfo.value.copyWith(
                location: Location(
                  long: Strings.empty,
                  lat: Strings.empty,
                  address: Strings.empty,
                  name: Strings.empty,
                ),
              );
            },
            child: Text(context.l10n.unSelect),
          ),
        ),
      ],
    );
  }

  Widget _buildStaffList() {
    return Expanded(
      child: ColoredBox(
        color: Theme.of(context).colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: ValueListenableBuilder(
            valueListenable: tagList,
            builder: (final context, final vTags, final child) {
              return ListView.builder(
                itemCount: data?.items.length,
                itemBuilder: (final context, final index) {
                  final items = data?.items[index];
                  return ListTile(
                    onTap: () {
                      final listCurrent = tagList.value;
                      final check = tagList.value.where(
                        (final e) => e.id == (items?.username ?? ''),
                      );
                      if (check.isEmpty) {
                        listCurrent.add(
                          Tags(
                            id: items?.username,
                            name: items?.name,
                            avatar: items?.avatar,
                          ),
                        );
                      } else {
                        listCurrent.removeWhere(
                          (final e) => e.id == (items?.username ?? ''),
                        );
                      }
                      tagList.setValue(listCurrent);
                    },
                    leading: ClipRRect(
                      borderRadius: BorderRadius.circular(45.0),
                      child: SizedBox(
                        width: 40,
                        height: 40,
                        child: ColoredBox(
                          color: Colors.grey,
                          child: EzCachedNetworkImage(
                            imageUrl: items?.avatar ?? Strings.empty,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    title: Text(items?.name ?? ''),
                    subtitle: Text.rich(
                      TextSpan(
                        text: items?.username ?? Strings.empty,
                        children: [
                          if (items?.departmentName != null &&
                              (items?.departmentName?.isNotEmpty ?? false))
                            TextSpan(
                              text:
                                  '${Strings.hyphenSpace}'
                                  '${items?.departmentName ?? Strings.empty}',
                            ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: Container(
                      width: 20,
                      height: 20,
                      decoration:
                          vTags
                              .map((final e) => e.id)
                              .toList()
                              .contains(items?.username)
                          ? ShapeDecoration(
                              shape: const CircleBorder(),
                              color: Theme.of(context).primaryColor,
                            )
                          : ShapeDecoration(
                              shape: CircleBorder(
                                side: BorderSide(
                                  color: Theme.of(context).colorScheme.surface,
                                ),
                              ),
                            ),
                      child:
                          vTags
                              .map((final e) => e.id)
                              .toList()
                              .contains(items?.username)
                          ? Center(
                              child: Icon(
                                Icons.check,
                                size: 14,
                                color: Theme.of(context).colorScheme.surface,
                              ),
                            )
                          : null,
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLocationList(final String lat, final String lng) {
    return BlocConsumer<StoryWriteBloc, StoryWriteState>(
      listener: (final context, final state) {
        if (state.status == StoryWriteStatus.addressCurrentSuccess) {
          dataAddress = Utils.getData(state.data);
        }
        if (state.status == StoryWriteStatus.addressNearSuccess) {
          dataNearLocation = Utils.getData(state.data);
          final safetyList = dataNearLocation?.results
              .map(
                (final e) =>
                    e ??
                    AddressNearLocationGoogleResults(photos: [], types: []),
              )
              .toList();
          locationNearList.setValue(safetyList);
        }
        if (state.status == StoryWriteStatus.failure) {
          unawaited(
            ApiErrorDialog.show(
              ApiErrorParams(context, Utils.getData(state.data)),
            ),
          );
        }
      },
      builder: (final context, final state) {
        if (state.status == StoryWriteStatus.addressCurrentSuccess ||
            state.status == StoryWriteStatus.addressNearSuccess) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  context.l10n.currentAddress,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              ListTile(
                onTap: () {
                  final address = Location(
                    long: lng,
                    lat: lat,
                    address: dataAddress?.results.firstOrNull?.formattedAddress,
                    name: dataAddress?.results.firstOrNull?.formattedAddress,
                  );
                  storyInfo.value = storyInfo.value.copyWith(location: address);
                  context.router.popForced();
                },
                leading: EZResources.image(
                  ImageParams(name: AppIcons.icMapMarker),
                ),
                title: Text(
                  dataAddress?.results.firstOrNull?.formattedAddress ?? '',
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  context.l10n.nearAddress,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              ValueListenableBuilder(
                valueListenable: locationNearList,
                builder: (final context, final vLocationNear, final child) {
                  return Expanded(
                    child: SizedBox(
                      child: ListView.builder(
                        itemCount: vLocationNear.length,
                        itemBuilder: (final cxt, final i) {
                          final item = vLocationNear[i];
                          return ListTile(
                            onTap: () {
                              final address = Location(
                                long: lng,
                                lat: lat,
                                address: item.vicinity,
                                name: item.name,
                              );
                              storyInfo.value = storyInfo.value.copyWith(
                                location: address,
                              );
                              context.router.popForced();
                            },
                            leading: EZResources.image(
                              ImageParams(name: AppIcons.icMapMarker),
                            ),
                            title: Text(
                              item.name ?? '',
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                            subtitle: Text(
                              item.vicinity ?? '',
                              style: TextStyle(
                                color: Theme.of(context).hintColor,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ],
          );
        }
        if (state.status == StoryWriteStatus.loadingAddressCurrent) {
          return const SizedBox(child: Center(child: LoadingWidget()));
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildLocationCurrent() {
    return Expanded(
      child: ValueListenableBuilder(
        valueListenable: isValuePermission,
        builder: (final context, final vCheckPermission, final child) {
          return vCheckPermission
              ? ValueListenableBuilder(
                  valueListenable: isGetLatLngSuccess,
                  builder:
                      (final context, final vIsGetLatLngSuccess, final child) {
                        return vIsGetLatLngSuccess
                            ? _onStoryWriteProvider()
                            : const SizedBox(
                                child: Center(
                                  child: CircularProgressIndicator(),
                                ),
                              );
                      },
                )
              : SizedBox(
                  child: Center(
                    child: TextButton(
                      onPressed: () async {
                        EzLocation.requestPermission().then((final val) {
                          if (val.name == PermissionStatus.granted.name) {
                            EzLocation.getLocation().then((final vLocation) {
                              lat = vLocation?.latitude;
                              lng = vLocation?.longitude;
                              locationString = '$lat,$lng';
                              isGetLatLngSuccess.value = true;
                              isValuePermission.value = true;
                            });
                          }
                        });
                      },
                      child: Text(context.l10n.getLocationCurrent),
                    ),
                  ),
                );
        },
      ),
    );
  }

  BlocProvider<StoryWriteBloc> _onStoryWriteProvider() {
    return BlocProvider(
      create: (final context) => getIt<StoryWriteBloc>()
        ..add(
          GetAddressLocationStoryWriteEvent(
            AddressLocationGoogleRequestParams(latlng: locationString),
          ),
        )
        ..add(
          GetAddressNearLocationStoryWriteEvent(
            AddressNearLocationGoogleRequestParams(location: locationString),
          ),
        ),
      child: _buildLocationList('$lat', '$lng'),
    );
  }

  Widget _buildTagsUI(
    final List<Tags> vTagList,
    final StoryWriteInfoRequestParams vStoryInfo,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: SizedBox(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(45.0),
              child: SizedBox(
                width: 40,
                height: 40,
                child: ColoredBox(
                  color: Colors.grey,
                  child: EzCachedNetworkImage(
                    imageUrl:
                        EZCache.shared.getUserProfile()?.avatar ??
                        Strings.empty,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.sizeOf(context).width - 90,
              ),
              child: Text.rich(
                TextSpan(
                  text: EZCache.shared.getUserProfile()?.name,
                  style: Theme.of(context).textTheme.titleSmall,
                  children: [
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: SizedBox(
                          width: 20,
                          height: 1,
                          child: ColoredBox(color: Theme.of(context).hintColor),
                        ),
                      ),
                    ),
                    if (vStoryInfo.emoji?.text?.isNotEmpty ?? false)
                      _buildEmojiOne(vStoryInfo),
                    if (vTagList.isNotEmpty) _buildTagOne(vTagList),
                    if (vStoryInfo.location?.name?.isNotEmpty ?? false)
                      _buildLocationOne(vStoryInfo),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressSearch() {
    return Expanded(
      child: ValueListenableBuilder(
        valueListenable: locationSearchList,
        builder: (final context, final vSearchList, final child) {
          return SizedBox(
            child: ColoredBox(
              color: Theme.of(context).colorScheme.surface,
              child: ListView.builder(
                itemCount: vSearchList.length,
                itemBuilder: (final cxt, final i) {
                  final item = vSearchList[i];
                  return ListTile(
                    onTap: () {
                      final address = Location(
                        long: '',
                        lat: '',
                        address: item.description,
                        name: item.structuredFormatting?.mainText,
                      );
                      storyInfo.value = storyInfo.value.copyWith(
                        location: address,
                      );
                      context.router.popForced();
                    },
                    leading: EZResources.image(
                      ImageParams(name: AppIcons.icMapMarker),
                    ),
                    title: Text(
                      item.structuredFormatting?.mainText ?? '',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    subtitle: Text(
                      item.description ?? '',
                      style: TextStyle(color: Theme.of(context).hintColor),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  TextSpan _buildTagOne(final List<Tags> vTagList) {
    return TextSpan(
      text: ' ${context.l10n.isTags} ',
      recognizer: TapGestureRecognizer()
        ..onTap = () async {
          final safeTop = MediaQueryData.fromView(
            ui.PlatformDispatcher.instance.implicitView ?? View.of(context),
          ).padding.top;
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            builder: (final _) => BlocProvider(
              create: (final context) => getIt<CreateChatGroupBloc>()
                ..add(
                  CreateChatGroupUserLoaded(
                    const CreateChatGroupUserLoadRequestParams(
                      search: Strings.empty,
                      page: 1,
                    ),
                  ),
                ),
              child: _buildTagBody(safeTop),
            ),
          ).whenComplete(() => isOptionContent.value = OptionContent.none);
        },
      children: [
        TextSpan(
          text: ' ${vTagList.firstOrNull?.name}',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        if (vTagList.length > 1)
          TextSpan(
            text: ' ${context.l10n.andOthers(vTagList.length - 1)}',
            style: Theme.of(context).textTheme.titleSmall,
          ),
      ],
    );
  }

  TextSpan _buildLocationOne(final StoryWriteInfoRequestParams vStoryInfo) {
    return TextSpan(
      text: ' ${context.l10n.isLocation} ',
      children: [
        TextSpan(
          text: '${vStoryInfo.location?.name}',
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ],
    );
  }

  TextSpan _buildEmojiOne(final StoryWriteInfoRequestParams vStoryInfo) {
    return TextSpan(
      text: ' ${context.l10n.feeling} ',
      children: [
        TextSpan(
          text:
              '${vStoryInfo.emoji?.icon} '
              '${vStoryInfo.emoji?.text}',
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ],
    );
  }

  Widget _buildTagBody(final double safeTop) {
    return SizedBox.expand(
      child: ColoredBox(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Padding(
          padding: EdgeInsets.only(top: safeTop),
          child: BlocConsumer<CreateChatGroupBloc, CreateChatGroupState>(
            listener: (final context, final state) {
              if (state.status == CreateChatGroupStatus.userLoadSuccess) {
                data = Utils.getData(state.data);
              }
              if (state.status == CreateChatGroupStatus.failure) {
                unawaited(
                  ApiErrorDialog.show(
                    ApiErrorParams(context, Utils.getData(state.data)),
                  ),
                );
              }
            },
            builder: (final context, final state) {
              return Column(
                children: [
                  _buildOptionStoryHeader(context.l10n.shareStaff),
                  _buildStaffSearch(context, context.l10n.hintFindStaff),
                  const SizedBox(height: 8),
                  ValueListenableBuilder(
                    valueListenable: tagList,
                    builder: (final builder, final vTagList, final child) {
                      return AnimatedSize(
                        duration: const Duration(milliseconds: 300),
                        child: vTagList.isNotEmpty
                            ? _buildTagList(vTagList)
                            : const SizedBox(),
                      );
                    },
                  ),
                  _buildStaffList(),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

enum OptionStory { none, location, staff }
