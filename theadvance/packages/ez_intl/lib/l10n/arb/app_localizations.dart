import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_vi.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'arb/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('vi')
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'The Advance'**
  String get appName;

  /// No description provided for @service.
  ///
  /// In en, this message translates to:
  /// **'Dịch vụ'**
  String get service;

  /// No description provided for @showDirection.
  ///
  /// In en, this message translates to:
  /// **'Chỉ đường'**
  String get showDirection;

  /// No description provided for @appointmentApproved.
  ///
  /// In en, this message translates to:
  /// **'approved'**
  String get appointmentApproved;

  /// No description provided for @appointmentWaitingApproved.
  ///
  /// In en, this message translates to:
  /// **'waiting_approve'**
  String get appointmentWaitingApproved;

  /// No description provided for @modifyAppointment.
  ///
  /// In en, this message translates to:
  /// **'Sửa lịch'**
  String get modifyAppointment;

  /// No description provided for @cancelAppointment.
  ///
  /// In en, this message translates to:
  /// **'Huỷ lịch'**
  String get cancelAppointment;

  /// No description provided for @guestUser.
  ///
  /// In en, this message translates to:
  /// **'Guest user'**
  String get guestUser;

  /// No description provided for @loginByPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Đăng nhập bằng số điện thoại'**
  String get loginByPhoneNumber;

  /// No description provided for @unknown.
  ///
  /// In en, this message translates to:
  /// **'Có lỗi xảy ra, vui lòng thử lại sau'**
  String get unknown;

  /// No description provided for @tokenExpired.
  ///
  /// In en, this message translates to:
  /// **'Phiên đăng nhập đã hết hạn, vui lòng đăng nhập lại'**
  String get tokenExpired;

  /// No description provided for @supportRequest.
  ///
  /// In en, this message translates to:
  /// **'Yêu cầu hỗ trợ'**
  String get supportRequest;

  /// No description provided for @inboxIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'Bạn chưa có thư hỗ trợ nào'**
  String get inboxIsEmpty;

  /// No description provided for @servicesIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'Bạn chưa đặt dịch vụ nào'**
  String get servicesIsEmpty;

  /// No description provided for @createSupportRequest.
  ///
  /// In en, this message translates to:
  /// **'Tạo yêu cầu hỗ trợ'**
  String get createSupportRequest;

  /// No description provided for @confirmLogout.
  ///
  /// In en, this message translates to:
  /// **'Bạn có muốn đăng xuất tài khoản này?'**
  String get confirmLogout;

  /// No description provided for @confirmCancelAppointment.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn muốn huỷ lịch?'**
  String get confirmCancelAppointment;

  /// No description provided for @callYouLater.
  ///
  /// In en, this message translates to:
  /// **'Sẽ có nhân viên gọi điện xác nhận trong thời gian sớm nhất'**
  String get callYouLater;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Đăng xuất'**
  String get logout;

  /// No description provided for @account.
  ///
  /// In en, this message translates to:
  /// **'Tài khoản'**
  String get account;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Hủy'**
  String get cancel;

  /// No description provided for @history.
  ///
  /// In en, this message translates to:
  /// **'Lịch sử'**
  String get history;

  /// No description provided for @referral.
  ///
  /// In en, this message translates to:
  /// **'Giới thiệu khách hàng'**
  String get referral;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Liên hệ'**
  String get contactUs;

  /// No description provided for @collaborator.
  ///
  /// In en, this message translates to:
  /// **'Cộng tác viên'**
  String get collaborator;

  /// No description provided for @notUpdated.
  ///
  /// In en, this message translates to:
  /// **'Chưa cập nhật'**
  String get notUpdated;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Họ và tên'**
  String get fullName;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Số điện thoại'**
  String get phoneNumber;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Địa chỉ'**
  String get address;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Lưu'**
  String get save;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Sửa'**
  String get edit;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'Có lỗi xảy ra khi cập nhật thông tin'**
  String get errorOccurred;

  /// No description provided for @errorEmptyFullName.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập họ và tên'**
  String get errorEmptyFullName;

  /// No description provided for @updateProfileSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật thông tin cá nhân thành công'**
  String get updateProfileSuccessful;

  /// No description provided for @personalInfo.
  ///
  /// In en, this message translates to:
  /// **'Thông tin cá nhân'**
  String get personalInfo;

  /// No description provided for @emailHaveWhiteSpace.
  ///
  /// In en, this message translates to:
  /// **'Email không được chứa khoảng trắng'**
  String get emailHaveWhiteSpace;

  /// No description provided for @emailWrongFormat.
  ///
  /// In en, this message translates to:
  /// **'Email không đúng định dạng'**
  String get emailWrongFormat;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Đóng'**
  String get close;

  /// No description provided for @openSettings.
  ///
  /// In en, this message translates to:
  /// **'Mở cài đặt'**
  String get openSettings;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Cài đặt chung'**
  String get settings;

  /// No description provided for @fonts.
  ///
  /// In en, this message translates to:
  /// **'Phông chữ'**
  String get fonts;

  /// No description provided for @fontSize.
  ///
  /// In en, this message translates to:
  /// **'Cỡ chữ'**
  String get fontSize;

  /// No description provided for @smallSize.
  ///
  /// In en, this message translates to:
  /// **'Cỡ nhỏ'**
  String get smallSize;

  /// No description provided for @bigSize.
  ///
  /// In en, this message translates to:
  /// **'Cỡ lớn'**
  String get bigSize;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Xong'**
  String get done;

  /// No description provided for @referralContent.
  ///
  /// In en, this message translates to:
  /// **'Giới thiệu khách hàng bằng cách chia sẻ mã giới thiệu hoặc nhập thông tin theo mẫu dưới để The Advance có thể hỗ trợ.'**
  String get referralContent;

  /// No description provided for @pickServices.
  ///
  /// In en, this message translates to:
  /// **'Lựa chọn quà tặng'**
  String get pickServices;

  /// No description provided for @pickYears.
  ///
  /// In en, this message translates to:
  /// **'Chọn năm sinh'**
  String get pickYears;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Gửi'**
  String get send;

  /// No description provided for @inputContent.
  ///
  /// In en, this message translates to:
  /// **'Nhập nội dung'**
  String get inputContent;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Ghi chú'**
  String get notes;

  /// No description provided for @gift.
  ///
  /// In en, this message translates to:
  /// **'Quà tặng'**
  String get gift;

  /// No description provided for @years.
  ///
  /// In en, this message translates to:
  /// **'Năm sinh (*)'**
  String get years;

  /// No description provided for @phoneHintText.
  ///
  /// In en, this message translates to:
  /// **'Nhập số điện thoại (bắt buộc)'**
  String get phoneHintText;

  /// No description provided for @phoneText.
  ///
  /// In en, this message translates to:
  /// **'Số điện thoại (*)'**
  String get phoneText;

  /// No description provided for @fullNameHintText.
  ///
  /// In en, this message translates to:
  /// **'Họ tên (bắt buộc)'**
  String get fullNameHintText;

  /// No description provided for @fullNameText.
  ///
  /// In en, this message translates to:
  /// **'Họ tên (*)'**
  String get fullNameText;

  /// No description provided for @pickGift.
  ///
  /// In en, this message translates to:
  /// **'Chọn quà tặng'**
  String get pickGift;

  /// No description provided for @notEnoughInfo.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập đầy đủ thông tin bắt buộc'**
  String get notEnoughInfo;

  /// No description provided for @ageRequired.
  ///
  /// In en, this message translates to:
  /// **'Số tuổi không hợp lệ, phải lớn hơn hoặc bằng 25 tuổi'**
  String get ageRequired;

  /// No description provided for @resendOTP.
  ///
  /// In en, this message translates to:
  /// **'Gửi lại OTP'**
  String get resendOTP;

  /// No description provided for @confirmOTP.
  ///
  /// In en, this message translates to:
  /// **'Xác thực OTP'**
  String get confirmOTP;

  /// No description provided for @otpInfo.
  ///
  /// In en, this message translates to:
  /// **'Một mã xác thực đã được gửi đến \n'**
  String get otpInfo;

  /// No description provided for @continuous.
  ///
  /// In en, this message translates to:
  /// **'Tiếp tục'**
  String get continuous;

  /// No description provided for @appointments.
  ///
  /// In en, this message translates to:
  /// **'Lịch hẹn'**
  String get appointments;

  /// No description provided for @appointmentDetails.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết lịch hẹn'**
  String get appointmentDetails;

  /// No description provided for @phoneIncorrect.
  ///
  /// In en, this message translates to:
  /// **'Số điện thoại không đúng, vui lòng kiểm tra lại'**
  String get phoneIncorrect;

  /// No description provided for @formIncorrect.
  ///
  /// In en, this message translates to:
  /// **'Thông tin không hợp lệ, vui lòng kiểm tra lại'**
  String get formIncorrect;

  /// No description provided for @loginMessage.
  ///
  /// In en, this message translates to:
  /// **'Quý khách vui lòng dùng số điện thoại đã cung cấp khi dùng dịch vụ tại Ngoc Dung Beauty'**
  String get loginMessage;

  /// No description provided for @agree.
  ///
  /// In en, this message translates to:
  /// **'Tôi đồng ý với '**
  String get agree;

  /// No description provided for @termsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'điều kiện và điều khoản sử dụng'**
  String get termsAndConditions;

  /// No description provided for @copyright.
  ///
  /// In en, this message translates to:
  /// **'của The Advance'**
  String get copyright;

  /// No description provided for @uploadAvatarSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật hình ảnh đại diện thành công'**
  String get uploadAvatarSuccessful;

  /// No description provided for @startUsingApp.
  ///
  /// In en, this message translates to:
  /// **'Bắt đầu trải nghiệm'**
  String get startUsingApp;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Để sau'**
  String get skip;

  /// No description provided for @emptyPage.
  ///
  /// In en, this message translates to:
  /// **'Chưa có nội dung'**
  String get emptyPage;

  /// No description provided for @accept.
  ///
  /// In en, this message translates to:
  /// **'Đồng ý'**
  String get accept;

  /// No description provided for @requestLocation.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng cho phép ứng dụng truy cập vị trí'**
  String get requestLocation;

  /// No description provided for @getLocationSuccesful.
  ///
  /// In en, this message translates to:
  /// **'Lấy thông tin vị trí hoàn tất'**
  String get getLocationSuccesful;

  /// No description provided for @getLocationFailure.
  ///
  /// In en, this message translates to:
  /// **'Lấy thông tin vị trí thất bại'**
  String get getLocationFailure;

  /// No description provided for @connectionError.
  ///
  /// In en, this message translates to:
  /// **'Không có kết nối internet, vui lòng kiểm tra lại'**
  String get connectionError;

  /// No description provided for @referralCode.
  ///
  /// In en, this message translates to:
  /// **'Mã giới thiệu'**
  String get referralCode;

  /// No description provided for @referralMessage.
  ///
  /// In en, this message translates to:
  /// **'CTV có thể chia sẻ mã bằng đưa voucher có mã CTV (Voucher do The Advance cung cấp) hoặc chia sẻ QR code cho KH, KH sau đó có thể sử dụng voucher hoặc mã QR khi thanh toán.'**
  String get referralMessage;

  /// No description provided for @shareQRCode.
  ///
  /// In en, this message translates to:
  /// **'Chia sẻ mã QR'**
  String get shareQRCode;

  /// No description provided for @invalidScannedCode.
  ///
  /// In en, this message translates to:
  /// **'Thông tin quét mã không đúng'**
  String get invalidScannedCode;

  /// No description provided for @referralCollaborator.
  ///
  /// In en, this message translates to:
  /// **'Giới thiệu CTV'**
  String get referralCollaborator;

  /// No description provided for @news.
  ///
  /// In en, this message translates to:
  /// **'Tin tức'**
  String get news;

  /// No description provided for @androidConfirmExit.
  ///
  /// In en, this message translates to:
  /// **'Chạm lần nữa để thoát ứng dụng'**
  String get androidConfirmExit;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Trang chủ'**
  String get home;

  /// No description provided for @sendFeedback.
  ///
  /// In en, this message translates to:
  /// **'Gửi đánh giá'**
  String get sendFeedback;

  /// No description provided for @sendResponse.
  ///
  /// In en, this message translates to:
  /// **'Gửi phản hồi'**
  String get sendResponse;

  /// No description provided for @feedbackServiceMessage.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng đánh giá sao dịch vụ'**
  String get feedbackServiceMessage;

  /// No description provided for @feedbackServiceTitle.
  ///
  /// In en, this message translates to:
  /// **'Đánh giá dịch vụ'**
  String get feedbackServiceTitle;

  /// No description provided for @addMoreComment.
  ///
  /// In en, this message translates to:
  /// **'Nhập thêm ý kiến'**
  String get addMoreComment;

  /// No description provided for @inputPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Nhập số điện thoại'**
  String get inputPhoneNumber;

  /// No description provided for @needSupportRequestType.
  ///
  /// In en, this message translates to:
  /// **'Yêu cầu cần hỗ trợ (không bắt buộc)'**
  String get needSupportRequestType;

  /// No description provided for @chooseSupportRequestType.
  ///
  /// In en, this message translates to:
  /// **'Chọn yêu cầu'**
  String get chooseSupportRequestType;

  /// No description provided for @selectChooseSupportRequestType.
  ///
  /// In en, this message translates to:
  /// **'Lựa chọn yêu cầu'**
  String get selectChooseSupportRequestType;

  /// No description provided for @noteSupportRequest.
  ///
  /// In en, this message translates to:
  /// **'Ghi chú'**
  String get noteSupportRequest;

  /// No description provided for @inputInformationSupportRequest.
  ///
  /// In en, this message translates to:
  /// **'Nhập nội dung'**
  String get inputInformationSupportRequest;

  /// No description provided for @missingInformationSupportRequest.
  ///
  /// In en, this message translates to:
  /// **'Thiếu thông tin'**
  String get missingInformationSupportRequest;

  /// No description provided for @hintNeedSupportRequest.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập nội dung yêu cầu hỗ trợ'**
  String get hintNeedSupportRequest;

  /// No description provided for @sendSupportRequestSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Đã gửi yêu cầu hỗ trợ'**
  String get sendSupportRequestSuccessfully;

  /// No description provided for @sendSupportRequestFail.
  ///
  /// In en, this message translates to:
  /// **'Gửi yêu cầu hỗ trợ thất bại'**
  String get sendSupportRequestFail;

  /// No description provided for @alert.
  ///
  /// In en, this message translates to:
  /// **'Thông báo'**
  String get alert;

  /// No description provided for @requestPermissionLibrary.
  ///
  /// In en, this message translates to:
  /// **'Mở quyền cho phép truy cập thư viện ảnh để sử dụng được tính năng này'**
  String get requestPermissionLibrary;

  /// No description provided for @requestPermissionCamera.
  ///
  /// In en, this message translates to:
  /// **'Mở quyền cho phép truy cập camera để sử dụng được tính năng này'**
  String get requestPermissionCamera;

  /// No description provided for @requestPermissionStorage.
  ///
  /// In en, this message translates to:
  /// **'Mở quyền cho phép truy cập tập tin để sử dụng được tính năng này'**
  String get requestPermissionStorage;

  /// No description provided for @requestPermissionMicro.
  ///
  /// In en, this message translates to:
  /// **'Mở quyền cho phép truy cập micro để sử dụng được tính năng này'**
  String get requestPermissionMicro;

  /// No description provided for @requestPermissionNotification.
  ///
  /// In en, this message translates to:
  /// **'Mở quyền cho phép thông báo để sử dụng được tính năng này'**
  String get requestPermissionNotification;

  /// No description provided for @takePicture.
  ///
  /// In en, this message translates to:
  /// **'Chụp ảnh'**
  String get takePicture;

  /// No description provided for @retakePicture.
  ///
  /// In en, this message translates to:
  /// **'Chụp lại'**
  String get retakePicture;

  /// No description provided for @photoLibrary.
  ///
  /// In en, this message translates to:
  /// **'Thư viện ảnh'**
  String get photoLibrary;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Xin chào'**
  String get welcome;

  /// No description provided for @calendarEvents.
  ///
  /// In en, this message translates to:
  /// **'Lịch sự kiện'**
  String get calendarEvents;

  /// No description provided for @calendarOthersEvents.
  ///
  /// In en, this message translates to:
  /// **'Lịch sự kiện khác'**
  String get calendarOthersEvents;

  /// No description provided for @detailEvent.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết sự kiện'**
  String get detailEvent;

  /// No description provided for @seeMore.
  ///
  /// In en, this message translates to:
  /// **'Xem thêm'**
  String get seeMore;

  /// No description provided for @collapse.
  ///
  /// In en, this message translates to:
  /// **'Thu gọn'**
  String get collapse;

  /// No description provided for @subscribeEvent.
  ///
  /// In en, this message translates to:
  /// **'ĐĂNG KÝ THAM GIA'**
  String get subscribeEvent;

  /// No description provided for @subscribedEventMessage.
  ///
  /// In en, this message translates to:
  /// **'BẠN ĐÃ ĐĂNG KÝ THAM GIA SỰ KIỆN NÀY'**
  String get subscribedEventMessage;

  /// No description provided for @subscribeEventSuccess.
  ///
  /// In en, this message translates to:
  /// **'Đăng ký tham dự thành công'**
  String get subscribeEventSuccess;

  /// No description provided for @waitForUsAcceptEvent.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chờ xác nhận của chúng tôi'**
  String get waitForUsAcceptEvent;

  /// No description provided for @subscribedEvent.
  ///
  /// In en, this message translates to:
  /// **'Sự kiện đã đăng ký'**
  String get subscribedEvent;

  /// No description provided for @detail.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết'**
  String get detail;

  /// No description provided for @eVoucher.
  ///
  /// In en, this message translates to:
  /// **'E-Vouchers'**
  String get eVoucher;

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'Xem tất cả'**
  String get viewAll;

  /// No description provided for @inputVoucherCode.
  ///
  /// In en, this message translates to:
  /// **'Nhập mã Vouchers'**
  String get inputVoucherCode;

  /// No description provided for @getItNow.
  ///
  /// In en, this message translates to:
  /// **'Đổi ngay'**
  String get getItNow;

  /// No description provided for @detailShop.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết ưu đãi'**
  String get detailShop;

  /// No description provided for @detailEvaluationResult.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết kết quả đánh giá'**
  String get detailEvaluationResult;

  /// No description provided for @getItSuccess.
  ///
  /// In en, this message translates to:
  /// **'Đổi thành công'**
  String get getItSuccess;

  /// No description provided for @getItShopMessageDone.
  ///
  /// In en, this message translates to:
  /// **'BẠN ĐÃ ĐÃ ĐỔI ƯU ĐÃI NÀY'**
  String get getItShopMessageDone;

  /// No description provided for @promotion.
  ///
  /// In en, this message translates to:
  /// **'Khuyến mãi'**
  String get promotion;

  /// No description provided for @promotionOthers.
  ///
  /// In en, this message translates to:
  /// **'Các Ưu đãi khác'**
  String get promotionOthers;

  /// No description provided for @subscribedEventCaution.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn nhận vé tham gia sự kiện này'**
  String get subscribedEventCaution;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Xác nhận'**
  String get confirm;

  /// No description provided for @ofYou.
  ///
  /// In en, this message translates to:
  /// **'Của bạn'**
  String get ofYou;

  /// No description provided for @expired.
  ///
  /// In en, this message translates to:
  /// **'Hết hạn'**
  String get expired;

  /// No description provided for @rewardPoints.
  ///
  /// In en, this message translates to:
  /// **'Điểm Thưởng'**
  String get rewardPoints;

  /// No description provided for @historyOfRewardPoints.
  ///
  /// In en, this message translates to:
  /// **'Lịch sử điểm thưởng'**
  String get historyOfRewardPoints;

  /// No description provided for @preferentialShop.
  ///
  /// In en, this message translates to:
  /// **'Shop quà'**
  String get preferentialShop;

  /// No description provided for @currentlyUnderDevelopment.
  ///
  /// In en, this message translates to:
  /// **'Tính năng này hiện đang được phát triển !'**
  String get currentlyUnderDevelopment;

  /// No description provided for @keepAccumulatingPoints.
  ///
  /// In en, this message translates to:
  /// **'Hãy tiếp tục tích điểm để nhận nhiều ưu đãi hơn đến từ TMV The Advance.'**
  String get keepAccumulatingPoints;

  /// No description provided for @aboutYou.
  ///
  /// In en, this message translates to:
  /// **'Dành cho bạn'**
  String get aboutYou;

  /// No description provided for @vipSilver.
  ///
  /// In en, this message translates to:
  /// **'Thành viên bạc'**
  String get vipSilver;

  /// No description provided for @vipGold.
  ///
  /// In en, this message translates to:
  /// **'Thành viên vàng'**
  String get vipGold;

  /// No description provided for @vipDiamond.
  ///
  /// In en, this message translates to:
  /// **'Thành viên kim cương'**
  String get vipDiamond;

  /// No description provided for @member.
  ///
  /// In en, this message translates to:
  /// **'Thành viên'**
  String get member;

  /// No description provided for @hotNews.
  ///
  /// In en, this message translates to:
  /// **'Tin tức nổi bật'**
  String get hotNews;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Phiên bản'**
  String get version;

  /// No description provided for @notYetRated.
  ///
  /// In en, this message translates to:
  /// **'Chưa có hạng'**
  String get notYetRated;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Danh mục'**
  String get category;

  /// No description provided for @sort.
  ///
  /// In en, this message translates to:
  /// **'Sắp xếp'**
  String get sort;

  /// No description provided for @getThisGift.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn đổi phần quà này?'**
  String get getThisGift;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Số lượng'**
  String get amount;

  /// No description provided for @jumpToTop.
  ///
  /// In en, this message translates to:
  /// **'TRỞ LÊN TRÊN'**
  String get jumpToTop;

  /// No description provided for @myGift.
  ///
  /// In en, this message translates to:
  /// **'Quà của bạn'**
  String get myGift;

  /// No description provided for @giftCode.
  ///
  /// In en, this message translates to:
  /// **'Mã phần quà: '**
  String get giftCode;

  /// No description provided for @newReward.
  ///
  /// In en, this message translates to:
  /// **'Mới'**
  String get newReward;

  /// No description provided for @rewardUsed.
  ///
  /// In en, this message translates to:
  /// **'Đã dùng'**
  String get rewardUsed;

  /// No description provided for @transactionHistory.
  ///
  /// In en, this message translates to:
  /// **'Lịch sử giao dịch'**
  String get transactionHistory;

  /// No description provided for @emptyTransaction.
  ///
  /// In en, this message translates to:
  /// **'Không có giao dịch'**
  String get emptyTransaction;

  /// No description provided for @usedPromotion.
  ///
  /// In en, this message translates to:
  /// **'ĐÃ SỬ DỤNG ƯU ĐÃI'**
  String get usedPromotion;

  /// No description provided for @allowNotification.
  ///
  /// In en, this message translates to:
  /// **'Cho phép thông báo'**
  String get allowNotification;

  /// No description provided for @beautyLive.
  ///
  /// In en, this message translates to:
  /// **'Beauty Live'**
  String get beautyLive;

  /// No description provided for @liveStream.
  ///
  /// In en, this message translates to:
  /// **'Đang live stream'**
  String get liveStream;

  /// No description provided for @previousStream.
  ///
  /// In en, this message translates to:
  /// **'Live stream trước'**
  String get previousStream;

  /// No description provided for @playingStream.
  ///
  /// In en, this message translates to:
  /// **'Đang phát: '**
  String get playingStream;

  /// No description provided for @joinEventCode.
  ///
  /// In en, this message translates to:
  /// **'Mã tham gia sự kiện: '**
  String get joinEventCode;

  /// No description provided for @voucherCode.
  ///
  /// In en, this message translates to:
  /// **'Mã voucher'**
  String get voucherCode;

  /// No description provided for @giveVouchers.
  ///
  /// In en, this message translates to:
  /// **'Tặng Vouchers'**
  String get giveVouchers;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Họ tên'**
  String get name;

  /// No description provided for @brother.
  ///
  /// In en, this message translates to:
  /// **'Anh'**
  String get brother;

  /// No description provided for @sister.
  ///
  /// In en, this message translates to:
  /// **'Chị'**
  String get sister;

  /// No description provided for @give.
  ///
  /// In en, this message translates to:
  /// **'Tặng'**
  String get give;

  /// No description provided for @infoReceivedPerson.
  ///
  /// In en, this message translates to:
  /// **'Thông tin người nhận'**
  String get infoReceivedPerson;

  /// No description provided for @giveSuccessfulGift.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã tặng Voucher thành công'**
  String get giveSuccessfulGift;

  /// No description provided for @schedulerTitle.
  ///
  /// In en, this message translates to:
  /// **'Đặt lịch'**
  String get schedulerTitle;

  /// No description provided for @findNearestBranch.
  ///
  /// In en, this message translates to:
  /// **'TÌM CHI NHÁNH GẦN NHẤT'**
  String get findNearestBranch;

  /// No description provided for @choiceAppointmentDay.
  ///
  /// In en, this message translates to:
  /// **'CHỌN THỜI GIAN'**
  String get choiceAppointmentDay;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Trở về'**
  String get back;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Tiếp'**
  String get next;

  /// No description provided for @choiceService.
  ///
  /// In en, this message translates to:
  /// **'CHỌN DỊCH VỤ'**
  String get choiceService;

  /// No description provided for @chosenInfo.
  ///
  /// In en, this message translates to:
  /// **'THÔNG TIN ĐÃ CHỌN'**
  String get chosenInfo;

  /// No description provided for @branchSelectionStep.
  ///
  /// In en, this message translates to:
  /// **'Chọn chi nhánh'**
  String get branchSelectionStep;

  /// No description provided for @timeSelectionStep.
  ///
  /// In en, this message translates to:
  /// **'Chọn thời gian'**
  String get timeSelectionStep;

  /// No description provided for @serviceSelectionStep.
  ///
  /// In en, this message translates to:
  /// **'Chọn dịch vụ'**
  String get serviceSelectionStep;

  /// No description provided for @finishStep.
  ///
  /// In en, this message translates to:
  /// **'Hoàn tất'**
  String get finishStep;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Loại'**
  String get type;

  /// No description provided for @haveANote.
  ///
  /// In en, this message translates to:
  /// **'Bạn có một lời nhắc'**
  String get haveANote;

  /// No description provided for @letNgocDungKnowYou.
  ///
  /// In en, this message translates to:
  /// **'Hãy cho The Advance biết về bạn!'**
  String get letNgocDungKnowYou;

  /// No description provided for @yourName.
  ///
  /// In en, this message translates to:
  /// **'Tên của bạn là gì?'**
  String get yourName;

  /// No description provided for @letLocation.
  ///
  /// In en, this message translates to:
  /// **'Cho phép vị trí'**
  String get letLocation;

  /// No description provided for @beginDiscovery.
  ///
  /// In en, this message translates to:
  /// **'Bắt đầu khám phá'**
  String get beginDiscovery;

  /// No description provided for @thankToScheduler.
  ///
  /// In en, this message translates to:
  /// **'Cám ơn bạn đã gửi thông tin đặt lịch'**
  String get thankToScheduler;

  /// No description provided for @maybeHaveStaffCallYouSoon.
  ///
  /// In en, this message translates to:
  /// **'Sẽ có nhân viên gọi điện xác nhận trong thời gian sớm nhất.'**
  String get maybeHaveStaffCallYouSoon;

  /// No description provided for @choiceProvince.
  ///
  /// In en, this message translates to:
  /// **'Chọn tỉnh thành'**
  String get choiceProvince;

  /// No description provided for @notCompleteStep.
  ///
  /// In en, this message translates to:
  /// **'Bạn chưa hoàn thành bước này'**
  String get notCompleteStep;

  /// No description provided for @part.
  ///
  /// In en, this message translates to:
  /// **'phần'**
  String get part;

  /// No description provided for @failToFetchSupportRequest.
  ///
  /// In en, this message translates to:
  /// **'failed to fetch support requests'**
  String get failToFetchSupportRequest;

  /// No description provided for @expiredGift.
  ///
  /// In en, this message translates to:
  /// **'Quà hết hạn'**
  String get expiredGift;

  /// No description provided for @successReceiveGift.
  ///
  /// In en, this message translates to:
  /// **'Nhận quà thành công'**
  String get successReceiveGift;

  /// No description provided for @receiveGift.
  ///
  /// In en, this message translates to:
  /// **'Nhận quà'**
  String get receiveGift;

  /// No description provided for @wantToReceiveGift.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn nhận phần quà này?'**
  String get wantToReceiveGift;

  /// No description provided for @scanVoucher.
  ///
  /// In en, this message translates to:
  /// **'Quét Voucher'**
  String get scanVoucher;

  /// No description provided for @giveVoucher.
  ///
  /// In en, this message translates to:
  /// **'Tặng Voucher'**
  String get giveVoucher;

  /// No description provided for @rank.
  ///
  /// In en, this message translates to:
  /// **'Hạng'**
  String get rank;

  /// No description provided for @schedulerNow.
  ///
  /// In en, this message translates to:
  /// **'Đặt lịch ngay'**
  String get schedulerNow;

  /// No description provided for @readAllMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn muốn đánh dấu đã đọc tất cả thông báo?'**
  String get readAllMessage;

  /// No description provided for @font.
  ///
  /// In en, this message translates to:
  /// **'Fonts'**
  String get font;

  /// No description provided for @haveNotAppointment.
  ///
  /// In en, this message translates to:
  /// **'Bạn chưa có lịch hẹn nào'**
  String get haveNotAppointment;

  /// No description provided for @choiceSchedule.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn Đặt lịch để tạo lịch hẹn mới.'**
  String get choiceSchedule;

  /// No description provided for @receivePromotion.
  ///
  /// In en, this message translates to:
  /// **'Nhận ưu đãi'**
  String get receivePromotion;

  /// No description provided for @notAllowedToExchangeGift.
  ///
  /// In en, this message translates to:
  /// **'Quý khách không đủ điều kiện đổi quà'**
  String get notAllowedToExchangeGift;

  /// No description provided for @beautyNews.
  ///
  /// In en, this message translates to:
  /// **'Tin tức làm đẹp'**
  String get beautyNews;

  /// No description provided for @expertTitle.
  ///
  /// In en, this message translates to:
  /// **'Chuyên gia'**
  String get expertTitle;

  /// No description provided for @mainExpertTitle.
  ///
  /// In en, this message translates to:
  /// **'Video'**
  String get mainExpertTitle;

  /// No description provided for @follow.
  ///
  /// In en, this message translates to:
  /// **'Theo dõi'**
  String get follow;

  /// No description provided for @unfollow.
  ///
  /// In en, this message translates to:
  /// **'Bỏ theo dõi'**
  String get unfollow;

  /// No description provided for @expertPart.
  ///
  /// In en, this message translates to:
  /// **'Góc chuyên gia'**
  String get expertPart;

  /// No description provided for @hotVideos.
  ///
  /// In en, this message translates to:
  /// **'Video nổi bật'**
  String get hotVideos;

  /// No description provided for @viewResponse.
  ///
  /// In en, this message translates to:
  /// **'Xem phản hồi'**
  String get viewResponse;

  /// No description provided for @comment.
  ///
  /// In en, this message translates to:
  /// **'Bình luận'**
  String get comment;

  /// No description provided for @cannotLoadVideo.
  ///
  /// In en, this message translates to:
  /// **'Không thể hiển thị video'**
  String get cannotLoadVideo;

  /// No description provided for @addComment.
  ///
  /// In en, this message translates to:
  /// **'Thêm bình luận'**
  String get addComment;

  /// No description provided for @noComment.
  ///
  /// In en, this message translates to:
  /// **'Không có bình luận'**
  String get noComment;

  /// No description provided for @codeCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Mã check in'**
  String get codeCheckIn;

  /// No description provided for @code.
  ///
  /// In en, this message translates to:
  /// **'Mã số'**
  String get code;

  /// No description provided for @checkin.
  ///
  /// In en, this message translates to:
  /// **'Check in'**
  String get checkin;

  /// No description provided for @survey.
  ///
  /// In en, this message translates to:
  /// **'Khảo sát'**
  String get survey;

  /// No description provided for @noMoreVideo.
  ///
  /// In en, this message translates to:
  /// **'Chưa có video mới'**
  String get noMoreVideo;

  /// No description provided for @systemBranch.
  ///
  /// In en, this message translates to:
  /// **'Hệ thống chi nhánh'**
  String get systemBranch;

  /// No description provided for @hotline.
  ///
  /// In en, this message translates to:
  /// **'Hotline'**
  String get hotline;

  /// No description provided for @choiceDistrict.
  ///
  /// In en, this message translates to:
  /// **'Chọn Quận/Huyện'**
  String get choiceDistrict;

  /// No description provided for @choiceWard.
  ///
  /// In en, this message translates to:
  /// **'Chọn Phường/Xã'**
  String get choiceWard;

  /// No description provided for @noPhone.
  ///
  /// In en, this message translates to:
  /// **'Không có số điện thoại'**
  String get noPhone;

  /// No description provided for @noCreateAppointment.
  ///
  /// In en, this message translates to:
  /// **'Hiện tại không thể đặt lịch'**
  String get noCreateAppointment;

  /// No description provided for @newsCare.
  ///
  /// In en, this message translates to:
  /// **'Chăm sóc sau làm'**
  String get newsCare;

  /// No description provided for @emptyBranch.
  ///
  /// In en, this message translates to:
  /// **'Không có chi nhánh'**
  String get emptyBranch;

  /// No description provided for @connectUs.
  ///
  /// In en, this message translates to:
  /// **'Kết nối với chúng tôi:'**
  String get connectUs;

  /// No description provided for @surveyMsg.
  ///
  /// In en, this message translates to:
  /// **'BẠN QUAN TÂM DỊCH VỤ NÀO CỦA The Advance ?'**
  String get surveyMsg;

  /// No description provided for @emailLogin.
  ///
  /// In en, this message translates to:
  /// **'Email đăng nhập'**
  String get emailLogin;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu'**
  String get password;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Đăng nhập'**
  String get login;

  /// No description provided for @exitAccount.
  ///
  /// In en, this message translates to:
  /// **'Thoát tài khoản'**
  String get exitAccount;

  /// No description provided for @ignore.
  ///
  /// In en, this message translates to:
  /// **'Bỏ qua'**
  String get ignore;

  /// No description provided for @contentTouchId.
  ///
  /// In en, this message translates to:
  /// **'Sử dụng dấu vân tay để mở khóa ứng dụng một cách nhanh chóng và tiện lợi'**
  String get contentTouchId;

  /// No description provided for @titleTouchId.
  ///
  /// In en, this message translates to:
  /// **'Mở khóa bằng vân tay'**
  String get titleTouchId;

  /// No description provided for @turnOn.
  ///
  /// In en, this message translates to:
  /// **'Bật'**
  String get turnOn;

  /// No description provided for @turnOff.
  ///
  /// In en, this message translates to:
  /// **'Tắt'**
  String get turnOff;

  /// No description provided for @loginTouchId.
  ///
  /// In en, this message translates to:
  /// **'Đăng nhập bằng vân tay'**
  String get loginTouchId;

  /// No description provided for @localizeReasonTouchId.
  ///
  /// In en, this message translates to:
  /// **'Quét vân tay của bạn để xác thực'**
  String get localizeReasonTouchId;

  /// No description provided for @fingerprintRequired.
  ///
  /// In en, this message translates to:
  /// **'Yêu cầu đăng kí vân tay'**
  String get fingerprintRequired;

  /// No description provided for @iOSLockOut.
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.'**
  String get iOSLockOut;

  /// No description provided for @androidFingerprintSuccess.
  ///
  /// In en, this message translates to:
  /// **'Nhận dạng thành công'**
  String get androidFingerprintSuccess;

  /// No description provided for @androidFingerprintNotRecognized.
  ///
  /// In en, this message translates to:
  /// **'Nhận dạng thái bại! Xin thử lại!'**
  String get androidFingerprintNotRecognized;

  /// No description provided for @events.
  ///
  /// In en, this message translates to:
  /// **'Sự kiện'**
  String get events;

  /// No description provided for @noTicketSaved.
  ///
  /// In en, this message translates to:
  /// **'Không có mã tài sản mới cần lưu !'**
  String get noTicketSaved;

  /// No description provided for @dailyInventory.
  ///
  /// In en, this message translates to:
  /// **'Kiểm kê thiết bị hằng ngày'**
  String get dailyInventory;

  /// No description provided for @normalInventory.
  ///
  /// In en, this message translates to:
  /// **'Kiểm kê thông thường'**
  String get normalInventory;

  /// No description provided for @searchInfoSuccess.
  ///
  /// In en, this message translates to:
  /// **'Tìm thông tin thành công'**
  String get searchInfoSuccess;

  /// No description provided for @searchInfoFailure.
  ///
  /// In en, this message translates to:
  /// **'Không tìm thấy thông tin'**
  String get searchInfoFailure;

  /// No description provided for @cantInputManual.
  ///
  /// In en, this message translates to:
  /// **'Tài sản này không được nhập tay, vui lòng dùng chức năng Quét QRCode'**
  String get cantInputManual;

  /// No description provided for @inventoryTicket.
  ///
  /// In en, this message translates to:
  /// **'Phiếu kiểm kê'**
  String get inventoryTicket;

  /// No description provided for @inventory.
  ///
  /// In en, this message translates to:
  /// **'Kiểm kê'**
  String get inventory;

  /// No description provided for @agency.
  ///
  /// In en, this message translates to:
  /// **'Chi nhánh'**
  String get agency;

  /// No description provided for @accountLogin.
  ///
  /// In en, this message translates to:
  /// **'Tài khoản đăng nhập'**
  String get accountLogin;

  /// No description provided for @hello.
  ///
  /// In en, this message translates to:
  /// **'Xin chào'**
  String get hello;

  /// No description provided for @emailEmpty.
  ///
  /// In en, this message translates to:
  /// **'Email không được bỏ trống'**
  String get emailEmpty;

  /// No description provided for @passwordHaveWhiteSpace.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu không được chứa khoảng trắng'**
  String get passwordHaveWhiteSpace;

  /// No description provided for @passEmpty.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu không được bỏ trống'**
  String get passEmpty;

  /// No description provided for @passwordWrongFormat.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu ít nhất phải là 6 ký tự'**
  String get passwordWrongFormat;

  /// No description provided for @loginWithFingerprint.
  ///
  /// In en, this message translates to:
  /// **'Đăng nhập bằng vân tay'**
  String get loginWithFingerprint;

  /// No description provided for @logoutAccount.
  ///
  /// In en, this message translates to:
  /// **'THOÁT TÀI KHOẢN'**
  String get logoutAccount;

  /// No description provided for @countTicket.
  ///
  /// In en, this message translates to:
  /// **'Số phiếu'**
  String get countTicket;

  /// No description provided for @department.
  ///
  /// In en, this message translates to:
  /// **'Bộ phận'**
  String get department;

  /// No description provided for @validateFormInventory.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng hoàn thành tất cả thông tin'**
  String get validateFormInventory;

  /// No description provided for @needInventory.
  ///
  /// In en, this message translates to:
  /// **'Cần kiểm kê'**
  String get needInventory;

  /// No description provided for @inventoried.
  ///
  /// In en, this message translates to:
  /// **'Đã kiểm kê'**
  String get inventoried;

  /// No description provided for @scanQRCode.
  ///
  /// In en, this message translates to:
  /// **'Quét QR Code'**
  String get scanQRCode;

  /// No description provided for @inputBarcode.
  ///
  /// In en, this message translates to:
  /// **'Nhập tay'**
  String get inputBarcode;

  /// No description provided for @assetBarcode.
  ///
  /// In en, this message translates to:
  /// **'Mã code tài sản'**
  String get assetBarcode;

  /// No description provided for @complete.
  ///
  /// In en, this message translates to:
  /// **'Hoàn tất'**
  String get complete;

  /// No description provided for @completeThisAssetInventory.
  ///
  /// In en, this message translates to:
  /// **'Tài sản đã kiểm kê trước đó'**
  String get completeThisAssetInventory;

  /// No description provided for @completedInventory.
  ///
  /// In en, this message translates to:
  /// **'Kiểm kê thành công'**
  String get completedInventory;

  /// No description provided for @notFindInventory.
  ///
  /// In en, this message translates to:
  /// **'Tài sản không thuộc bộ phận hiện tại !'**
  String get notFindInventory;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @staff.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên'**
  String get staff;

  /// No description provided for @staffInventory.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên kiểm kê'**
  String get staffInventory;

  /// No description provided for @staffAccountant.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên kế toán'**
  String get staffAccountant;

  /// No description provided for @staffOther.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên khác'**
  String get staffOther;

  /// No description provided for @listInventory.
  ///
  /// In en, this message translates to:
  /// **'Danh sách kiểm kê'**
  String get listInventory;

  /// No description provided for @createNewInventory.
  ///
  /// In en, this message translates to:
  /// **'Tạo phiếu kiểm kê mới'**
  String get createNewInventory;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm'**
  String get search;

  /// No description provided for @listIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'Danh sách trống'**
  String get listIsEmpty;

  /// No description provided for @filterBy.
  ///
  /// In en, this message translates to:
  /// **'Lọc theo'**
  String get filterBy;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'LỌC'**
  String get filter;

  /// No description provided for @fromDay.
  ///
  /// In en, this message translates to:
  /// **'Từ ngày'**
  String get fromDay;

  /// No description provided for @toDay.
  ///
  /// In en, this message translates to:
  /// **'Đến ngày'**
  String get toDay;

  /// No description provided for @checkIn.
  ///
  /// In en, this message translates to:
  /// **'Chấm công vào'**
  String get checkIn;

  /// No description provided for @checkOut.
  ///
  /// In en, this message translates to:
  /// **'Chấm công ra'**
  String get checkOut;

  /// No description provided for @unmap.
  ///
  /// In en, this message translates to:
  /// **'unmap'**
  String get unmap;

  /// No description provided for @ticketCode.
  ///
  /// In en, this message translates to:
  /// **'Mã check in'**
  String get ticketCode;

  /// No description provided for @voucherCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Voucher check in'**
  String get voucherCheckIn;

  /// No description provided for @scan.
  ///
  /// In en, this message translates to:
  /// **'Quét'**
  String get scan;

  /// No description provided for @reward.
  ///
  /// In en, this message translates to:
  /// **'Trao thưởng'**
  String get reward;

  /// No description provided for @rewardTitle.
  ///
  /// In en, this message translates to:
  /// **'Voucher nhận quà'**
  String get rewardTitle;

  /// No description provided for @confirmCheckInMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn check in cho khách hàng này?'**
  String get confirmCheckInMessage;

  /// No description provided for @confirmCheckOutMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn check out cho khách hàng này?'**
  String get confirmCheckOutMessage;

  /// No description provided for @confirmRewardMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn trao quà cho khách hàng này?'**
  String get confirmRewardMessage;

  /// No description provided for @confirmUnmapVoucherMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn hủy voucher của khách hàng này?'**
  String get confirmUnmapVoucherMessage;

  /// No description provided for @ticketCodeIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'Mã check in không được để trống'**
  String get ticketCodeIsEmpty;

  /// No description provided for @voucherCodeIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'Mã voucher không được để trống'**
  String get voucherCodeIsEmpty;

  /// No description provided for @rankIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'Giải thưởng không được để trống'**
  String get rankIsEmpty;

  /// No description provided for @politePrefix.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập'**
  String get politePrefix;

  /// No description provided for @yearOfBirth.
  ///
  /// In en, this message translates to:
  /// **'Năm sinh'**
  String get yearOfBirth;

  /// No description provided for @voucherCheckInIsEmptywarningText.
  ///
  /// In en, this message translates to:
  /// **'Chưa nhập voucher check in'**
  String get voucherCheckInIsEmptywarningText;

  /// No description provided for @createTicket.
  ///
  /// In en, this message translates to:
  /// **'TẠO TICKET'**
  String get createTicket;

  /// No description provided for @allowLocation.
  ///
  /// In en, this message translates to:
  /// **'Cho phép vị trí'**
  String get allowLocation;

  /// No description provided for @getStarted.
  ///
  /// In en, this message translates to:
  /// **'Bắt đầu khám phá'**
  String get getStarted;

  /// No description provided for @accountEmpty.
  ///
  /// In en, this message translates to:
  /// **'Chưa điền tài khoản đăng nhập'**
  String get accountEmpty;

  /// No description provided for @branchEmpty.
  ///
  /// In en, this message translates to:
  /// **'Chưa chọn chi nhánh'**
  String get branchEmpty;

  /// No description provided for @approval.
  ///
  /// In en, this message translates to:
  /// **'Phê duyệt'**
  String get approval;

  /// No description provided for @waiting.
  ///
  /// In en, this message translates to:
  /// **'Đang chờ'**
  String get waiting;

  /// No description provided for @approved.
  ///
  /// In en, this message translates to:
  /// **'Đã duyệt'**
  String get approved;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Đã từ chối'**
  String get rejected;

  /// No description provided for @myApproval.
  ///
  /// In en, this message translates to:
  /// **'Phê duyệt của tôi'**
  String get myApproval;

  /// No description provided for @staffCode.
  ///
  /// In en, this message translates to:
  /// **'Mã nhân viên'**
  String get staffCode;

  /// No description provided for @staffName.
  ///
  /// In en, this message translates to:
  /// **'Tên nhân viên'**
  String get staffName;

  /// No description provided for @sendDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày gửi'**
  String get sendDate;

  /// No description provided for @editCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Ngày chỉnh công'**
  String get editCheckIn;

  /// No description provided for @totalCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Tổng ngày công'**
  String get totalCheckIn;

  /// No description provided for @checkInTime.
  ///
  /// In en, this message translates to:
  /// **'Giờ check - in'**
  String get checkInTime;

  /// No description provided for @checkOutTime.
  ///
  /// In en, this message translates to:
  /// **'Giờ check out'**
  String get checkOutTime;

  /// No description provided for @typeUpdateCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Loại công cập nhật'**
  String get typeUpdateCheckIn;

  /// No description provided for @hour.
  ///
  /// In en, this message translates to:
  /// **'giờ'**
  String get hour;

  /// No description provided for @sendAWish.
  ///
  /// In en, this message translates to:
  /// **'gửi lời chúc'**
  String get sendAWish;

  /// No description provided for @waitingApprove.
  ///
  /// In en, this message translates to:
  /// **'chờ duyệt'**
  String get waitingApprove;

  /// No description provided for @mine.
  ///
  /// In en, this message translates to:
  /// **'của tôi'**
  String get mine;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Từ chối'**
  String get reject;

  /// No description provided for @emptyHistoryApproval.
  ///
  /// In en, this message translates to:
  /// **'Yêu cầu không có lịch sử nào?'**
  String get emptyHistoryApproval;

  /// No description provided for @reason.
  ///
  /// In en, this message translates to:
  /// **'Lý do'**
  String get reason;

  /// No description provided for @hintReason.
  ///
  /// In en, this message translates to:
  /// **'Nhập nội dung ...'**
  String get hintReason;

  /// No description provided for @choice.
  ///
  /// In en, this message translates to:
  /// **'Chọn theo'**
  String get choice;

  /// No description provided for @typeAsset.
  ///
  /// In en, this message translates to:
  /// **'Loại tài sán'**
  String get typeAsset;

  /// No description provided for @nameAsset.
  ///
  /// In en, this message translates to:
  /// **'Tên tài sán'**
  String get nameAsset;

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Giá'**
  String get price;

  /// No description provided for @recommendationList.
  ///
  /// In en, this message translates to:
  /// **'DANH SÁCH GỢI Ý'**
  String get recommendationList;

  /// No description provided for @recentSearch.
  ///
  /// In en, this message translates to:
  /// **'TÌM KIẾM GẦN ĐÂY'**
  String get recentSearch;

  /// No description provided for @chat.
  ///
  /// In en, this message translates to:
  /// **'Chats'**
  String get chat;

  /// No description provided for @skype.
  ///
  /// In en, this message translates to:
  /// **'Skype'**
  String get skype;

  /// No description provided for @contract.
  ///
  /// In en, this message translates to:
  /// **'Hợp đồng lao động'**
  String get contract;

  /// No description provided for @position.
  ///
  /// In en, this message translates to:
  /// **'Chức vụ'**
  String get position;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Trạng thái'**
  String get status;

  /// No description provided for @reportTo.
  ///
  /// In en, this message translates to:
  /// **'Báo cáo cho'**
  String get reportTo;

  /// No description provided for @group.
  ///
  /// In en, this message translates to:
  /// **'Nhóm'**
  String get group;

  /// No description provided for @gender.
  ///
  /// In en, this message translates to:
  /// **'Giới tính'**
  String get gender;

  /// No description provided for @dob.
  ///
  /// In en, this message translates to:
  /// **'Ngày sinh'**
  String get dob;

  /// No description provided for @form.
  ///
  /// In en, this message translates to:
  /// **'Hình thức'**
  String get form;

  /// No description provided for @totalSalary.
  ///
  /// In en, this message translates to:
  /// **'TỔNG LƯƠNG'**
  String get totalSalary;

  /// No description provided for @ctt.
  ///
  /// In en, this message translates to:
  /// **'Công thực tế'**
  String get ctt;

  /// No description provided for @timekeeper.
  ///
  /// In en, this message translates to:
  /// **'Máy chấm công'**
  String get timekeeper;

  /// No description provided for @takingCheckinPhoto.
  ///
  /// In en, this message translates to:
  /// **'Chụp ảnh check-in'**
  String get takingCheckinPhoto;

  /// No description provided for @wordkedDay.
  ///
  /// In en, this message translates to:
  /// **'Công tính lương'**
  String get wordkedDay;

  /// No description provided for @payrollRate.
  ///
  /// In en, this message translates to:
  /// **'Tỉ lệ nhận lương'**
  String get payrollRate;

  /// No description provided for @annualLeave.
  ///
  /// In en, this message translates to:
  /// **'Phép năm'**
  String get annualLeave;

  /// No description provided for @unusedAnnualLeave.
  ///
  /// In en, this message translates to:
  /// **'Ngày phép còn lại'**
  String get unusedAnnualLeave;

  /// No description provided for @timekeepingOverview.
  ///
  /// In en, this message translates to:
  /// **'Tổng hợp chấm công'**
  String get timekeepingOverview;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'ngày'**
  String get day;

  /// No description provided for @salaryDetail.
  ///
  /// In en, this message translates to:
  /// **'Chấm công chi tiết'**
  String get salaryDetail;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'tháng'**
  String get month;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'năm'**
  String get year;

  /// No description provided for @totalWorkInDay.
  ///
  /// In en, this message translates to:
  /// **'Tổng công trong ngày'**
  String get totalWorkInDay;

  /// No description provided for @timeCheckIn.
  ///
  /// In en, this message translates to:
  /// **'giờ vào'**
  String get timeCheckIn;

  /// No description provided for @timeCheckOut.
  ///
  /// In en, this message translates to:
  /// **'giờ ra'**
  String get timeCheckOut;

  /// No description provided for @createUpdateWorkRequest.
  ///
  /// In en, this message translates to:
  /// **'Tạo yêu cầu sửa công'**
  String get createUpdateWorkRequest;

  /// No description provided for @workType.
  ///
  /// In en, this message translates to:
  /// **'Loại công'**
  String get workType;

  /// No description provided for @totalWork.
  ///
  /// In en, this message translates to:
  /// **'Tổng công'**
  String get totalWork;

  /// No description provided for @request.
  ///
  /// In en, this message translates to:
  /// **'yêu cầu'**
  String get request;

  /// No description provided for @daysOff.
  ///
  /// In en, this message translates to:
  /// **'nghỉ phép'**
  String get daysOff;

  /// No description provided for @daysOffLeft.
  ///
  /// In en, this message translates to:
  /// **'Ngày phép còn'**
  String get daysOffLeft;

  /// No description provided for @business.
  ///
  /// In en, this message translates to:
  /// **'công tác'**
  String get business;

  /// No description provided for @purpose.
  ///
  /// In en, this message translates to:
  /// **'mục đích'**
  String get purpose;

  /// No description provided for @mth.
  ///
  /// In en, this message translates to:
  /// **'THG'**
  String get mth;

  /// No description provided for @areYouSureSigningOut.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn thoát tài khoản?'**
  String get areYouSureSigningOut;

  /// No description provided for @workingProgress.
  ///
  /// In en, this message translates to:
  /// **'Quá trình làm việc'**
  String get workingProgress;

  /// No description provided for @connectionErrorPleaseRefresh.
  ///
  /// In en, this message translates to:
  /// **'Lỗi kết nối, vui lòng tải lại trang'**
  String get connectionErrorPleaseRefresh;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Tải lại'**
  String get refresh;

  /// No description provided for @prize.
  ///
  /// In en, this message translates to:
  /// **'Giải thưởng'**
  String get prize;

  /// No description provided for @check.
  ///
  /// In en, this message translates to:
  /// **'Kiểm tra'**
  String get check;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Thay đổi'**
  String get change;

  /// No description provided for @noRouteDefinedYet.
  ///
  /// In en, this message translates to:
  /// **'Chưa có đường dẫn'**
  String get noRouteDefinedYet;

  /// No description provided for @otpCodeIsNotEnoughNumbers.
  ///
  /// In en, this message translates to:
  /// **'Mã xác thực chưa đủ 4 số'**
  String get otpCodeIsNotEnoughNumbers;

  /// No description provided for @statistic.
  ///
  /// In en, this message translates to:
  /// **'Thống kê'**
  String get statistic;

  /// No description provided for @inputPhoneReceiver.
  ///
  /// In en, this message translates to:
  /// **'Nhập số điện thoại người nhận'**
  String get inputPhoneReceiver;

  /// No description provided for @noMessage.
  ///
  /// In en, this message translates to:
  /// **'Không có tin nhắn'**
  String get noMessage;

  /// No description provided for @confirmSendImage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn gửi ảnh này ?'**
  String get confirmSendImage;

  /// No description provided for @donotJob.
  ///
  /// In en, this message translates to:
  /// **'Chưa xử lí'**
  String get donotJob;

  /// No description provided for @expiredJob.
  ///
  /// In en, this message translates to:
  /// **'Quá hạn'**
  String get expiredJob;

  /// No description provided for @doneJob.
  ///
  /// In en, this message translates to:
  /// **'Đã xử lí'**
  String get doneJob;

  /// No description provided for @createTicketSuccess.
  ///
  /// In en, this message translates to:
  /// **'Tạo phiếu thành công'**
  String get createTicketSuccess;

  /// No description provided for @validateInventoryType.
  ///
  /// In en, this message translates to:
  /// **'Vui Lòng lựa chọn hình thức kiểm kê'**
  String get validateInventoryType;

  /// No description provided for @inputOTP.
  ///
  /// In en, this message translates to:
  /// **'Nhập mã OTP'**
  String get inputOTP;

  /// No description provided for @isReceiveOTP.
  ///
  /// In en, this message translates to:
  /// **'Bạn chưa nhận được?'**
  String get isReceiveOTP;

  /// No description provided for @forgetPassword.
  ///
  /// In en, this message translates to:
  /// **'Quên mật khẩu?'**
  String get forgetPassword;

  /// No description provided for @inputPassword.
  ///
  /// In en, this message translates to:
  /// **'Nhập mật khẩu'**
  String get inputPassword;

  /// No description provided for @setPassword.
  ///
  /// In en, this message translates to:
  /// **'Đặt mật khẩu'**
  String get setPassword;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Tổng'**
  String get total;

  /// No description provided for @selectMonth.
  ///
  /// In en, this message translates to:
  /// **'Chọn tháng'**
  String get selectMonth;

  /// No description provided for @goToWorkLate.
  ///
  /// In en, this message translates to:
  /// **'đi muộn'**
  String get goToWorkLate;

  /// No description provided for @offWork.
  ///
  /// In en, this message translates to:
  /// **'nghỉ làm'**
  String get offWork;

  /// No description provided for @timeAttendance.
  ///
  /// In en, this message translates to:
  /// **'Chấm công'**
  String get timeAttendance;

  /// No description provided for @updateCheckin.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật công'**
  String get updateCheckin;

  /// No description provided for @fetchHistoryCheckinFailed.
  ///
  /// In en, this message translates to:
  /// **'Có lỗi tải dữ liệu chấm công'**
  String get fetchHistoryCheckinFailed;

  /// No description provided for @fetchWorkTypeFailed.
  ///
  /// In en, this message translates to:
  /// **'Có lỗi tải danh sách loại công'**
  String get fetchWorkTypeFailed;

  /// No description provided for @requestUpdateCheckinFailed.
  ///
  /// In en, this message translates to:
  /// **'Có lỗi yêu cầu chỉnh sửa công'**
  String get requestUpdateCheckinFailed;

  /// No description provided for @expiredDate.
  ///
  /// In en, this message translates to:
  /// **'Hạn ngày'**
  String get expiredDate;

  /// No description provided for @detailJob.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết công việc'**
  String get detailJob;

  /// No description provided for @from.
  ///
  /// In en, this message translates to:
  /// **'Từ'**
  String get from;

  /// No description provided for @attachedFile.
  ///
  /// In en, this message translates to:
  /// **'File đính kèm'**
  String get attachedFile;

  /// No description provided for @conservation.
  ///
  /// In en, this message translates to:
  /// **'Thảo luận'**
  String get conservation;

  /// No description provided for @feedback.
  ///
  /// In en, this message translates to:
  /// **'Phản hồi'**
  String get feedback;

  /// No description provided for @asignee.
  ///
  /// In en, this message translates to:
  /// **'Người thực hiện'**
  String get asignee;

  /// No description provided for @sendFeedBack.
  ///
  /// In en, this message translates to:
  /// **'Gửi phản hồi'**
  String get sendFeedBack;

  /// No description provided for @fileAttach.
  ///
  /// In en, this message translates to:
  /// **'Đính kèm file'**
  String get fileAttach;

  /// No description provided for @sendFeedBackSuccess.
  ///
  /// In en, this message translates to:
  /// **'Gửi phản hồi thành công'**
  String get sendFeedBackSuccess;

  /// No description provided for @checkinFailure.
  ///
  /// In en, this message translates to:
  /// **'Check in thất bại'**
  String get checkinFailure;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Thử lại'**
  String get tryAgain;

  /// No description provided for @timekeeping.
  ///
  /// In en, this message translates to:
  /// **'Chấm công'**
  String get timekeeping;

  /// No description provided for @checkinSuccess.
  ///
  /// In en, this message translates to:
  /// **'Check in thành công'**
  String get checkinSuccess;

  /// No description provided for @getIn.
  ///
  /// In en, this message translates to:
  /// **'Vào'**
  String get getIn;

  /// No description provided for @more.
  ///
  /// In en, this message translates to:
  /// **'Thêm'**
  String get more;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Mở'**
  String get open;

  /// No description provided for @defaultError.
  ///
  /// In en, this message translates to:
  /// **'Đã có lỗi xảy ra'**
  String get defaultError;

  /// No description provided for @defaultText.
  ///
  /// In en, this message translates to:
  /// **'Mặc định'**
  String get defaultText;

  /// No description provided for @creatingWork.
  ///
  /// In en, this message translates to:
  /// **'Tạo công việc'**
  String get creatingWork;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Tiêu đề'**
  String get title;

  /// No description provided for @dateExpired.
  ///
  /// In en, this message translates to:
  /// **'Hạn ngày'**
  String get dateExpired;

  /// No description provided for @prioritize.
  ///
  /// In en, this message translates to:
  /// **'Ưu tiên'**
  String get prioritize;

  /// No description provided for @creatWork.
  ///
  /// In en, this message translates to:
  /// **'Tự tạo việc'**
  String get creatWork;

  /// No description provided for @choiceOffice.
  ///
  /// In en, this message translates to:
  /// **'Chọn phòng ban'**
  String get choiceOffice;

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Tạo'**
  String get create;

  /// No description provided for @yourChoice.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã chọn'**
  String get yourChoice;

  /// No description provided for @findByName.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên'**
  String get findByName;

  /// No description provided for @choiceMember.
  ///
  /// In en, this message translates to:
  /// **'Chọn thành viên'**
  String get choiceMember;

  /// No description provided for @choiceAll.
  ///
  /// In en, this message translates to:
  /// **'Chọn tất cả'**
  String get choiceAll;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Xác nhận mật khẩu'**
  String get confirmPassword;

  /// No description provided for @wrongConfirmPass.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu xác nhận không đúng'**
  String get wrongConfirmPass;

  /// No description provided for @loginBy.
  ///
  /// In en, this message translates to:
  /// **'Đăng nhập bằng'**
  String get loginBy;

  /// No description provided for @loginByFingerPrint.
  ///
  /// In en, this message translates to:
  /// **'vân tay cho những lần sau'**
  String get loginByFingerPrint;

  /// No description provided for @loginByFaceID.
  ///
  /// In en, this message translates to:
  /// **'Face ID cho những lần sau'**
  String get loginByFaceID;

  /// No description provided for @asset.
  ///
  /// In en, this message translates to:
  /// **'Tài sản'**
  String get asset;

  /// No description provided for @socialInsurance.
  ///
  /// In en, this message translates to:
  /// **'Bảo hiểm xã hội'**
  String get socialInsurance;

  /// No description provided for @laborContract.
  ///
  /// In en, this message translates to:
  /// **'Hợp đồng lao động'**
  String get laborContract;

  /// No description provided for @indentify.
  ///
  /// In en, this message translates to:
  /// **'Số CCCD'**
  String get indentify;

  /// No description provided for @birthday.
  ///
  /// In en, this message translates to:
  /// **'Ngày tháng năm sinh'**
  String get birthday;

  /// No description provided for @numberBank.
  ///
  /// In en, this message translates to:
  /// **'Số tài khoản ngân hàng'**
  String get numberBank;

  /// No description provided for @company.
  ///
  /// In en, this message translates to:
  /// **'Công ty'**
  String get company;

  /// No description provided for @positionMember.
  ///
  /// In en, this message translates to:
  /// **'Chức danh'**
  String get positionMember;

  /// No description provided for @office.
  ///
  /// In en, this message translates to:
  /// **'Phòng ban'**
  String get office;

  /// No description provided for @productCode.
  ///
  /// In en, this message translates to:
  /// **'Mã hàng hóa'**
  String get productCode;

  /// No description provided for @assetCode.
  ///
  /// In en, this message translates to:
  /// **'Mã tài sản'**
  String get assetCode;

  /// No description provided for @productName.
  ///
  /// In en, this message translates to:
  /// **'Tên hàng hóa'**
  String get productName;

  /// No description provided for @activeDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày hiệu lực'**
  String get activeDate;

  /// No description provided for @employeePart.
  ///
  /// In en, this message translates to:
  /// **'Phần người lao động'**
  String get employeePart;

  /// No description provided for @getImageInCamera.
  ///
  /// In en, this message translates to:
  /// **'Chụp ảnh mới'**
  String get getImageInCamera;

  /// No description provided for @getImageInGallery.
  ///
  /// In en, this message translates to:
  /// **'Chọn ảnh có sẵn'**
  String get getImageInGallery;

  /// No description provided for @avatar.
  ///
  /// In en, this message translates to:
  /// **'Ảnh đại diện'**
  String get avatar;

  /// No description provided for @titleNoti.
  ///
  /// In en, this message translates to:
  /// **'Tin tức'**
  String get titleNoti;

  /// No description provided for @notSupportDevice.
  ///
  /// In en, this message translates to:
  /// **'Thiết bị của bạn không cài đặt mở khóa khuôn mặt và vân tay'**
  String get notSupportDevice;

  /// No description provided for @approvalTitle.
  ///
  /// In en, this message translates to:
  /// **'Duyệt'**
  String get approvalTitle;

  /// No description provided for @rejectReason.
  ///
  /// In en, this message translates to:
  /// **'Lý do từ chối'**
  String get rejectReason;

  /// No description provided for @typeApprove.
  ///
  /// In en, this message translates to:
  /// **'Loại duyệt'**
  String get typeApprove;

  /// No description provided for @signal.
  ///
  /// In en, this message translates to:
  /// **'Chữ ký'**
  String get signal;

  /// No description provided for @otp.
  ///
  /// In en, this message translates to:
  /// **'OTP'**
  String get otp;

  /// No description provided for @sendEform.
  ///
  /// In en, this message translates to:
  /// **'Gửi yêu cầu'**
  String get sendEform;

  /// No description provided for @detailEform.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết yêu cầu'**
  String get detailEform;

  /// No description provided for @sender.
  ///
  /// In en, this message translates to:
  /// **'Người gửi'**
  String get sender;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Thời gian'**
  String get time;

  /// No description provided for @loginFaceID.
  ///
  /// In en, this message translates to:
  /// **'Đăng nhập bằng khuôn mặt'**
  String get loginFaceID;

  /// No description provided for @creatWorkSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã tạo công việc thành công'**
  String get creatWorkSuccess;

  /// No description provided for @authenRequired.
  ///
  /// In en, this message translates to:
  /// **'Yêu cầu xác thực'**
  String get authenRequired;

  /// No description provided for @authenToLogin.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng xác thực để đăng nhập'**
  String get authenToLogin;

  /// No description provided for @repeatWork.
  ///
  /// In en, this message translates to:
  /// **'Công việc lặp lại'**
  String get repeatWork;

  /// No description provided for @repeatWorkTitle.
  ///
  /// In en, this message translates to:
  /// **'Hình thức lặp lại'**
  String get repeatWorkTitle;

  /// No description provided for @msgDoneTask.
  ///
  /// In en, this message translates to:
  /// **'Hoàn thành công việc'**
  String get msgDoneTask;

  /// No description provided for @infoAccount.
  ///
  /// In en, this message translates to:
  /// **'Thông tin tài khoản'**
  String get infoAccount;

  /// No description provided for @mainScreen.
  ///
  /// In en, this message translates to:
  /// **'Màn hình chính'**
  String get mainScreen;

  /// No description provided for @pickHistoryCheckinDate.
  ///
  /// In en, this message translates to:
  /// **'Bảng chấm công'**
  String get pickHistoryCheckinDate;

  /// No description provided for @content.
  ///
  /// In en, this message translates to:
  /// **'Nội dung'**
  String get content;

  /// No description provided for @image.
  ///
  /// In en, this message translates to:
  /// **'Ảnh'**
  String get image;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Ngôn ngữ'**
  String get language;

  /// No description provided for @biometricVerification.
  ///
  /// In en, this message translates to:
  /// **'Xác thực sinh trắc học'**
  String get biometricVerification;

  /// No description provided for @turnOnFaceID.
  ///
  /// In en, this message translates to:
  /// **'Bật tính năng mở bằng khuôn mặt'**
  String get turnOnFaceID;

  /// No description provided for @turnOffFaceID.
  ///
  /// In en, this message translates to:
  /// **'Tắt tính năng mở bằng khuôn mặt'**
  String get turnOffFaceID;

  /// No description provided for @turnOnTouchID.
  ///
  /// In en, this message translates to:
  /// **'Bật tính năng mở bằng vân tay'**
  String get turnOnTouchID;

  /// No description provided for @turnOffTouchID.
  ///
  /// In en, this message translates to:
  /// **'Tắt tính năng mở bằng vân tay'**
  String get turnOffTouchID;

  /// No description provided for @setUpBiometrics.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng thiết lập mở khóa vân tay hoặc khuôn mặt'**
  String get setUpBiometrics;

  /// No description provided for @vnLanguage.
  ///
  /// In en, this message translates to:
  /// **'Tiếng Việt'**
  String get vnLanguage;

  /// No description provided for @enLanguage.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get enLanguage;

  /// No description provided for @infoCustomer.
  ///
  /// In en, this message translates to:
  /// **'Thông tin khách hàng'**
  String get infoCustomer;

  /// No description provided for @job.
  ///
  /// In en, this message translates to:
  /// **'Nghề nghiệp'**
  String get job;

  /// No description provided for @pathological.
  ///
  /// In en, this message translates to:
  /// **'Bệnh lý khách hàng'**
  String get pathological;

  /// No description provided for @expression.
  ///
  /// In en, this message translates to:
  /// **'Những biểu hiện của KH'**
  String get expression;

  /// No description provided for @otherExpression.
  ///
  /// In en, this message translates to:
  /// **'Biểu hiện khác'**
  String get otherExpression;

  /// No description provided for @infoCustomerBrief.
  ///
  /// In en, this message translates to:
  /// **'Thông tin KH'**
  String get infoCustomerBrief;

  /// No description provided for @task.
  ///
  /// In en, this message translates to:
  /// **'Công việc'**
  String get task;

  /// No description provided for @sendRequest.
  ///
  /// In en, this message translates to:
  /// **'Gửi yêu cầu'**
  String get sendRequest;

  /// No description provided for @typeEform.
  ///
  /// In en, this message translates to:
  /// **'Loại yêu cầu'**
  String get typeEform;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Bắt đầu'**
  String get start;

  /// No description provided for @collaboratorNews.
  ///
  /// In en, this message translates to:
  /// **'Tin tức'**
  String get collaboratorNews;

  /// No description provided for @alwayEnableNotificationForNews.
  ///
  /// In en, this message translates to:
  /// **'Luôn bật thông báo để thông tin tới quý khách tin tức hàng ngày.'**
  String get alwayEnableNotificationForNews;

  /// No description provided for @taskInformation.
  ///
  /// In en, this message translates to:
  /// **'Thông tin công việc'**
  String get taskInformation;

  /// No description provided for @checkinManagement.
  ///
  /// In en, this message translates to:
  /// **'Quản lý chấm công'**
  String get checkinManagement;

  /// No description provided for @efficientCheckinManagement.
  ///
  /// In en, this message translates to:
  /// **'Quản lý chấm công chính xác, dễ dàng'**
  String get efficientCheckinManagement;

  /// No description provided for @checkinMonthDetail.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết công tháng {month}'**
  String checkinMonthDetail(int? month);

  /// No description provided for @assetHistory.
  ///
  /// In en, this message translates to:
  /// **'Lịch sử tài sản'**
  String get assetHistory;

  /// No description provided for @transfer.
  ///
  /// In en, this message translates to:
  /// **'Điều chuyển'**
  String get transfer;

  /// No description provided for @maintenance.
  ///
  /// In en, this message translates to:
  /// **'Bảo trì'**
  String get maintenance;

  /// No description provided for @jobType.
  ///
  /// In en, this message translates to:
  /// **'Loại công việc'**
  String get jobType;

  /// No description provided for @requirementEmployee.
  ///
  /// In en, this message translates to:
  /// **'NV yêu cầu'**
  String get requirementEmployee;

  /// No description provided for @maintenanceEmployee.
  ///
  /// In en, this message translates to:
  /// **'NV bảo trì'**
  String get maintenanceEmployee;

  /// No description provided for @completionDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày hoàn thành'**
  String get completionDate;

  /// No description provided for @createNewGroup.
  ///
  /// In en, this message translates to:
  /// **'Tạo nhóm mới'**
  String get createNewGroup;

  /// No description provided for @updateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã cập nhật thành công'**
  String get updateSuccess;

  /// No description provided for @createSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã tạo thành công'**
  String get createSuccess;

  /// No description provided for @copyLink.
  ///
  /// In en, this message translates to:
  /// **'Sao chép đường dẫn'**
  String get copyLink;

  /// No description provided for @checkinReminder.
  ///
  /// In en, this message translates to:
  /// **'Nhắc nhở chấm công'**
  String get checkinReminder;

  /// No description provided for @removeUserConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Bạn có muốn xóa tài khoản này?'**
  String get removeUserConfirmation;

  /// No description provided for @removeUserAccount.
  ///
  /// In en, this message translates to:
  /// **'Xóa tài khoản'**
  String get removeUserAccount;

  /// No description provided for @removeUserAccountMessage.
  ///
  /// In en, this message translates to:
  /// **'- Xóa tên, ảnh đại diện cùng thông tin tài khoản\n- Xóa tất cả nhật ký tin nhắn và lịch sử gọi\n- Tất cả dữ liệu sẽ bị xóa vĩnh viễn không thể khôi phục'**
  String get removeUserAccountMessage;

  /// No description provided for @specification.
  ///
  /// In en, this message translates to:
  /// **'Cấu hình'**
  String get specification;

  /// No description provided for @incomingCount.
  ///
  /// In en, this message translates to:
  /// **'Số lần đến'**
  String get incomingCount;

  /// No description provided for @averageRevenue.
  ///
  /// In en, this message translates to:
  /// **'DT bình quân'**
  String get averageRevenue;

  /// No description provided for @totalCost.
  ///
  /// In en, this message translates to:
  /// **'Tổng tiền đã chi'**
  String get totalCost;

  /// No description provided for @cardBalance.
  ///
  /// In en, this message translates to:
  /// **'Số dư thẻ'**
  String get cardBalance;

  /// No description provided for @paid.
  ///
  /// In en, this message translates to:
  /// **'Đã thanh toán'**
  String get paid;

  /// No description provided for @payBack.
  ///
  /// In en, this message translates to:
  /// **'Hoàn tiền'**
  String get payBack;

  /// No description provided for @loan.
  ///
  /// In en, this message translates to:
  /// **'Tổng tiền nợ'**
  String get loan;

  /// No description provided for @submittedCost.
  ///
  /// In en, this message translates to:
  /// **'Tổng tiền nộp'**
  String get submittedCost;

  /// No description provided for @payWithCard.
  ///
  /// In en, this message translates to:
  /// **'Thanh toán bằng thẻ'**
  String get payWithCard;

  /// No description provided for @pickFloor.
  ///
  /// In en, this message translates to:
  /// **'Chọn tầng'**
  String get pickFloor;

  /// No description provided for @pickProvince.
  ///
  /// In en, this message translates to:
  /// **'Chọn tỉnh thành'**
  String get pickProvince;

  /// No description provided for @pickBranchAndFloor.
  ///
  /// In en, this message translates to:
  /// **'Chọn chi nhánh, tầng'**
  String get pickBranchAndFloor;

  /// No description provided for @goBack.
  ///
  /// In en, this message translates to:
  /// **'Trở về'**
  String get goBack;

  /// No description provided for @pickBranchFloorAndBed.
  ///
  /// In en, this message translates to:
  /// **'Chọn tầng, phòng, giường'**
  String get pickBranchFloorAndBed;

  /// No description provided for @pickBed.
  ///
  /// In en, this message translates to:
  /// **'Chọn giường'**
  String get pickBed;

  /// No description provided for @pickRoom.
  ///
  /// In en, this message translates to:
  /// **'Chọn phòng'**
  String get pickRoom;

  /// No description provided for @customerList.
  ///
  /// In en, this message translates to:
  /// **'Danh sách khách hàng'**
  String get customerList;

  /// No description provided for @selectCustomerBranchNote.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn chi nhánh, tầng để xem danh sách khách hàng!'**
  String get selectCustomerBranchNote;

  /// No description provided for @choose.
  ///
  /// In en, this message translates to:
  /// **'Chọn'**
  String get choose;

  /// No description provided for @consultationFile.
  ///
  /// In en, this message translates to:
  /// **'Hồ sơ tư vấn'**
  String get consultationFile;

  /// No description provided for @consultationManager.
  ///
  /// In en, this message translates to:
  /// **'Quản lý tư vấn'**
  String get consultationManager;

  /// No description provided for @consultationAgent.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên tư vấn'**
  String get consultationAgent;

  /// No description provided for @consultationCharge.
  ///
  /// In en, this message translates to:
  /// **'Tiền nhận tư vấn'**
  String get consultationCharge;

  /// No description provided for @doctorCharge.
  ///
  /// In en, this message translates to:
  /// **'Tiền bác sĩ'**
  String get doctorCharge;

  /// No description provided for @consultationContent.
  ///
  /// In en, this message translates to:
  /// **'Nội dung tư vấn'**
  String get consultationContent;

  /// No description provided for @exploitPlan.
  ///
  /// In en, this message translates to:
  /// **'Kế hoạch khai thác'**
  String get exploitPlan;

  /// No description provided for @botReport.
  ///
  /// In en, this message translates to:
  /// **'Báo bot'**
  String get botReport;

  /// No description provided for @chooseBed.
  ///
  /// In en, this message translates to:
  /// **'Chọn giường'**
  String get chooseBed;

  /// No description provided for @out.
  ///
  /// In en, this message translates to:
  /// **'Ra về'**
  String get out;

  /// No description provided for @treatmentFile.
  ///
  /// In en, this message translates to:
  /// **'Hồ sơ điều trị'**
  String get treatmentFile;

  /// No description provided for @searchDepartment.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên phòng'**
  String get searchDepartment;

  /// No description provided for @searchService.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên dịch vụ'**
  String get searchService;

  /// No description provided for @treatmentDetail.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết điều trị'**
  String get treatmentDetail;

  /// No description provided for @treatmentService.
  ///
  /// In en, this message translates to:
  /// **'Dịch vụ điều trị'**
  String get treatmentService;

  /// No description provided for @useCount.
  ///
  /// In en, this message translates to:
  /// **'SL sử dụng'**
  String get useCount;

  /// No description provided for @reExaminationDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày tái khám'**
  String get reExaminationDate;

  /// No description provided for @attendingDoctor.
  ///
  /// In en, this message translates to:
  /// **'Bác sĩ điều trị'**
  String get attendingDoctor;

  /// No description provided for @agentIncharge.
  ///
  /// In en, this message translates to:
  /// **'NV phụ trách'**
  String get agentIncharge;

  /// No description provided for @treatment.
  ///
  /// In en, this message translates to:
  /// **'điều trị'**
  String get treatment;

  /// No description provided for @picture.
  ///
  /// In en, this message translates to:
  /// **'Hình ảnh'**
  String get picture;

  /// No description provided for @medicalPrescription.
  ///
  /// In en, this message translates to:
  /// **'toa thuốc'**
  String get medicalPrescription;

  /// No description provided for @morning.
  ///
  /// In en, this message translates to:
  /// **'sáng'**
  String get morning;

  /// No description provided for @noon.
  ///
  /// In en, this message translates to:
  /// **'trưa'**
  String get noon;

  /// No description provided for @afternoon.
  ///
  /// In en, this message translates to:
  /// **'chiều'**
  String get afternoon;

  /// No description provided for @night.
  ///
  /// In en, this message translates to:
  /// **'tối'**
  String get night;

  /// No description provided for @medicineDetail.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết thuốc'**
  String get medicineDetail;

  /// No description provided for @searchCustomer.
  ///
  /// In en, this message translates to:
  /// **'Tìm khách hàng'**
  String get searchCustomer;

  /// No description provided for @listCustomer.
  ///
  /// In en, this message translates to:
  /// **'Danh sách khách hàng'**
  String get listCustomer;

  /// No description provided for @returnBed.
  ///
  /// In en, this message translates to:
  /// **'Trả giường'**
  String get returnBed;

  /// No description provided for @changeBed.
  ///
  /// In en, this message translates to:
  /// **'Đổi giường'**
  String get changeBed;

  /// No description provided for @picking.
  ///
  /// In en, this message translates to:
  /// **'Bạn đang chọn'**
  String get picking;

  /// No description provided for @floor.
  ///
  /// In en, this message translates to:
  /// **'Tầng'**
  String get floor;

  /// No description provided for @product.
  ///
  /// In en, this message translates to:
  /// **'Sản phẩm'**
  String get product;

  /// No description provided for @serviceAndProduct.
  ///
  /// In en, this message translates to:
  /// **'Dịch vụ/Sản phẩm'**
  String get serviceAndProduct;

  /// No description provided for @findService.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên dịch vụ'**
  String get findService;

  /// No description provided for @findProduct.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên sản phẩm'**
  String get findProduct;

  /// No description provided for @buyCount.
  ///
  /// In en, this message translates to:
  /// **'SL Mua'**
  String get buyCount;

  /// No description provided for @existCount.
  ///
  /// In en, this message translates to:
  /// **'SL Còn'**
  String get existCount;

  /// No description provided for @totalMoney.
  ///
  /// In en, this message translates to:
  /// **'Tổng tiền'**
  String get totalMoney;

  /// No description provided for @detailService.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết dịch vụ'**
  String get detailService;

  /// No description provided for @handle.
  ///
  /// In en, this message translates to:
  /// **'Xử lý'**
  String get handle;

  /// No description provided for @importantNote.
  ///
  /// In en, this message translates to:
  /// **'Ghi chú quan trọng'**
  String get importantNote;

  /// No description provided for @createNew.
  ///
  /// In en, this message translates to:
  /// **'Tạo mới'**
  String get createNew;

  /// No description provided for @firstName.
  ///
  /// In en, this message translates to:
  /// **'Tên'**
  String get firstName;

  /// No description provided for @requestStoragePermission.
  ///
  /// In en, this message translates to:
  /// **'Cho phép truy cập tệp tin'**
  String get requestStoragePermission;

  /// No description provided for @storageRequest1.
  ///
  /// In en, this message translates to:
  /// **'Lưu trữ tệp tải về'**
  String get storageRequest1;

  /// No description provided for @cameraRequest1.
  ///
  /// In en, this message translates to:
  /// **'Chụp hình cập nhật thông tin cá nhân'**
  String get cameraRequest1;

  /// No description provided for @cameraRequest2.
  ///
  /// In en, this message translates to:
  /// **'Quét mã code dễ dàng'**
  String get cameraRequest2;

  /// No description provided for @microPermissionRequest.
  ///
  /// In en, this message translates to:
  /// **'Cho phép ghi âm thiết bị'**
  String get microPermissionRequest;

  /// No description provided for @microPermissionSettings.
  ///
  /// In en, this message translates to:
  /// **'Xin cho phép quyền ghi âm trong cài đặt thiết bị'**
  String get microPermissionSettings;

  /// No description provided for @record.
  ///
  /// In en, this message translates to:
  /// **'Ghi âm'**
  String get record;

  /// No description provided for @microRequest1.
  ///
  /// In en, this message translates to:
  /// **'Gọi âm thanh'**
  String get microRequest1;

  /// No description provided for @microRequest2.
  ///
  /// In en, this message translates to:
  /// **'Gửi ghi âm hỗ trợ'**
  String get microRequest2;

  /// No description provided for @photoRequest1.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật ảnh thông tin cá nhân'**
  String get photoRequest1;

  /// No description provided for @onTheNextScreen.
  ///
  /// In en, this message translates to:
  /// **'ở màn hình tiếp theo để:'**
  String get onTheNextScreen;

  /// No description provided for @changeSettingsLater.
  ///
  /// In en, this message translates to:
  /// **'Bạn có thể thay đổi quyền này sau ở trong mục Cài đặt của ứng dụng'**
  String get changeSettingsLater;

  /// No description provided for @notificationRequest1.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật tin tức, dịch vụ mới nhất'**
  String get notificationRequest1;

  /// No description provided for @employeeInfoShort.
  ///
  /// In en, this message translates to:
  /// **'TT nhân viên'**
  String get employeeInfoShort;

  /// No description provided for @performEmployee.
  ///
  /// In en, this message translates to:
  /// **'Tên NV thực hiện'**
  String get performEmployee;

  /// No description provided for @caredQL.
  ///
  /// In en, this message translates to:
  /// **'QL đã chăm sóc'**
  String get caredQL;

  /// No description provided for @pleaseSelect.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn'**
  String get pleaseSelect;

  /// No description provided for @dateCountReturn.
  ///
  /// In en, this message translates to:
  /// **'Tái khám sau'**
  String get dateCountReturn;

  /// No description provided for @takeEmployee.
  ///
  /// In en, this message translates to:
  /// **'NV thực hiện'**
  String get takeEmployee;

  /// No description provided for @adviseEmployee.
  ///
  /// In en, this message translates to:
  /// **'NV tư vấn'**
  String get adviseEmployee;

  /// No description provided for @takeDoctor.
  ///
  /// In en, this message translates to:
  /// **'Bác sĩ thực hiện'**
  String get takeDoctor;

  /// No description provided for @medicine.
  ///
  /// In en, this message translates to:
  /// **'Thuốc'**
  String get medicine;

  /// No description provided for @dosage.
  ///
  /// In en, this message translates to:
  /// **'Liều'**
  String get dosage;

  /// No description provided for @point.
  ///
  /// In en, this message translates to:
  /// **'Điểm'**
  String get point;

  /// No description provided for @avgPointByOthers.
  ///
  /// In en, this message translates to:
  /// **'Điểm trung bình (người khác đánh giá)'**
  String get avgPointByOthers;

  /// No description provided for @khacnho.
  ///
  /// In en, this message translates to:
  /// **'Khấc nhỏ'**
  String get khacnho;

  /// No description provided for @differentGap.
  ///
  /// In en, this message translates to:
  /// **'Chênh lệch'**
  String get differentGap;

  /// No description provided for @reExaminateAfter.
  ///
  /// In en, this message translates to:
  /// **'Tái khám sau'**
  String get reExaminateAfter;

  /// No description provided for @machine.
  ///
  /// In en, this message translates to:
  /// **'Máy'**
  String get machine;

  /// No description provided for @spotsise.
  ///
  /// In en, this message translates to:
  /// **'Spotsise'**
  String get spotsise;

  /// No description provided for @originStatus.
  ///
  /// In en, this message translates to:
  /// **'TTBD'**
  String get originStatus;

  /// No description provided for @inkColor.
  ///
  /// In en, this message translates to:
  /// **'Màu mực'**
  String get inkColor;

  /// No description provided for @prescriptionTemplate.
  ///
  /// In en, this message translates to:
  /// **'Toa thuốc mẫu'**
  String get prescriptionTemplate;

  /// No description provided for @pleaseSelectPrescriptionTemplate.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn toa thuốc mẫu'**
  String get pleaseSelectPrescriptionTemplate;

  /// No description provided for @list.
  ///
  /// In en, this message translates to:
  /// **'Danh sách'**
  String get list;

  /// No description provided for @diagnosis.
  ///
  /// In en, this message translates to:
  /// **'Chuẩn đoán'**
  String get diagnosis;

  /// No description provided for @advice.
  ///
  /// In en, this message translates to:
  /// **'Lời dặn'**
  String get advice;

  /// No description provided for @prescriber.
  ///
  /// In en, this message translates to:
  /// **'Người kê toa'**
  String get prescriber;

  /// No description provided for @usage.
  ///
  /// In en, this message translates to:
  /// **'Cách dùng'**
  String get usage;

  /// No description provided for @unit.
  ///
  /// In en, this message translates to:
  /// **'Đơn vị tính'**
  String get unit;

  /// No description provided for @addMedicine.
  ///
  /// In en, this message translates to:
  /// **'Thêm thuốc mới'**
  String get addMedicine;

  /// No description provided for @addMedicineForms.
  ///
  /// In en, this message translates to:
  /// **'Thêm thuốc theo mẫu'**
  String get addMedicineForms;

  /// No description provided for @addContent.
  ///
  /// In en, this message translates to:
  /// **'Nhập nội dung...'**
  String get addContent;

  /// No description provided for @action.
  ///
  /// In en, this message translates to:
  /// **'Thao tác'**
  String get action;

  /// No description provided for @netFare.
  ///
  /// In en, this message translates to:
  /// **'Đơn giá'**
  String get netFare;

  /// No description provided for @discount.
  ///
  /// In en, this message translates to:
  /// **'Giảm giá'**
  String get discount;

  /// No description provided for @finalCost.
  ///
  /// In en, this message translates to:
  /// **'Thành tiền'**
  String get finalCost;

  /// No description provided for @trackingTransparency.
  ///
  /// In en, this message translates to:
  /// **'Xin phép được theo dõi hành vi người dùng ở màn hình kế tiếp để:'**
  String get trackingTransparency;

  /// No description provided for @locationRequest1.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật tin tức, dịch vụ chính xác hơn'**
  String get locationRequest1;

  /// No description provided for @checkedOut.
  ///
  /// In en, this message translates to:
  /// **'Đã về'**
  String get checkedOut;

  /// No description provided for @orderFood.
  ///
  /// In en, this message translates to:
  /// **'Đặt cơm'**
  String get orderFood;

  /// No description provided for @morningSession.
  ///
  /// In en, this message translates to:
  /// **'Sáng'**
  String get morningSession;

  /// No description provided for @noonSession.
  ///
  /// In en, this message translates to:
  /// **'Trưa'**
  String get noonSession;

  /// No description provided for @afternoonSession.
  ///
  /// In en, this message translates to:
  /// **'Chiều'**
  String get afternoonSession;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật'**
  String get update;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Thành công'**
  String get success;

  /// No description provided for @orderFoodUpdateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật đặt cơm thành công'**
  String get orderFoodUpdateSuccess;

  /// No description provided for @overAmount.
  ///
  /// In en, this message translates to:
  /// **'Số lượng sử dụng đã quá số lượng còn lại'**
  String get overAmount;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Thứ 2'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Thứ 3'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Thứ 4'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thứ 5'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Thứ 6'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Thứ 7'**
  String get saturday;

  /// No description provided for @sunDay.
  ///
  /// In en, this message translates to:
  /// **'Chủ Nhật'**
  String get sunDay;

  /// No description provided for @nation.
  ///
  /// In en, this message translates to:
  /// **'Quốc gia'**
  String get nation;

  /// No description provided for @visitReason.
  ///
  /// In en, this message translates to:
  /// **'Biết Ngọc Dung từ đâu ?'**
  String get visitReason;

  /// No description provided for @introducer.
  ///
  /// In en, this message translates to:
  /// **'Người giới thiệu'**
  String get introducer;

  /// No description provided for @firstVisit.
  ///
  /// In en, this message translates to:
  /// **'Ngày đầu đến'**
  String get firstVisit;

  /// No description provided for @lastVisit.
  ///
  /// In en, this message translates to:
  /// **'Ngày cuối đến'**
  String get lastVisit;

  /// No description provided for @scheduleInfo.
  ///
  /// In en, this message translates to:
  /// **'Thông tin đặt hẹn'**
  String get scheduleInfo;

  /// No description provided for @customerCharacteristics.
  ///
  /// In en, this message translates to:
  /// **'Đặc điểm của khách hàng'**
  String get customerCharacteristics;

  /// No description provided for @currentWeek.
  ///
  /// In en, this message translates to:
  /// **'Tuần hiện tại'**
  String get currentWeek;

  /// No description provided for @optionQuestion.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn thực hiện thao tác?'**
  String get optionQuestion;

  /// No description provided for @assignTask.
  ///
  /// In en, this message translates to:
  /// **'Gán CV'**
  String get assignTask;

  /// No description provided for @bookingSchedule.
  ///
  /// In en, this message translates to:
  /// **'Lịch đặt hẹn'**
  String get bookingSchedule;

  /// No description provided for @information.
  ///
  /// In en, this message translates to:
  /// **'Thông tin'**
  String get information;

  /// No description provided for @bookedService.
  ///
  /// In en, this message translates to:
  /// **'DV đã đặt hẹn'**
  String get bookedService;

  /// No description provided for @noVisit.
  ///
  /// In en, this message translates to:
  /// **'Số lần đến'**
  String get noVisit;

  /// No description provided for @firstVisited.
  ///
  /// In en, this message translates to:
  /// **'Lần đầu đến'**
  String get firstVisited;

  /// No description provided for @lastVisited.
  ///
  /// In en, this message translates to:
  /// **'Đến gần đây'**
  String get lastVisited;

  /// No description provided for @balanceMoney.
  ///
  /// In en, this message translates to:
  /// **'Số dư thẻ'**
  String get balanceMoney;

  /// No description provided for @lastMoney.
  ///
  /// In en, this message translates to:
  /// **'Chi gần đây'**
  String get lastMoney;

  /// No description provided for @totalSpent.
  ///
  /// In en, this message translates to:
  /// **'Tổng chi'**
  String get totalSpent;

  /// No description provided for @avgRevenue.
  ///
  /// In en, this message translates to:
  /// **'DT bình quân'**
  String get avgRevenue;

  /// No description provided for @takeCareEmpName.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên chăm sóc'**
  String get takeCareEmpName;

  /// No description provided for @used.
  ///
  /// In en, this message translates to:
  /// **'Đã sử dụng'**
  String get used;

  /// No description provided for @unUsed.
  ///
  /// In en, this message translates to:
  /// **'Chưa sử dụng'**
  String get unUsed;

  /// No description provided for @customerHaveBook.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng đặt lịch hẹn'**
  String get customerHaveBook;

  /// No description provided for @noCheckin.
  ///
  /// In en, this message translates to:
  /// **'Chưa đến'**
  String get noCheckin;

  /// No description provided for @hasCheckin.
  ///
  /// In en, this message translates to:
  /// **'Đã đến'**
  String get hasCheckin;

  /// No description provided for @beingTakeCare.
  ///
  /// In en, this message translates to:
  /// **'Đang chăm sóc'**
  String get beingTakeCare;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Hôm nay'**
  String get today;

  /// No description provided for @tomorrow.
  ///
  /// In en, this message translates to:
  /// **'Ngày mai'**
  String get tomorrow;

  /// No description provided for @treatmentAvailable.
  ///
  /// In en, this message translates to:
  /// **'Còn liệu trình'**
  String get treatmentAvailable;

  /// No description provided for @notAssignTask.
  ///
  /// In en, this message translates to:
  /// **'Chưa gán CV'**
  String get notAssignTask;

  /// No description provided for @assignedTask.
  ///
  /// In en, this message translates to:
  /// **'Đã gán CV'**
  String get assignedTask;

  /// No description provided for @customerAssign.
  ///
  /// In en, this message translates to:
  /// **'{total} KH được chọn'**
  String customerAssign(String total);

  /// No description provided for @deleteAssignTaskMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn hủy công việc đã gán?'**
  String get deleteAssignTaskMessage;

  /// No description provided for @sendFailure.
  ///
  /// In en, this message translates to:
  /// **'Gửi thất bại'**
  String get sendFailure;

  /// No description provided for @sendNoteSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã gửi thông tin thành công'**
  String get sendNoteSuccess;

  /// No description provided for @input.
  ///
  /// In en, this message translates to:
  /// **'Nhập'**
  String get input;

  /// No description provided for @bookingService.
  ///
  /// In en, this message translates to:
  /// **'Dịch vụ đặt hẹn'**
  String get bookingService;

  /// No description provided for @bookingNote.
  ///
  /// In en, this message translates to:
  /// **'Ghi chú đặt hẹn'**
  String get bookingNote;

  /// No description provided for @pasteToConsultation.
  ///
  /// In en, this message translates to:
  /// **'Dán vào nội dung tư vấn'**
  String get pasteToConsultation;

  /// No description provided for @printTicket.
  ///
  /// In en, this message translates to:
  /// **'In phiếu'**
  String get printTicket;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'Không có dữ liệu'**
  String get noData;

  /// No description provided for @pullToLoadMore.
  ///
  /// In en, this message translates to:
  /// **'Kéo để tải thêm'**
  String get pullToLoadMore;

  /// No description provided for @checkinList.
  ///
  /// In en, this message translates to:
  /// **'Danh sách check-in'**
  String get checkinList;

  /// No description provided for @notAssigned.
  ///
  /// In en, this message translates to:
  /// **'Chưa gán'**
  String get notAssigned;

  /// No description provided for @assigned.
  ///
  /// In en, this message translates to:
  /// **'Đã gán'**
  String get assigned;

  /// No description provided for @perform.
  ///
  /// In en, this message translates to:
  /// **'Thực hiện'**
  String get perform;

  /// No description provided for @taskList.
  ///
  /// In en, this message translates to:
  /// **'Danh sách công việc'**
  String get taskList;

  /// No description provided for @collectInformation.
  ///
  /// In en, this message translates to:
  /// **'Thu thập thông tin'**
  String get collectInformation;

  /// No description provided for @boughtServices.
  ///
  /// In en, this message translates to:
  /// **'{count} dịch vụ đã mua'**
  String boughtServices(int count);

  /// No description provided for @assignWork.
  ///
  /// In en, this message translates to:
  /// **'Gán công việc'**
  String get assignWork;

  /// No description provided for @selecting.
  ///
  /// In en, this message translates to:
  /// **'{count} đang chọn'**
  String selecting(int count);

  /// No description provided for @onlyAssignMax.
  ///
  /// In en, this message translates to:
  /// **'Chỉ được gán tối đa {count} nhân viên'**
  String onlyAssignMax(int count);

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Chọn'**
  String get select;

  /// No description provided for @confirmFinishTask.
  ///
  /// In en, this message translates to:
  /// **'Bạn chắc chắn đã hoàn tất công việc?'**
  String get confirmFinishTask;

  /// No description provided for @tutorial.
  ///
  /// In en, this message translates to:
  /// **'Hướng dẫn'**
  String get tutorial;

  /// No description provided for @takePictureBefore.
  ///
  /// In en, this message translates to:
  /// **'Chụp hình trước làm'**
  String get takePictureBefore;

  /// No description provided for @takePictureAfter.
  ///
  /// In en, this message translates to:
  /// **'Chụp hình sau làm'**
  String get takePictureAfter;

  /// No description provided for @warningFinish.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng thực hiện đầy đủ các công việc để hoàn tất'**
  String get warningFinish;

  /// No description provided for @warningFinish2.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng thực hiện ít nhất một công việc để hoàn tất'**
  String get warningFinish2;

  /// No description provided for @beforeDone.
  ///
  /// In en, this message translates to:
  /// **'Trước làm'**
  String get beforeDone;

  /// No description provided for @afterDone.
  ///
  /// In en, this message translates to:
  /// **'Sau làm'**
  String get afterDone;

  /// No description provided for @attachImages.
  ///
  /// In en, this message translates to:
  /// **'Đính kèm hình ảnh'**
  String get attachImages;

  /// No description provided for @customerCompleteServiceConfirm.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn khách hàng đã hoàn tất dịch vụ?'**
  String get customerCompleteServiceConfirm;

  /// No description provided for @searchEmployee.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên, mã nhân viên ...'**
  String get searchEmployee;

  /// No description provided for @requiredSelectRoom.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn phòng'**
  String get requiredSelectRoom;

  /// No description provided for @getLocationSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Lấy thông tin vị trí hoàn tất'**
  String get getLocationSuccessful;

  /// No description provided for @microRequest3.
  ///
  /// In en, this message translates to:
  /// **'Thu thập cải thiện chất lượng dịch vụ'**
  String get microRequest3;

  /// No description provided for @province.
  ///
  /// In en, this message translates to:
  /// **'Tỉnh/Thành phố'**
  String get province;

  /// No description provided for @district.
  ///
  /// In en, this message translates to:
  /// **'Quận/Huyện'**
  String get district;

  /// No description provided for @ward.
  ///
  /// In en, this message translates to:
  /// **'Phường/Xã'**
  String get ward;

  /// No description provided for @male.
  ///
  /// In en, this message translates to:
  /// **'Nam'**
  String get male;

  /// No description provided for @female.
  ///
  /// In en, this message translates to:
  /// **'Nữ'**
  String get female;

  /// No description provided for @selectGender.
  ///
  /// In en, this message translates to:
  /// **'Chọn giới tính'**
  String get selectGender;

  /// No description provided for @selectJob.
  ///
  /// In en, this message translates to:
  /// **'Chọn nghề nghiệp'**
  String get selectJob;

  /// No description provided for @branch.
  ///
  /// In en, this message translates to:
  /// **'Chi nhánh'**
  String get branch;

  /// No description provided for @addCustomer.
  ///
  /// In en, this message translates to:
  /// **'Thêm khách hàng'**
  String get addCustomer;

  /// No description provided for @addCustomerSuccess.
  ///
  /// In en, this message translates to:
  /// **'Thêm khách hàng thành công'**
  String get addCustomerSuccess;

  /// No description provided for @addNew.
  ///
  /// In en, this message translates to:
  /// **'Thêm mới'**
  String get addNew;

  /// No description provided for @endRecord.
  ///
  /// In en, this message translates to:
  /// **'Kết thúc ghi âm'**
  String get endRecord;

  /// No description provided for @recordWarning.
  ///
  /// In en, this message translates to:
  /// **'Màn hình tiếp theo sẽ thực hiện ghi âm vui lòng xác nhận để tiếp tục'**
  String get recordWarning;

  /// No description provided for @recordPerform.
  ///
  /// In en, this message translates to:
  /// **'Thực hiện ghi âm'**
  String get recordPerform;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Xóa'**
  String get delete;

  /// No description provided for @requiredField.
  ///
  /// In en, this message translates to:
  /// **'Trường bắt buộc'**
  String get requiredField;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'Tất cả'**
  String get all;

  /// No description provided for @nextService.
  ///
  /// In en, this message translates to:
  /// **'Tiếp tục dịch vụ khác'**
  String get nextService;

  /// No description provided for @changeRoom.
  ///
  /// In en, this message translates to:
  /// **'Chuyển phòng'**
  String get changeRoom;

  /// No description provided for @ttbd.
  ///
  /// In en, this message translates to:
  /// **'TTBD'**
  String get ttbd;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Chọn ngày'**
  String get selectDate;

  /// No description provided for @selectContent.
  ///
  /// In en, this message translates to:
  /// **'Chọn nội dung'**
  String get selectContent;

  /// No description provided for @searchCustomerPhone.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo số điện thoại KH'**
  String get searchCustomerPhone;

  /// No description provided for @manualCheckin.
  ///
  /// In en, this message translates to:
  /// **'Checkin thủ công'**
  String get manualCheckin;

  /// No description provided for @editCustomerInfo.
  ///
  /// In en, this message translates to:
  /// **'Sửa thông tin khách hàng'**
  String get editCustomerInfo;

  /// No description provided for @room.
  ///
  /// In en, this message translates to:
  /// **'Phòng {roomCode}'**
  String room(String roomCode);

  /// No description provided for @pickStaff.
  ///
  /// In en, this message translates to:
  /// **'Chọn nhân viên'**
  String get pickStaff;

  /// No description provided for @bed.
  ///
  /// In en, this message translates to:
  /// **'Giường'**
  String get bed;

  /// No description provided for @consultation.
  ///
  /// In en, this message translates to:
  /// **'Tư vấn'**
  String get consultation;

  /// No description provided for @updateConsultation.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật tư vấn'**
  String get updateConsultation;

  /// No description provided for @technical.
  ///
  /// In en, this message translates to:
  /// **'Kỹ thuật'**
  String get technical;

  /// No description provided for @consultationCost.
  ///
  /// In en, this message translates to:
  /// **'Tiền nhận tư vấn'**
  String get consultationCost;

  /// No description provided for @notificationBot.
  ///
  /// In en, this message translates to:
  /// **'Thông báo bot'**
  String get notificationBot;

  /// No description provided for @affective.
  ///
  /// In en, this message translates to:
  /// **'Hiệu quả'**
  String get affective;

  /// No description provided for @nonAffective.
  ///
  /// In en, this message translates to:
  /// **'Không hiệu quả'**
  String get nonAffective;

  /// No description provided for @adviseContinuous.
  ///
  /// In en, this message translates to:
  /// **'Tư vấn tiếp'**
  String get adviseContinuous;

  /// No description provided for @minNetFareError.
  ///
  /// In en, this message translates to:
  /// **'Không được phép thấp hơn giá ban đầu'**
  String get minNetFareError;

  /// No description provided for @supportConsultation.
  ///
  /// In en, this message translates to:
  /// **'Hỗ trợ tư vấn'**
  String get supportConsultation;

  /// No description provided for @selfConsultation.
  ///
  /// In en, this message translates to:
  /// **'Tự tư vấn'**
  String get selfConsultation;

  /// No description provided for @selectConsultation.
  ///
  /// In en, this message translates to:
  /// **'Chọn hình thức tư vấn'**
  String get selectConsultation;

  /// No description provided for @notConsultation.
  ///
  /// In en, this message translates to:
  /// **'Chưa tư vấn'**
  String get notConsultation;

  /// No description provided for @didConsultation.
  ///
  /// In en, this message translates to:
  /// **'Đã tư vấn'**
  String get didConsultation;

  /// No description provided for @recept.
  ///
  /// In en, this message translates to:
  /// **'Tiếp nhận'**
  String get recept;

  /// No description provided for @evaluationPeriodList.
  ///
  /// In en, this message translates to:
  /// **'Danh sách kỳ đánh giá'**
  String get evaluationPeriodList;

  /// No description provided for @noEvaluationPeriodAvailable.
  ///
  /// In en, this message translates to:
  /// **'Chưa tới kỳ đánh giá'**
  String get noEvaluationPeriodAvailable;

  /// No description provided for @evaluationResult.
  ///
  /// In en, this message translates to:
  /// **'Kết quả đánh giá'**
  String get evaluationResult;

  /// No description provided for @evaluation.
  ///
  /// In en, this message translates to:
  /// **'Đánh giá'**
  String get evaluation;

  /// No description provided for @completeRating.
  ///
  /// In en, this message translates to:
  /// **'Hoàn thành đánh giá?'**
  String get completeRating;

  /// No description provided for @questionList.
  ///
  /// In en, this message translates to:
  /// **'Danh sách câu hỏi'**
  String get questionList;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Quay lại'**
  String get previous;

  /// No description provided for @questionNumber.
  ///
  /// In en, this message translates to:
  /// **'Câu hỏi'**
  String get questionNumber;

  /// No description provided for @consultationContentShort.
  ///
  /// In en, this message translates to:
  /// **'Nội dung TV'**
  String get consultationContentShort;

  /// No description provided for @callLogs.
  ///
  /// In en, this message translates to:
  /// **'LS cuộc gọi'**
  String get callLogs;

  /// No description provided for @call.
  ///
  /// In en, this message translates to:
  /// **'Gọi điện'**
  String get call;

  /// No description provided for @messageLogs.
  ///
  /// In en, this message translates to:
  /// **'LS tin nhắn'**
  String get messageLogs;

  /// No description provided for @bookingLogs.
  ///
  /// In en, this message translates to:
  /// **'LS đặt hẹn'**
  String get bookingLogs;

  /// No description provided for @comeDateTime.
  ///
  /// In en, this message translates to:
  /// **'Ngày giờ đến'**
  String get comeDateTime;

  /// No description provided for @bookSource.
  ///
  /// In en, this message translates to:
  /// **'Đặt từ'**
  String get bookSource;

  /// No description provided for @copied.
  ///
  /// In en, this message translates to:
  /// **'Sao chép'**
  String get copied;

  /// No description provided for @advisoryTicketStatus.
  ///
  /// In en, this message translates to:
  /// **'Trạng thái phiếu ghi'**
  String get advisoryTicketStatus;

  /// No description provided for @skillsNeedToImprove.
  ///
  /// In en, this message translates to:
  /// **'Các năng lực cần cải thiện'**
  String get skillsNeedToImprove;

  /// No description provided for @emptyEvaluationResult.
  ///
  /// In en, this message translates to:
  /// **'Chưa có kết quả đánh giá'**
  String get emptyEvaluationResult;

  /// No description provided for @ratingSuccess.
  ///
  /// In en, this message translates to:
  /// **'Gửi đánh giá thành công'**
  String get ratingSuccess;

  /// No description provided for @ratingFailure.
  ///
  /// In en, this message translates to:
  /// **'Không lưu được dữ liệu'**
  String get ratingFailure;

  /// No description provided for @tabNew.
  ///
  /// In en, this message translates to:
  /// **'Mới'**
  String get tabNew;

  /// No description provided for @tabPending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get tabPending;

  /// No description provided for @tabSuccess.
  ///
  /// In en, this message translates to:
  /// **'Đã xử lý'**
  String get tabSuccess;

  /// No description provided for @searchHint.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên,sđt, ngày gọi'**
  String get searchHint;

  /// No description provided for @ticketFilter.
  ///
  /// In en, this message translates to:
  /// **'Lọc phiếu ghi'**
  String get ticketFilter;

  /// No description provided for @statusTicket.
  ///
  /// In en, this message translates to:
  /// **'Trạng thái phiếu ghi'**
  String get statusTicket;

  /// No description provided for @dateFollow.
  ///
  /// In en, this message translates to:
  /// **'Ngày tương tác'**
  String get dateFollow;

  /// No description provided for @dateCreate.
  ///
  /// In en, this message translates to:
  /// **'Ngày tạo'**
  String get dateCreate;

  /// No description provided for @titleTimeInit.
  ///
  /// In en, this message translates to:
  /// **'- Chọn ngày -'**
  String get titleTimeInit;

  /// No description provided for @titleStatusInit.
  ///
  /// In en, this message translates to:
  /// **'- Chọn trạng thái -'**
  String get titleStatusInit;

  /// No description provided for @isEnd.
  ///
  /// In en, this message translates to:
  /// **'- Đã hết -'**
  String get isEnd;

  /// No description provided for @accessReject.
  ///
  /// In en, this message translates to:
  /// **'Từ chối truy cập'**
  String get accessReject;

  /// No description provided for @booking.
  ///
  /// In en, this message translates to:
  /// **'Đặt hẹn'**
  String get booking;

  /// No description provided for @fromPhone.
  ///
  /// In en, this message translates to:
  /// **'Số gọi ra'**
  String get fromPhone;

  /// No description provided for @customerPhone.
  ///
  /// In en, this message translates to:
  /// **'Số khách hàng'**
  String get customerPhone;

  /// No description provided for @call2.
  ///
  /// In en, this message translates to:
  /// **'Gọi'**
  String get call2;

  /// No description provided for @connecting.
  ///
  /// In en, this message translates to:
  /// **'Đang kết nối...'**
  String get connecting;

  /// No description provided for @end.
  ///
  /// In en, this message translates to:
  /// **'Kết thúc'**
  String get end;

  /// No description provided for @userTicket.
  ///
  /// In en, this message translates to:
  /// **'Danh sách phiếu ghi'**
  String get userTicket;

  /// No description provided for @comeDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày đến'**
  String get comeDate;

  /// No description provided for @comeTime.
  ///
  /// In en, this message translates to:
  /// **'Thời gian đến'**
  String get comeTime;

  /// No description provided for @totalBooking.
  ///
  /// In en, this message translates to:
  /// **'Tổng lịch đã đặt'**
  String get totalBooking;

  /// No description provided for @detailService2.
  ///
  /// In en, this message translates to:
  /// **'DV chi tiết'**
  String get detailService2;

  /// No description provided for @sendMethod.
  ///
  /// In en, this message translates to:
  /// **'Hình thức gửi'**
  String get sendMethod;

  /// No description provided for @sendRecordQuestion.
  ///
  /// In en, this message translates to:
  /// **'Bạn có muốn gửi ghi âm ?'**
  String get sendRecordQuestion;

  /// No description provided for @pleaseSelectDate.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn ngày đặt lịch'**
  String get pleaseSelectDate;

  /// No description provided for @planSchedule.
  ///
  /// In en, this message translates to:
  /// **'Lịch kế hoạch'**
  String get planSchedule;

  /// No description provided for @sendSMS.
  ///
  /// In en, this message translates to:
  /// **'Gửi SMS'**
  String get sendSMS;

  /// No description provided for @sendZNS.
  ///
  /// In en, this message translates to:
  /// **'Gửi ZNS'**
  String get sendZNS;

  /// No description provided for @sendAppNoti.
  ///
  /// In en, this message translates to:
  /// **'Gửi Noti app'**
  String get sendAppNoti;

  /// No description provided for @callTo.
  ///
  /// In en, this message translates to:
  /// **'Gọi đến'**
  String get callTo;

  /// No description provided for @cancelAll.
  ///
  /// In en, this message translates to:
  /// **'Hủy tất cả'**
  String get cancelAll;

  /// No description provided for @createBookingWarning.
  ///
  /// In en, this message translates to:
  /// **'Bạn có một lịch kế hoạch chưa được chọn. Bỏ qua?'**
  String get createBookingWarning;

  /// No description provided for @bookingSuccess.
  ///
  /// In en, this message translates to:
  /// **'Đặt lịch thành công'**
  String get bookingSuccess;

  /// No description provided for @serviceHasExist.
  ///
  /// In en, this message translates to:
  /// **'Dịch vụ đã tồn tại'**
  String get serviceHasExist;

  /// No description provided for @consultationCreate.
  ///
  /// In en, this message translates to:
  /// **'Tạo tư vấn'**
  String get consultationCreate;

  /// No description provided for @updateBookingSuccess.
  ///
  /// In en, this message translates to:
  /// **'Đã cập nhật lịch hẹn thành công'**
  String get updateBookingSuccess;

  /// No description provided for @updateNDTVSuccess.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật NDTV thành công'**
  String get updateNDTVSuccess;

  /// No description provided for @selectProduct.
  ///
  /// In en, this message translates to:
  /// **'Chọn sản phẩm'**
  String get selectProduct;

  /// No description provided for @selectMethod.
  ///
  /// In en, this message translates to:
  /// **'Chọn phương thức'**
  String get selectMethod;

  /// No description provided for @selectUnit.
  ///
  /// In en, this message translates to:
  /// **'Chọn đơn vị tính'**
  String get selectUnit;

  /// No description provided for @notificationType.
  ///
  /// In en, this message translates to:
  /// **'Loại tin thông báo'**
  String get notificationType;

  /// No description provided for @buocSong.
  ///
  /// In en, this message translates to:
  /// **'Bước sóng'**
  String get buocSong;

  /// No description provided for @cacBuoc.
  ///
  /// In en, this message translates to:
  /// **'Các bước'**
  String get cacBuoc;

  /// No description provided for @dauMay.
  ///
  /// In en, this message translates to:
  /// **'Đầu máy'**
  String get dauMay;

  /// No description provided for @postsize.
  ///
  /// In en, this message translates to:
  /// **'Spotsize'**
  String get postsize;

  /// No description provided for @shot.
  ///
  /// In en, this message translates to:
  /// **'Shot'**
  String get shot;

  /// No description provided for @vung.
  ///
  /// In en, this message translates to:
  /// **'Vùng'**
  String get vung;

  /// No description provided for @tanso.
  ///
  /// In en, this message translates to:
  /// **'Tần số'**
  String get tanso;

  /// No description provided for @nangluong.
  ///
  /// In en, this message translates to:
  /// **'Năng lượng'**
  String get nangluong;

  /// No description provided for @energyRate.
  ///
  /// In en, this message translates to:
  /// **'Mức năng lượng'**
  String get energyRate;

  /// No description provided for @specs.
  ///
  /// In en, this message translates to:
  /// **'Thông số'**
  String get specs;

  /// No description provided for @drinkProduct.
  ///
  /// In en, this message translates to:
  /// **'Sản phẩm uống'**
  String get drinkProduct;

  /// No description provided for @latherProduct.
  ///
  /// In en, this message translates to:
  /// **'Sản phẩm bôi'**
  String get latherProduct;

  /// No description provided for @drinkAndLatherProductOfFirm.
  ///
  /// In en, this message translates to:
  /// **'Sản phẩm bôi và uống của TMV'**
  String get drinkAndLatherProductOfFirm;

  /// No description provided for @drinkAndLatherProductOfClient.
  ///
  /// In en, this message translates to:
  /// **'Sản phẩm bôi và uống của KH'**
  String get drinkAndLatherProductOfClient;

  /// No description provided for @sang.
  ///
  /// In en, this message translates to:
  /// **'Sáng'**
  String get sang;

  /// No description provided for @trua.
  ///
  /// In en, this message translates to:
  /// **'Trưa'**
  String get trua;

  /// No description provided for @chieu.
  ///
  /// In en, this message translates to:
  /// **'Chiều'**
  String get chieu;

  /// No description provided for @search2.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo từ khoá'**
  String get search2;

  /// No description provided for @intro.
  ///
  /// In en, this message translates to:
  /// **'Hướng dẫn sử dụng'**
  String get intro;

  /// No description provided for @selectLather.
  ///
  /// In en, this message translates to:
  /// **'Chọn sản phẩm bôi'**
  String get selectLather;

  /// No description provided for @selectDrink.
  ///
  /// In en, this message translates to:
  /// **'Chọn sản phẩm uống'**
  String get selectDrink;

  /// No description provided for @hintNotes.
  ///
  /// In en, this message translates to:
  /// **'Viết ghi chú...'**
  String get hintNotes;

  /// No description provided for @hintTotal.
  ///
  /// In en, this message translates to:
  /// **'Nhập số lượng...'**
  String get hintTotal;

  /// No description provided for @titleTotal.
  ///
  /// In en, this message translates to:
  /// **'Số lượng'**
  String get titleTotal;

  /// No description provided for @selectWorking.
  ///
  /// In en, this message translates to:
  /// **'Chọn công việc'**
  String get selectWorking;

  /// No description provided for @nvtv.
  ///
  /// In en, this message translates to:
  /// **'NVTV'**
  String get nvtv;

  /// No description provided for @nvth.
  ///
  /// In en, this message translates to:
  /// **'NVTH'**
  String get nvth;

  /// No description provided for @bsth.
  ///
  /// In en, this message translates to:
  /// **'BSTH'**
  String get bsth;

  /// No description provided for @sl.
  ///
  /// In en, this message translates to:
  /// **'SL'**
  String get sl;

  /// No description provided for @times.
  ///
  /// In en, this message translates to:
  /// **'Lần'**
  String get times;

  /// No description provided for @devFeatures.
  ///
  /// In en, this message translates to:
  /// **'Tính năng đang phát triển'**
  String get devFeatures;

  /// No description provided for @reasonVisit.
  ///
  /// In en, this message translates to:
  /// **'Biết Ngọc Dung từ đâu ?'**
  String get reasonVisit;

  /// No description provided for @titleDay.
  ///
  /// In en, this message translates to:
  /// **'Ngày'**
  String get titleDay;

  /// No description provided for @setCheckinTime.
  ///
  /// In en, this message translates to:
  /// **'Cài đặt giờ chấm công vào'**
  String get setCheckinTime;

  /// No description provided for @setCheckoutTime.
  ///
  /// In en, this message translates to:
  /// **'Cài đặt giờ chấm công ra'**
  String get setCheckoutTime;

  /// No description provided for @setupCheckin.
  ///
  /// In en, this message translates to:
  /// **'Cài đặt chấm công'**
  String get setupCheckin;

  /// No description provided for @exampleNote.
  ///
  /// In en, this message translates to:
  /// **'ví dụ: Ăn no trước khi uống'**
  String get exampleNote;

  /// No description provided for @technicalQL.
  ///
  /// In en, this message translates to:
  /// **'QL tay nghề'**
  String get technicalQL;

  /// No description provided for @recordCheckingStaff.
  ///
  /// In en, this message translates to:
  /// **'NV kiểm tra hồ sơ'**
  String get recordCheckingStaff;

  /// No description provided for @stopRecordConfirm.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn dừng ghi âm?'**
  String get stopRecordConfirm;

  /// No description provided for @stopRecordSuccess.
  ///
  /// In en, this message translates to:
  /// **'Chúc mừng bạn đã hoàn tất ghi âm'**
  String get stopRecordSuccess;

  /// No description provided for @printingProgress.
  ///
  /// In en, this message translates to:
  /// **'Đang xử lý: {value}%'**
  String printingProgress(String value);

  /// No description provided for @pleaseInputDescription.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập miêu tả'**
  String get pleaseInputDescription;

  /// No description provided for @searchByCodeOrName.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo mã KH hoặc tên KH'**
  String get searchByCodeOrName;

  /// No description provided for @diary.
  ///
  /// In en, this message translates to:
  /// **'MXH'**
  String get diary;

  /// No description provided for @explore.
  ///
  /// In en, this message translates to:
  /// **'Khám phá'**
  String get explore;

  /// No description provided for @internal.
  ///
  /// In en, this message translates to:
  /// **'Nội bộ'**
  String get internal;

  /// No description provided for @recentlyChat.
  ///
  /// In en, this message translates to:
  /// **'vừa mới chat'**
  String get recentlyChat;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Tin nhắn'**
  String get message;

  /// No description provided for @groupName.
  ///
  /// In en, this message translates to:
  /// **'Tên nhóm'**
  String get groupName;

  /// No description provided for @createGroup.
  ///
  /// In en, this message translates to:
  /// **'Tạo nhóm'**
  String get createGroup;

  /// No description provided for @customer.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng'**
  String get customer;

  /// No description provided for @timeAttendanceNow.
  ///
  /// In en, this message translates to:
  /// **'Chấm công ngay'**
  String get timeAttendanceNow;

  /// No description provided for @cameraAgain.
  ///
  /// In en, this message translates to:
  /// **'Chụp lại ảnh'**
  String get cameraAgain;

  /// No description provided for @imageAttendance.
  ///
  /// In en, this message translates to:
  /// **'Ảnh chấm công'**
  String get imageAttendance;

  /// No description provided for @inputReason.
  ///
  /// In en, this message translates to:
  /// **'Nhập lý do'**
  String get inputReason;

  /// No description provided for @typeAction.
  ///
  /// In en, this message translates to:
  /// **'Chọn loại thao tác'**
  String get typeAction;

  /// No description provided for @dayApply.
  ///
  /// In en, this message translates to:
  /// **'Ngày áp dụng'**
  String get dayApply;

  /// No description provided for @fromDayToDay.
  ///
  /// In en, this message translates to:
  /// **'Từ ngày đến ngày'**
  String get fromDayToDay;

  /// No description provided for @personInfo.
  ///
  /// In en, this message translates to:
  /// **'Thông tin tài khoản'**
  String get personInfo;

  /// No description provided for @bio.
  ///
  /// In en, this message translates to:
  /// **'Bio'**
  String get bio;

  /// No description provided for @hintBio.
  ///
  /// In en, this message translates to:
  /// **'Mô tả bản thân'**
  String get hintBio;

  /// No description provided for @nickName.
  ///
  /// In en, this message translates to:
  /// **'NickName'**
  String get nickName;

  /// No description provided for @hintNickName.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật nickname...'**
  String get hintNickName;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Hình ảnh/Video'**
  String get gallery;

  /// No description provided for @limitImage.
  ///
  /// In en, this message translates to:
  /// **'Chọn thêm ảnh cho phép'**
  String get limitImage;

  /// No description provided for @changePermission.
  ///
  /// In en, this message translates to:
  /// **'cài đặt lại quyền'**
  String get changePermission;

  /// No description provided for @limitImageIOS.
  ///
  /// In en, this message translates to:
  /// **'Chọn lại ảnh cho phép'**
  String get limitImageIOS;

  /// No description provided for @previewImage.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật ảnh đại diện'**
  String get previewImage;

  /// No description provided for @andOthers.
  ///
  /// In en, this message translates to:
  /// **'và {count} người khác'**
  String andOthers(int count);

  /// No description provided for @groupDetail.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết nhóm'**
  String get groupDetail;

  /// No description provided for @createGroupSuccess.
  ///
  /// In en, this message translates to:
  /// **'Tạo nhóm thành công'**
  String get createGroupSuccess;

  /// No description provided for @msnv.
  ///
  /// In en, this message translates to:
  /// **'MSNV'**
  String get msnv;

  /// No description provided for @requiredGroupName.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập tên nhóm'**
  String get requiredGroupName;

  /// No description provided for @minuteAgo.
  ///
  /// In en, this message translates to:
  /// **'phút trước'**
  String get minuteAgo;

  /// No description provided for @hoursAgo.
  ///
  /// In en, this message translates to:
  /// **'giờ trước'**
  String get hoursAgo;

  /// No description provided for @now.
  ///
  /// In en, this message translates to:
  /// **'Ngay bây giờ'**
  String get now;

  /// No description provided for @like.
  ///
  /// In en, this message translates to:
  /// **'Thích'**
  String get like;

  /// No description provided for @reply.
  ///
  /// In en, this message translates to:
  /// **'Phản hồi'**
  String get reply;

  /// No description provided for @boss.
  ///
  /// In en, this message translates to:
  /// **'Sếp'**
  String get boss;

  /// No description provided for @eventsNews.
  ///
  /// In en, this message translates to:
  /// **'Event'**
  String get eventsNews;

  /// No description provided for @viewLike.
  ///
  /// In en, this message translates to:
  /// **'Xem lượt thích'**
  String get viewLike;

  /// No description provided for @titleLike.
  ///
  /// In en, this message translates to:
  /// **'Lượt thích'**
  String get titleLike;

  /// No description provided for @statusHint.
  ///
  /// In en, this message translates to:
  /// **'Hôm nay bạn thế nào?'**
  String get statusHint;

  /// No description provided for @story.
  ///
  /// In en, this message translates to:
  /// **'Nhật ký'**
  String get story;

  /// No description provided for @messenger.
  ///
  /// In en, this message translates to:
  /// **'Nhắn tin'**
  String get messenger;

  /// No description provided for @deleteGroup.
  ///
  /// In en, this message translates to:
  /// **'Xóa nhóm'**
  String get deleteGroup;

  /// No description provided for @turnOffNoti.
  ///
  /// In en, this message translates to:
  /// **'Tắt thông báo'**
  String get turnOffNoti;

  /// No description provided for @document.
  ///
  /// In en, this message translates to:
  /// **'Tài liệu'**
  String get document;

  /// No description provided for @link.
  ///
  /// In en, this message translates to:
  /// **'Liên kết'**
  String get link;

  /// No description provided for @downloadComplete.
  ///
  /// In en, this message translates to:
  /// **'Tải xuống thành công'**
  String get downloadComplete;

  /// No description provided for @video.
  ///
  /// In en, this message translates to:
  /// **'Video'**
  String get video;

  /// No description provided for @pathNotFound.
  ///
  /// In en, this message translates to:
  /// **'Không tìm thấy đường dẫn thư mục'**
  String get pathNotFound;

  /// No description provided for @permissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Không có quyền truy cập'**
  String get permissionDenied;

  /// No description provided for @media.
  ///
  /// In en, this message translates to:
  /// **'Ảnh/video'**
  String get media;

  /// No description provided for @dayNow.
  ///
  /// In en, this message translates to:
  /// **'Hôm nay'**
  String get dayNow;

  /// No description provided for @addMember.
  ///
  /// In en, this message translates to:
  /// **'Thêm thành viên'**
  String get addMember;

  /// No description provided for @leaveGroup.
  ///
  /// In en, this message translates to:
  /// **'Rời nhóm'**
  String get leaveGroup;

  /// No description provided for @warningRemoveMember.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn xóa thành viên này?'**
  String get warningRemoveMember;

  /// No description provided for @warningLeaveGroup.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn rời nhóm?'**
  String get warningLeaveGroup;

  /// No description provided for @ruleStory.
  ///
  /// In en, this message translates to:
  /// **'Đối tượng của bài viết'**
  String get ruleStory;

  /// No description provided for @postStory.
  ///
  /// In en, this message translates to:
  /// **'Đăng'**
  String get postStory;

  /// No description provided for @titleEditOptionStory.
  ///
  /// In en, this message translates to:
  /// **'Chỉnh sửa bài viết'**
  String get titleEditOptionStory;

  /// No description provided for @titleDeleteOptionStory.
  ///
  /// In en, this message translates to:
  /// **'Xóa bài viết'**
  String get titleDeleteOptionStory;

  /// No description provided for @titleEditOptionComment.
  ///
  /// In en, this message translates to:
  /// **'Chỉnh sửa bình luận'**
  String get titleEditOptionComment;

  /// No description provided for @titleDeleteOptionComment.
  ///
  /// In en, this message translates to:
  /// **'Xóa bình luận'**
  String get titleDeleteOptionComment;

  /// No description provided for @hintContentStory.
  ///
  /// In en, this message translates to:
  /// **'Bạn đang nghĩ gì?'**
  String get hintContentStory;

  /// No description provided for @recently.
  ///
  /// In en, this message translates to:
  /// **'Gần đây'**
  String get recently;

  /// No description provided for @chatConservation.
  ///
  /// In en, this message translates to:
  /// **'Hộp thoại'**
  String get chatConservation;

  /// No description provided for @nearAddress.
  ///
  /// In en, this message translates to:
  /// **'Địa chỉ gần đây'**
  String get nearAddress;

  /// No description provided for @currentAddress.
  ///
  /// In en, this message translates to:
  /// **'Địa chỉ của bạn'**
  String get currentAddress;

  /// No description provided for @shareStaff.
  ///
  /// In en, this message translates to:
  /// **'Chia sẻ người khác'**
  String get shareStaff;

  /// No description provided for @shareAddress.
  ///
  /// In en, this message translates to:
  /// **'Chia sẻ địa chỉ'**
  String get shareAddress;

  /// No description provided for @hintFindStaff.
  ///
  /// In en, this message translates to:
  /// **'Tìm tên người'**
  String get hintFindStaff;

  /// No description provided for @hintFindAddress.
  ///
  /// In en, this message translates to:
  /// **'Tìm địa chỉ cần share'**
  String get hintFindAddress;

  /// No description provided for @isTags.
  ///
  /// In en, this message translates to:
  /// **'cùng với'**
  String get isTags;

  /// No description provided for @isLocation.
  ///
  /// In en, this message translates to:
  /// **'tại'**
  String get isLocation;

  /// No description provided for @isLocation2.
  ///
  /// In en, this message translates to:
  /// **'đang ở'**
  String get isLocation2;

  /// No description provided for @notCheckin.
  ///
  /// In en, this message translates to:
  /// **'Không chấm công'**
  String get notCheckin;

  /// No description provided for @getLocationCurrent.
  ///
  /// In en, this message translates to:
  /// **'Lấy địa chỉ hiện tại'**
  String get getLocationCurrent;

  /// No description provided for @checkin2.
  ///
  /// In en, this message translates to:
  /// **'Vào'**
  String get checkin2;

  /// No description provided for @checkout2.
  ///
  /// In en, this message translates to:
  /// **'Ra'**
  String get checkout2;

  /// No description provided for @selectTag.
  ///
  /// In en, this message translates to:
  /// **'Người đã chọn'**
  String get selectTag;

  /// No description provided for @selectAddress.
  ///
  /// In en, this message translates to:
  /// **'Địa chỉ đã chọn'**
  String get selectAddress;

  /// No description provided for @unSelect.
  ///
  /// In en, this message translates to:
  /// **'Bỏ chọn'**
  String get unSelect;

  /// No description provided for @groupOrPerson.
  ///
  /// In en, this message translates to:
  /// **'Nhóm hoặc cá nhân'**
  String get groupOrPerson;

  /// No description provided for @replyTo.
  ///
  /// In en, this message translates to:
  /// **'Trả lời {name}'**
  String replyTo(String name);

  /// No description provided for @groupCreatedRecently.
  ///
  /// In en, this message translates to:
  /// **'Nhóm vừa được tạo'**
  String get groupCreatedRecently;

  /// No description provided for @userSentObject.
  ///
  /// In en, this message translates to:
  /// **'{name} đã gửi một {object}'**
  String userSentObject(String name, String object);

  /// No description provided for @file.
  ///
  /// In en, this message translates to:
  /// **'Tập tin'**
  String get file;

  /// No description provided for @openTime.
  ///
  /// In en, this message translates to:
  /// **'Thời gian làm việc: '**
  String get openTime;

  /// No description provided for @creatingStory.
  ///
  /// In en, this message translates to:
  /// **'Đang đăng bài '**
  String get creatingStory;

  /// No description provided for @edited.
  ///
  /// In en, this message translates to:
  /// **'Đã sửa'**
  String get edited;

  /// No description provided for @storyPreviewImage.
  ///
  /// In en, this message translates to:
  /// **'Xem lại ảnh'**
  String get storyPreviewImage;

  /// No description provided for @watchImage.
  ///
  /// In en, this message translates to:
  /// **'Xem ảnh'**
  String get watchImage;

  /// No description provided for @updateAvatar.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật ảnh đại diện'**
  String get updateAvatar;

  /// No description provided for @pinConversation.
  ///
  /// In en, this message translates to:
  /// **'Ghim hộp thoại'**
  String get pinConversation;

  /// No description provided for @unpinConversation.
  ///
  /// In en, this message translates to:
  /// **'Bỏ ghim hộp thoại'**
  String get unpinConversation;

  /// No description provided for @departmentRoom.
  ///
  /// In en, this message translates to:
  /// **'Phòng ban'**
  String get departmentRoom;

  /// No description provided for @inDay.
  ///
  /// In en, this message translates to:
  /// **'Buổi'**
  String get inDay;

  /// No description provided for @validateBranchDepartment.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng bổ sung chi nhánh và bộ phận'**
  String get validateBranchDepartment;

  /// No description provided for @validateNote.
  ///
  /// In en, this message translates to:
  /// **'Bổ sung địa điểm công tác vào ô Ghi chú'**
  String get validateNote;

  /// No description provided for @validateInday.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn buổi'**
  String get validateInday;

  /// No description provided for @titleEmoji.
  ///
  /// In en, this message translates to:
  /// **'Bạn cảm thấy thế nào?'**
  String get titleEmoji;

  /// No description provided for @feeling.
  ///
  /// In en, this message translates to:
  /// **'Đang cảm thấy'**
  String get feeling;

  /// No description provided for @forward.
  ///
  /// In en, this message translates to:
  /// **'Chuyển tiếp'**
  String get forward;

  /// No description provided for @forwardTo.
  ///
  /// In en, this message translates to:
  /// **'Chuyển tiếp {name}'**
  String forwardTo(String name);

  /// No description provided for @forwardFrom.
  ///
  /// In en, this message translates to:
  /// **'Chuyển tiếp từ {name}'**
  String forwardFrom(String name);

  /// No description provided for @forwardMessageSuccess.
  ///
  /// In en, this message translates to:
  /// **'Chuyển tiếp tin nhắn thành công'**
  String get forwardMessageSuccess;

  /// No description provided for @storyDetail.
  ///
  /// In en, this message translates to:
  /// **'Bài viết'**
  String get storyDetail;

  /// No description provided for @updating.
  ///
  /// In en, this message translates to:
  /// **'Đang cập nhật...'**
  String get updating;

  /// No description provided for @warningDeleteGroup.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn xóa nhóm?'**
  String get warningDeleteGroup;

  /// No description provided for @bugReport.
  ///
  /// In en, this message translates to:
  /// **'Báo lỗi'**
  String get bugReport;

  /// No description provided for @doYouDeleteStory.
  ///
  /// In en, this message translates to:
  /// **'Xoá bài viết?'**
  String get doYouDeleteStory;

  /// No description provided for @doYouDeleteComment.
  ///
  /// In en, this message translates to:
  /// **'Xoá bình luận?'**
  String get doYouDeleteComment;

  /// No description provided for @notFoundTag.
  ///
  /// In en, this message translates to:
  /// **'Không tìm thấy nhân viên'**
  String get notFoundTag;

  /// No description provided for @turnOnNoti.
  ///
  /// In en, this message translates to:
  /// **'Bật thông báo'**
  String get turnOnNoti;

  /// No description provided for @downloading.
  ///
  /// In en, this message translates to:
  /// **'Đang tải xuống {progress}'**
  String downloading(String progress);

  /// No description provided for @updateBackground.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật ảnh nền'**
  String get updateBackground;

  /// No description provided for @reaction.
  ///
  /// In en, this message translates to:
  /// **'Cảm xúc'**
  String get reaction;

  /// No description provided for @playSpeed.
  ///
  /// In en, this message translates to:
  /// **'Tốc độ phát'**
  String get playSpeed;

  /// No description provided for @playSpeedNomal.
  ///
  /// In en, this message translates to:
  /// **'Bình thường'**
  String get playSpeedNomal;

  /// No description provided for @uploadFileFailure.
  ///
  /// In en, this message translates to:
  /// **'Upload thất bại'**
  String get uploadFileFailure;

  /// No description provided for @joinNow.
  ///
  /// In en, this message translates to:
  /// **'Tham gia ngay'**
  String get joinNow;

  /// No description provided for @joinGroup.
  ///
  /// In en, this message translates to:
  /// **'Tham gia nhóm'**
  String get joinGroup;

  /// No description provided for @inviteLink.
  ///
  /// In en, this message translates to:
  /// **'Liên kết mời'**
  String get inviteLink;

  /// No description provided for @copiedInviteLink.
  ///
  /// In en, this message translates to:
  /// **'Đã sao chép link tham gia nhóm'**
  String get copiedInviteLink;

  /// No description provided for @titleNewPost.
  ///
  /// In en, this message translates to:
  /// **'Đăng bài mới'**
  String get titleNewPost;

  /// No description provided for @updateCheckinFailure.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật công thất bại'**
  String get updateCheckinFailure;

  /// No description provided for @callByApp.
  ///
  /// In en, this message translates to:
  /// **'Gọi qua ứng dụng'**
  String get callByApp;

  /// No description provided for @vpnDetectionWarning.
  ///
  /// In en, this message translates to:
  /// **'Bạn đang bật VPN vui lòng tắt để tiếp tục sử dụng ứng dụng'**
  String get vpnDetectionWarning;

  /// No description provided for @doYouDeleteNoti.
  ///
  /// In en, this message translates to:
  /// **'Xoá thông báo này?'**
  String get doYouDeleteNoti;

  /// No description provided for @recordToSendMessage.
  ///
  /// In en, this message translates to:
  /// **'Ứng dụng sử dụng quyền truy cập để ghi âm tin nhắn thoại'**
  String get recordToSendMessage;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Đổi mật khẩu'**
  String get changePassword;

  /// No description provided for @currentPassword.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu hiện tại'**
  String get currentPassword;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'Mật khẩu mới'**
  String get newPassword;

  /// No description provided for @changePasswordSuccess.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật mật khẩu thành công'**
  String get changePasswordSuccess;

  /// No description provided for @warningUploadFile.
  ///
  /// In en, this message translates to:
  /// **'Đang đăng tin, bạn có muốn huỷ để sang tab khác?'**
  String get warningUploadFile;

  /// No description provided for @confirmRemoveUploadFile.
  ///
  /// In en, this message translates to:
  /// **'Huỷ ngay'**
  String get confirmRemoveUploadFile;

  /// No description provided for @keepPage.
  ///
  /// In en, this message translates to:
  /// **'Ở lại trang'**
  String get keepPage;

  /// No description provided for @createFolder.
  ///
  /// In en, this message translates to:
  /// **'Tạo thư mục'**
  String get createFolder;

  /// No description provided for @recommendFolder.
  ///
  /// In en, this message translates to:
  /// **'Đề xuất thư mục'**
  String get recommendFolder;

  /// No description provided for @unread.
  ///
  /// In en, this message translates to:
  /// **'Chưa đọc'**
  String get unread;

  /// No description provided for @addUnreadFolder.
  ///
  /// In en, this message translates to:
  /// **'Nhấn ‘thêm’ để tạo thư mục Chưa đọc'**
  String get addUnreadFolder;

  /// No description provided for @addPersonalFolder.
  ///
  /// In en, this message translates to:
  /// **'Nhấn ‘thêm’ để tạo thư mục Cá nhân'**
  String get addPersonalFolder;

  /// No description provided for @personal.
  ///
  /// In en, this message translates to:
  /// **'Cá nhân'**
  String get personal;

  /// No description provided for @folder.
  ///
  /// In en, this message translates to:
  /// **'Thư mục'**
  String get folder;

  /// No description provided for @createAFolder.
  ///
  /// In en, this message translates to:
  /// **'Tạo 1 thư mục'**
  String get createAFolder;

  /// No description provided for @allConversations.
  ///
  /// In en, this message translates to:
  /// **'Tất cả các cuộc trò chuyện'**
  String get allConversations;

  /// No description provided for @editFolderHint.
  ///
  /// In en, this message translates to:
  /// **'Nhấn ‘sửa’ để thay đổi hoặc xóa thư mục'**
  String get editFolderHint;

  /// No description provided for @createFolderIntro.
  ///
  /// In en, this message translates to:
  /// **'Tạo thư mục cho các nhóm trò chuyện khác nhau và nhanh chóng chuyển đổi.'**
  String get createFolderIntro;

  /// No description provided for @newFolder.
  ///
  /// In en, this message translates to:
  /// **'Thư mục mới'**
  String get newFolder;

  /// No description provided for @folderName.
  ///
  /// In en, this message translates to:
  /// **'Tên thư mục'**
  String get folderName;

  /// No description provided for @addConversation.
  ///
  /// In en, this message translates to:
  /// **'Thêm hộp thoại'**
  String get addConversation;

  /// No description provided for @selectedConversation.
  ///
  /// In en, this message translates to:
  /// **'Hộp thoại đã chọn'**
  String get selectedConversation;

  /// No description provided for @addConversationHint.
  ///
  /// In en, this message translates to:
  /// **'Cuộc hội thoại sẽ xuất hiện trong thư mục này.'**
  String get addConversationHint;

  /// No description provided for @warningRemoveFolder.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn xóa thư mục này?'**
  String get warningRemoveFolder;

  /// No description provided for @editFolder.
  ///
  /// In en, this message translates to:
  /// **'Sửa thư mục'**
  String get editFolder;

  /// No description provided for @inefficient.
  ///
  /// In en, this message translates to:
  /// **'Không hiệu quả'**
  String get inefficient;

  /// No description provided for @titleTreatMent.
  ///
  /// In en, this message translates to:
  /// **'Liệu trình'**
  String get titleTreatMent;

  /// No description provided for @titleCommit.
  ///
  /// In en, this message translates to:
  /// **'Cam kết'**
  String get titleCommit;

  /// No description provided for @titleNVCS.
  ///
  /// In en, this message translates to:
  /// **'NVCS'**
  String get titleNVCS;

  /// No description provided for @titleEndTreatMent.
  ///
  /// In en, this message translates to:
  /// **'Kết thúc liệu trình'**
  String get titleEndTreatMent;

  /// No description provided for @titleCommitInfo.
  ///
  /// In en, this message translates to:
  /// **'Thông tin lúc cam kết'**
  String get titleCommitInfo;

  /// No description provided for @titleProductDrinkLather.
  ///
  /// In en, this message translates to:
  /// **'Sản phẩm (bôi,uống ghi rõ công thức)'**
  String get titleProductDrinkLather;

  /// No description provided for @titleTreatMentTime.
  ///
  /// In en, this message translates to:
  /// **'Thời gian bao lâu đi 1 lần, 1 lần bao nhiêu phút'**
  String get titleTreatMentTime;

  /// No description provided for @titleTotalMoney.
  ///
  /// In en, this message translates to:
  /// **'Tổng số tiền làm ốm'**
  String get titleTotalMoney;

  /// No description provided for @titleTreatmentZone.
  ///
  /// In en, this message translates to:
  /// **'Số xuất, công nghệ, vùng điều trị'**
  String get titleTreatmentZone;

  /// No description provided for @titleHigh.
  ///
  /// In en, this message translates to:
  /// **'Chiều cao'**
  String get titleHigh;

  /// No description provided for @titleWeight.
  ///
  /// In en, this message translates to:
  /// **'Cân nặng'**
  String get titleWeight;

  /// No description provided for @titleMeasurements.
  ///
  /// In en, this message translates to:
  /// **'Số đo'**
  String get titleMeasurements;

  /// No description provided for @pinnedMessage.
  ///
  /// In en, this message translates to:
  /// **'Tin được ghim'**
  String get pinnedMessage;

  /// No description provided for @pinMessage.
  ///
  /// In en, this message translates to:
  /// **'Ghim tin nhắn'**
  String get pinMessage;

  /// No description provided for @unpinMessage.
  ///
  /// In en, this message translates to:
  /// **'Bỏ ghim tin nhắn'**
  String get unpinMessage;

  /// No description provided for @warningHotfixUpdate.
  ///
  /// In en, this message translates to:
  /// **'Đã có bản cập nhật mới, bạn có muốn khởi động lại ứng dụng để hoàn tất cập nhật ?'**
  String get warningHotfixUpdate;

  /// No description provided for @checkUpdate.
  ///
  /// In en, this message translates to:
  /// **'Kiểm tra cập nhật'**
  String get checkUpdate;

  /// No description provided for @noAvailableUpdate.
  ///
  /// In en, this message translates to:
  /// **'Không có bản cập nhật khả dụng'**
  String get noAvailableUpdate;

  /// No description provided for @titleAddOption.
  ///
  /// In en, this message translates to:
  /// **'Thêm tuỳ chọn'**
  String get titleAddOption;

  /// No description provided for @option.
  ///
  /// In en, this message translates to:
  /// **'Tuỳ chọn'**
  String get option;

  /// No description provided for @inputTitle.
  ///
  /// In en, this message translates to:
  /// **'Nhập tiêu đề'**
  String get inputTitle;

  /// No description provided for @maxOption.
  ///
  /// In en, this message translates to:
  /// **'Bạn có thể tạo tối đa 8 tuỳ chọn'**
  String get maxOption;

  /// No description provided for @unVote.
  ///
  /// In en, this message translates to:
  /// **'Rút lại'**
  String get unVote;

  /// No description provided for @vote.
  ///
  /// In en, this message translates to:
  /// **'Bình chọn'**
  String get vote;

  /// No description provided for @timeoutMessage.
  ///
  /// In en, this message translates to:
  /// **'Hết thời gian chờ xử lý, vui lòng thử lại'**
  String get timeoutMessage;

  /// No description provided for @beforeDay.
  ///
  /// In en, this message translates to:
  /// **'Ngày trước'**
  String get beforeDay;

  /// No description provided for @readAllNoti.
  ///
  /// In en, this message translates to:
  /// **'Đọc tất cả'**
  String get readAllNoti;

  /// No description provided for @titleAllNoti.
  ///
  /// In en, this message translates to:
  /// **'Tất cả thông báo'**
  String get titleAllNoti;

  /// No description provided for @unpinAllMessageWarning.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn bỏ ghim tất cả tin nhắn?'**
  String get unpinAllMessageWarning;

  /// No description provided for @unpinAllMessage.
  ///
  /// In en, this message translates to:
  /// **'Bỏ ghim tất cả tin nhắn'**
  String get unpinAllMessage;

  /// No description provided for @createPoll.
  ///
  /// In en, this message translates to:
  /// **'Tạo bình chọn'**
  String get createPoll;

  /// No description provided for @voting.
  ///
  /// In en, this message translates to:
  /// **'Bình chọn'**
  String get voting;

  /// No description provided for @optional.
  ///
  /// In en, this message translates to:
  /// **'Lựa chọn'**
  String get optional;

  /// No description provided for @createNewOptional.
  ///
  /// In en, this message translates to:
  /// **'Tạo lựa chọn mới'**
  String get createNewOptional;

  /// No description provided for @canCreate8Optional.
  ///
  /// In en, this message translates to:
  /// **'Bạn có thể tạo tối đa 8 lựa chọn'**
  String get canCreate8Optional;

  /// No description provided for @anonymousVoting.
  ///
  /// In en, this message translates to:
  /// **'Ẩn người lựa chọn'**
  String get anonymousVoting;

  /// No description provided for @multipleAnswers.
  ///
  /// In en, this message translates to:
  /// **'Chọn nhiều lựa chọn'**
  String get multipleAnswers;

  /// No description provided for @addOptional.
  ///
  /// In en, this message translates to:
  /// **'Thêm lựa chọn'**
  String get addOptional;

  /// No description provided for @voted.
  ///
  /// In en, this message translates to:
  /// **'Đã bình chọn'**
  String get voted;

  /// No description provided for @anonymous.
  ///
  /// In en, this message translates to:
  /// **'Ẩn danh'**
  String get anonymous;

  /// No description provided for @viewResults.
  ///
  /// In en, this message translates to:
  /// **'Xem kết quả'**
  String get viewResults;

  /// No description provided for @retractVote.
  ///
  /// In en, this message translates to:
  /// **'Bỏ bình chọn'**
  String get retractVote;

  /// No description provided for @stopVote.
  ///
  /// In en, this message translates to:
  /// **'Kết thúc bình chọn'**
  String get stopVote;

  /// No description provided for @addImage.
  ///
  /// In en, this message translates to:
  /// **'Thêm ảnh'**
  String get addImage;

  /// No description provided for @addSomeImage.
  ///
  /// In en, this message translates to:
  /// **'(Đã thêm {total} ảnh)'**
  String addSomeImage(int total);

  /// No description provided for @addImageFromSystem.
  ///
  /// In en, this message translates to:
  /// **'Chọn ảnh từ phần mềm'**
  String get addImageFromSystem;

  /// No description provided for @effective.
  ///
  /// In en, this message translates to:
  /// **'Hiệu quả'**
  String get effective;

  /// No description provided for @notEffective.
  ///
  /// In en, this message translates to:
  /// **'Không hiệu quả'**
  String get notEffective;

  /// No description provided for @workPausedTitle.
  ///
  /// In en, this message translates to:
  /// **'Quy trình đã tạm dừng'**
  String get workPausedTitle;

  /// No description provided for @workPausedContent.
  ///
  /// In en, this message translates to:
  /// **'\nBạn không thể tiếp tục thao tác.\nVui lòng thực hiện lại khi có thông báo.'**
  String get workPausedContent;

  /// No description provided for @paused.
  ///
  /// In en, this message translates to:
  /// **'Tạm dừng'**
  String get paused;

  /// No description provided for @noFaceDetected.
  ///
  /// In en, this message translates to:
  /// **'Không tìm thấy khuôn mặt trong ảnh, bạn có muốn tiếp tục ?'**
  String get noFaceDetected;

  /// No description provided for @multipleFaceDetected.
  ///
  /// In en, this message translates to:
  /// **'Có nhiều khuôn mặt trong ảnh, vui lòng thử lại'**
  String get multipleFaceDetected;

  /// No description provided for @lookStraight.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhìn thẳng'**
  String get lookStraight;

  /// No description provided for @lookStraightAndBlink.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhìn thẳng và chớp mắt'**
  String get lookStraightAndBlink;

  /// No description provided for @lookStraightAndSmile.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhìn thẳng và cười'**
  String get lookStraightAndSmile;

  /// No description provided for @turnLeft.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng xoay trái'**
  String get turnLeft;

  /// No description provided for @turnRight.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng xoay phải'**
  String get turnRight;

  /// No description provided for @faceVerify.
  ///
  /// In en, this message translates to:
  /// **'Xác thực khuôn mặt'**
  String get faceVerify;

  /// No description provided for @titlePoll.
  ///
  /// In en, this message translates to:
  /// **'Câu hỏi'**
  String get titlePoll;

  /// No description provided for @inputTitlePoll.
  ///
  /// In en, this message translates to:
  /// **'Nhập câu hỏi'**
  String get inputTitlePoll;

  /// No description provided for @titleOption.
  ///
  /// In en, this message translates to:
  /// **'Lựa chọn'**
  String get titleOption;

  /// No description provided for @inputTitleOption.
  ///
  /// In en, this message translates to:
  /// **'Nhập lựa chọn'**
  String get inputTitleOption;

  /// No description provided for @inputCreateOption.
  ///
  /// In en, this message translates to:
  /// **'Tạo lựa chọn mới'**
  String get inputCreateOption;

  /// No description provided for @max8Option.
  ///
  /// In en, this message translates to:
  /// **'Bạn có thể tạo tối đa 8 lựa chọn'**
  String get max8Option;

  /// No description provided for @hiddenVoted.
  ///
  /// In en, this message translates to:
  /// **'Ẩn người lựa chọn'**
  String get hiddenVoted;

  /// No description provided for @multipleVoted.
  ///
  /// In en, this message translates to:
  /// **'Chọn nhiều lựa chọn'**
  String get multipleVoted;

  /// No description provided for @titleModePoll.
  ///
  /// In en, this message translates to:
  /// **'Bình chọn ẩn danh'**
  String get titleModePoll;

  /// No description provided for @emptyVote.
  ///
  /// In en, this message translates to:
  /// **'Không có bình chọn'**
  String get emptyVote;

  /// No description provided for @createVote.
  ///
  /// In en, this message translates to:
  /// **'Tạo bình chọn'**
  String get createVote;

  /// No description provided for @editPoll.
  ///
  /// In en, this message translates to:
  /// **'Sửa bình chọn'**
  String get editPoll;

  /// No description provided for @only8Option.
  ///
  /// In en, this message translates to:
  /// **'Chỉ tối đa 8 tuỳ chọn'**
  String get only8Option;

  /// No description provided for @refunOption.
  ///
  /// In en, this message translates to:
  /// **'Hoàn lại'**
  String get refunOption;

  /// No description provided for @remindKYC.
  ///
  /// In en, this message translates to:
  /// **'Bạn chưa thực hiện xác thực khuôn mặt, vui lòng thực hiện để hỗ trợ việc chấm công chính xác hơn'**
  String get remindKYC;

  /// No description provided for @alreadyKYCMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã xác thực khuôn mặt, vui lòng liên hệ nhân sự nếu cần xác thực lại'**
  String get alreadyKYCMessage;

  /// No description provided for @ticket.
  ///
  /// In en, this message translates to:
  /// **'Ticket'**
  String get ticket;

  /// No description provided for @createdBy.
  ///
  /// In en, this message translates to:
  /// **'tạo bởi'**
  String get createdBy;

  /// No description provided for @by.
  ///
  /// In en, this message translates to:
  /// **'bởi'**
  String get by;

  /// No description provided for @removeFilter.
  ///
  /// In en, this message translates to:
  /// **'Xoá lọc'**
  String get removeFilter;

  /// No description provided for @groupRecieve.
  ///
  /// In en, this message translates to:
  /// **'Nhóm nhận'**
  String get groupRecieve;

  /// No description provided for @qrCode.
  ///
  /// In en, this message translates to:
  /// **'Mã QR'**
  String get qrCode;

  /// No description provided for @hintTextMedia.
  ///
  /// In en, this message translates to:
  /// **'Thêm hình ảnh/video/file đính kèm'**
  String get hintTextMedia;

  /// No description provided for @hintTextOnlyMedia.
  ///
  /// In en, this message translates to:
  /// **'Thêm hình ảnh/video'**
  String get hintTextOnlyMedia;

  /// No description provided for @hintTextTypeSelect.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn loại --'**
  String get hintTextTypeSelect;

  /// No description provided for @hintTextGroupRecieveSelect.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn nhóm nhận --'**
  String get hintTextGroupRecieveSelect;

  /// No description provided for @contentTicket.
  ///
  /// In en, this message translates to:
  /// **'Nội dung ticket'**
  String get contentTicket;

  /// No description provided for @messageOrderFoodSuccess.
  ///
  /// In en, this message translates to:
  /// **'Chúc mừng bạn đã đặt cơm thành công'**
  String get messageOrderFoodSuccess;

  /// No description provided for @messageCreatedTicketSuccess.
  ///
  /// In en, this message translates to:
  /// **'Chúc mừng bạn đã tạo thành công ticket'**
  String get messageCreatedTicketSuccess;

  /// No description provided for @messageCancelOrderFood.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc chắn muốn huỷ đặt cơm?'**
  String get messageCancelOrderFood;

  /// No description provided for @barCode.
  ///
  /// In en, this message translates to:
  /// **'Mã code'**
  String get barCode;

  /// No description provided for @noteQR.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng đưa mã QR này cho nhà bếp để xác nhận phần cơm'**
  String get noteQR;

  /// No description provided for @complaint.
  ///
  /// In en, this message translates to:
  /// **'Góp ý'**
  String get complaint;

  /// No description provided for @regulations.
  ///
  /// In en, this message translates to:
  /// **'Quy định'**
  String get regulations;

  /// No description provided for @messageRatingFood.
  ///
  /// In en, this message translates to:
  /// **'Bạn thấy món ăn hôm nay như thế nào?'**
  String get messageRatingFood;

  /// No description provided for @messageOrderFoodComplaint.
  ///
  /// In en, this message translates to:
  /// **'Lời nhắn'**
  String get messageOrderFoodComplaint;

  /// No description provided for @expiredOrderFood.
  ///
  /// In en, this message translates to:
  /// **'Thời gian chốt cơm còn'**
  String get expiredOrderFood;

  /// No description provided for @ticketType.
  ///
  /// In en, this message translates to:
  /// **'Loại ticket'**
  String get ticketType;

  /// No description provided for @statusType.
  ///
  /// In en, this message translates to:
  /// **'Loại trạng thái'**
  String get statusType;

  /// No description provided for @cameraV2.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get cameraV2;

  /// No description provided for @thuoc.
  ///
  /// In en, this message translates to:
  /// **'thuộc'**
  String get thuoc;

  /// No description provided for @reply2.
  ///
  /// In en, this message translates to:
  /// **'Trả lời'**
  String get reply2;

  /// No description provided for @allActive.
  ///
  /// In en, this message translates to:
  /// **'Tất cả hoạt động'**
  String get allActive;

  /// No description provided for @titleCollection.
  ///
  /// In en, this message translates to:
  /// **'Chọn mục tải lên'**
  String get titleCollection;

  /// No description provided for @statusTypeSelected.
  ///
  /// In en, this message translates to:
  /// **'Lọc kèm với trạng thái'**
  String get statusTypeSelected;

  /// No description provided for @ticketTypeSelected.
  ///
  /// In en, this message translates to:
  /// **'Lọc kèm với ticket'**
  String get ticketTypeSelected;

  /// No description provided for @reasonCancel.
  ///
  /// In en, this message translates to:
  /// **'Lý do huỷ'**
  String get reasonCancel;

  /// No description provided for @unRecept.
  ///
  /// In en, this message translates to:
  /// **'Bỏ tiếp nhận'**
  String get unRecept;

  /// No description provided for @receptSuccess.
  ///
  /// In en, this message translates to:
  /// **'Tiếp nhận thành công'**
  String get receptSuccess;

  /// No description provided for @completeTicketSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã hoàn tất xong ticket'**
  String get completeTicketSuccess;

  /// No description provided for @deleteTicketSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn vừa huỷ 1 ticket'**
  String get deleteTicketSuccess;

  /// No description provided for @deleteReceptSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn vừa huỷ bỏ tiếp nhận ticket'**
  String get deleteReceptSuccess;

  /// No description provided for @creatingTicket.
  ///
  /// In en, this message translates to:
  /// **'Đang tạo ticket'**
  String get creatingTicket;

  /// No description provided for @createNewTicket.
  ///
  /// In en, this message translates to:
  /// **'Tạo mới ticket'**
  String get createNewTicket;

  /// No description provided for @messageReportSuccess.
  ///
  /// In en, this message translates to:
  /// **'Đã góp ý thành công'**
  String get messageReportSuccess;

  /// No description provided for @orderedFood.
  ///
  /// In en, this message translates to:
  /// **'Đã chốt cơm'**
  String get orderedFood;

  /// No description provided for @cancelRice.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã huỷ đặt'**
  String get cancelRice;

  /// No description provided for @expiredQR.
  ///
  /// In en, this message translates to:
  /// **'Mã QR đã hết hạn'**
  String get expiredQR;

  /// No description provided for @imageEdit.
  ///
  /// In en, this message translates to:
  /// **'Bạn có muốn thoát?'**
  String get imageEdit;

  /// No description provided for @createStickers.
  ///
  /// In en, this message translates to:
  /// **'Tạo sticker'**
  String get createStickers;

  /// No description provided for @expireFood.
  ///
  /// In en, this message translates to:
  /// **'Thời gian chốt cơm còn'**
  String get expireFood;

  /// No description provided for @inputCodeManual.
  ///
  /// In en, this message translates to:
  /// **'Nhập mã thủ công'**
  String get inputCodeManual;

  /// No description provided for @scanCodeSuccess.
  ///
  /// In en, this message translates to:
  /// **'Quét mã {code} thành công'**
  String scanCodeSuccess(Object code);

  /// No description provided for @defaultAddress.
  ///
  /// In en, this message translates to:
  /// **'Địa chỉ mặc định'**
  String get defaultAddress;

  /// No description provided for @setDefaultAddress.
  ///
  /// In en, this message translates to:
  /// **'Đặt làm địa chỉ mặc định'**
  String get setDefaultAddress;

  /// No description provided for @addAddress.
  ///
  /// In en, this message translates to:
  /// **'Thêm địa chỉ'**
  String get addAddress;

  /// No description provided for @remindBookingMealMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có muốn đặt cơm cho ngày mai?'**
  String get remindBookingMealMessage;

  /// No description provided for @pleaseSelectMealAddress.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn địa chỉ đặt cơm'**
  String get pleaseSelectMealAddress;

  /// No description provided for @getOTPOnAcc.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng truy cập ACC để lấy OTP!'**
  String get getOTPOnAcc;

  /// No description provided for @anonymousMessage.
  ///
  /// In en, this message translates to:
  /// **'Ẩn danh (Góp ý của bạn sẽ được gửi ẩn danh)'**
  String get anonymousMessage;

  /// No description provided for @detailProfile.
  ///
  /// In en, this message translates to:
  /// **'Hồ sơ chi tiết'**
  String get detailProfile;

  /// No description provided for @titleTreatmentDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày tham gia'**
  String get titleTreatmentDate;

  /// No description provided for @titleAssureWeight.
  ///
  /// In en, this message translates to:
  /// **'Số kg'**
  String get titleAssureWeight;

  /// No description provided for @titleAssureSize.
  ///
  /// In en, this message translates to:
  /// **'Số đo'**
  String get titleAssureSize;

  /// No description provided for @titleLoseWeight.
  ///
  /// In en, this message translates to:
  /// **'Số kg đã xuống'**
  String get titleLoseWeight;

  /// No description provided for @titleTTKHAge.
  ///
  /// In en, this message translates to:
  /// **'Tuổi ( ngày, tháng, năm sinh )'**
  String get titleTTKHAge;

  /// No description provided for @titleTTKHOccupation.
  ///
  /// In en, this message translates to:
  /// **'Nghề nghiệp'**
  String get titleTTKHOccupation;

  /// No description provided for @titleTTKHMeasure.
  ///
  /// In en, this message translates to:
  /// **'Số đo 3 vòng'**
  String get titleTTKHMeasure;

  /// No description provided for @titleTTKHOverWeight.
  ///
  /// In en, this message translates to:
  /// **'Dư cân'**
  String get titleTTKHOverWeight;

  /// No description provided for @titleTypeOfBone.
  ///
  /// In en, this message translates to:
  /// **'Xuơng to hay nhỏ'**
  String get titleTypeOfBone;

  /// No description provided for @titleTypeOfBirth.
  ///
  /// In en, this message translates to:
  /// **'Đã sinh con chưa? Sinh thường hay mổ?'**
  String get titleTypeOfBirth;

  /// No description provided for @titleIsTreatment.
  ///
  /// In en, this message translates to:
  /// **'Có đang điều trị bệnh không?'**
  String get titleIsTreatment;

  /// No description provided for @titleTTKHMeal.
  ///
  /// In en, this message translates to:
  /// **'Chế độ ăn hàng ngày ( 1 ngày mấy bữa, Thường ăn gì? )'**
  String get titleTTKHMeal;

  /// No description provided for @titleTTKHDoExercise.
  ///
  /// In en, this message translates to:
  /// **'Tập luyện ( đang tập gì?, Bao nhiêu phút 1 ngày? )'**
  String get titleTTKHDoExercise;

  /// No description provided for @titleTTKHHouseWork.
  ///
  /// In en, this message translates to:
  /// **'Bạn có làm việc nhà hay không? Cụ thể công việc ? Mất bao nhiêu phút?'**
  String get titleTTKHHouseWork;

  /// No description provided for @titleTTKHHistoryFit.
  ///
  /// In en, this message translates to:
  /// **'Có từng uống thuốc ốm không? Hay điều trị giảm béo ở đâu ? Ghi rõ cụ thể'**
  String get titleTTKHHistoryFit;

  /// No description provided for @titleTTKH.
  ///
  /// In en, this message translates to:
  /// **'TT khách hàng'**
  String get titleTTKH;

  /// No description provided for @titleGNKQ.
  ///
  /// In en, this message translates to:
  /// **'Ghi nhận kết quả'**
  String get titleGNKQ;

  /// No description provided for @titleGNKQSTT.
  ///
  /// In en, this message translates to:
  /// **'STT'**
  String get titleGNKQSTT;

  /// No description provided for @titleGNKQDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày'**
  String get titleGNKQDate;

  /// No description provided for @titleGNKQArea.
  ///
  /// In en, this message translates to:
  /// **'Vùng điều trị, số đo'**
  String get titleGNKQArea;

  /// No description provided for @titleGNKQMhs.
  ///
  /// In en, this message translates to:
  /// **'Toa trà'**
  String get titleGNKQMhs;

  /// No description provided for @titleGNKQMhsEmpName.
  ///
  /// In en, this message translates to:
  /// **'Người ra toa'**
  String get titleGNKQMhsEmpName;

  /// No description provided for @titleGNKQDetailTreatment.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết điều trị tại nhà'**
  String get titleGNKQDetailTreatment;

  /// No description provided for @titleGNKQLoseWeight.
  ///
  /// In en, this message translates to:
  /// **'Tổng giảm'**
  String get titleGNKQLoseWeight;

  /// No description provided for @titleGNKQWeight.
  ///
  /// In en, this message translates to:
  /// **'Cân'**
  String get titleGNKQWeight;

  /// No description provided for @titleGNKQResult.
  ///
  /// In en, this message translates to:
  /// **'Hiệu quả'**
  String get titleGNKQResult;

  /// No description provided for @titleGNKQTT.
  ///
  /// In en, this message translates to:
  /// **'TT'**
  String get titleGNKQTT;

  /// No description provided for @titleGNKQStatus.
  ///
  /// In en, this message translates to:
  /// **'Tình Trạng'**
  String get titleGNKQStatus;

  /// No description provided for @titleTTKHTitleSeconds.
  ///
  /// In en, this message translates to:
  /// **'( nhập số )'**
  String get titleTTKHTitleSeconds;

  /// No description provided for @isValiGNKQ.
  ///
  /// In en, this message translates to:
  /// **'Chọn dịch vụ'**
  String get isValiGNKQ;

  /// No description provided for @titleNDTV.
  ///
  /// In en, this message translates to:
  /// **'ND TV'**
  String get titleNDTV;

  /// No description provided for @doYouDeleteKQGH.
  ///
  /// In en, this message translates to:
  /// **'Bạn chắc chắn muốn xoá ghi nhận kết quả này ?'**
  String get doYouDeleteKQGH;

  /// No description provided for @titleGNKQDelete.
  ///
  /// In en, this message translates to:
  /// **'Xoá ghi nhận'**
  String get titleGNKQDelete;

  /// No description provided for @titleTabTTKH.
  ///
  /// In en, this message translates to:
  /// **'TTKH'**
  String get titleTabTTKH;

  /// No description provided for @serviceEmpty.
  ///
  /// In en, this message translates to:
  /// **'Chưa chọn dịch vụ'**
  String get serviceEmpty;

  /// No description provided for @treatmentHome.
  ///
  /// In en, this message translates to:
  /// **'Chi tiết điều trị tại nhà'**
  String get treatmentHome;

  /// No description provided for @classify.
  ///
  /// In en, this message translates to:
  /// **'Phân loại'**
  String get classify;

  /// No description provided for @requiredSelectClassify.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn phân loại'**
  String get requiredSelectClassify;

  /// No description provided for @myTicket.
  ///
  /// In en, this message translates to:
  /// **'Của tôi'**
  String get myTicket;

  /// No description provided for @processingTicket.
  ///
  /// In en, this message translates to:
  /// **'Danh sách xử lý'**
  String get processingTicket;

  /// No description provided for @consumerId.
  ///
  /// In en, this message translates to:
  /// **'Mã KH'**
  String get consumerId;

  /// No description provided for @consumerId2.
  ///
  /// In en, this message translates to:
  /// **'Mã khách hàng'**
  String get consumerId2;

  /// No description provided for @hintEmployeeId.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn khách hàng --'**
  String get hintEmployeeId;

  /// No description provided for @hintService.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn dịch vụ --'**
  String get hintService;

  /// No description provided for @hintStatus.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn trạng thái --'**
  String get hintStatus;

  /// No description provided for @updateTicket.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật ticket'**
  String get updateTicket;

  /// No description provided for @hintInputGroupTicket.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm theo nhóm nhận'**
  String get hintInputGroupTicket;

  /// No description provided for @hintInputTypeTicket.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm theo tên loại'**
  String get hintInputTypeTicket;

  /// No description provided for @hintInputCustomerTicket.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm theo tên, mã KH'**
  String get hintInputCustomerTicket;

  /// No description provided for @hintInputServiceTicket.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm theo tên dịch vụ'**
  String get hintInputServiceTicket;

  /// No description provided for @confirmComplete.
  ///
  /// In en, this message translates to:
  /// **'Xác nhận hoàn tất'**
  String get confirmComplete;

  /// No description provided for @followIssue.
  ///
  /// In en, this message translates to:
  /// **'Theo dõi sự cố'**
  String get followIssue;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Khác'**
  String get other;

  /// No description provided for @giveBackTicket.
  ///
  /// In en, this message translates to:
  /// **'Bạn vừa bỏ tiếp nhận 1 ticket'**
  String get giveBackTicket;

  /// No description provided for @messageUpdatedTicketSuccess.
  ///
  /// In en, this message translates to:
  /// **'Cập nhật ticket thành công'**
  String get messageUpdatedTicketSuccess;

  /// No description provided for @processTicketSuccess.
  ///
  /// In en, this message translates to:
  /// **'Xử lý ticket thành công'**
  String get processTicketSuccess;

  /// No description provided for @titleProcess.
  ///
  /// In en, this message translates to:
  /// **'Xử lý'**
  String get titleProcess;

  /// No description provided for @confirmProcess.
  ///
  /// In en, this message translates to:
  /// **'Xác nhận xử lý'**
  String get confirmProcess;

  /// No description provided for @unitRequired.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng nhập đơn vị tính'**
  String get unitRequired;

  /// No description provided for @requiredRestart.
  ///
  /// In en, this message translates to:
  /// **'Ứng dụng cần khởi động lại để hoàn tất thiết lập!'**
  String get requiredRestart;

  /// No description provided for @cc.
  ///
  /// In en, this message translates to:
  /// **'CC'**
  String get cc;

  /// No description provided for @ccAction.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn cc --'**
  String get ccAction;

  /// No description provided for @requestEmp.
  ///
  /// In en, this message translates to:
  /// **'Người yêu cầu'**
  String get requestEmp;

  /// No description provided for @requestEmpAction.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn người yêu cầu --'**
  String get requestEmpAction;

  /// No description provided for @receptEmp.
  ///
  /// In en, this message translates to:
  /// **'Người tiếp nhận'**
  String get receptEmp;

  /// No description provided for @receptEmpAction.
  ///
  /// In en, this message translates to:
  /// **'-- Chọn người tiếp nhận --'**
  String get receptEmpAction;

  /// No description provided for @deadline.
  ///
  /// In en, this message translates to:
  /// **'Deadline'**
  String get deadline;

  /// No description provided for @rework.
  ///
  /// In en, this message translates to:
  /// **'Chuyển việc'**
  String get rework;

  /// No description provided for @assignee.
  ///
  /// In en, this message translates to:
  /// **'Chuyển việc'**
  String get assignee;

  /// No description provided for @assigneeAction.
  ///
  /// In en, this message translates to:
  /// **'Chuyển việc'**
  String get assigneeAction;

  /// No description provided for @actionCancel.
  ///
  /// In en, this message translates to:
  /// **'vuốt để huỷ'**
  String get actionCancel;

  /// No description provided for @assignedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Chuyển việc thành công'**
  String get assignedSuccess;

  /// No description provided for @sendFrom.
  ///
  /// In en, this message translates to:
  /// **'Gửi từ'**
  String get sendFrom;

  /// No description provided for @sendTo.
  ///
  /// In en, this message translates to:
  /// **'đến'**
  String get sendTo;

  /// No description provided for @ccDisplay.
  ///
  /// In en, this message translates to:
  /// **'CC'**
  String get ccDisplay;

  /// No description provided for @requestorDisplay.
  ///
  /// In en, this message translates to:
  /// **'Người yêu cầu'**
  String get requestorDisplay;

  /// No description provided for @isRecording.
  ///
  /// In en, this message translates to:
  /// **'Thiết bị dang ghi âm...'**
  String get isRecording;

  /// No description provided for @doYouDeleteAudio.
  ///
  /// In en, this message translates to:
  /// **'Bạn có muốn xoá ghi âm này?'**
  String get doYouDeleteAudio;

  /// No description provided for @waitingForLoadingMessage.
  ///
  /// In en, this message translates to:
  /// **'Chờ tải tin nhắn'**
  String get waitingForLoadingMessage;

  /// No description provided for @isCompleteRecord.
  ///
  /// In en, this message translates to:
  /// **'Bạn phải hoàn thành phần ghi âm trước'**
  String get isCompleteRecord;

  /// No description provided for @custom.
  ///
  /// In en, this message translates to:
  /// **'Tùy chỉnh'**
  String get custom;

  /// No description provided for @favoriteFeatures.
  ///
  /// In en, this message translates to:
  /// **'Chức năng yêu thích'**
  String get favoriteFeatures;

  /// No description provided for @featureList.
  ///
  /// In en, this message translates to:
  /// **'Danh sách tính năng'**
  String get featureList;

  /// No description provided for @searchByFeature.
  ///
  /// In en, this message translates to:
  /// **'Tìm theo tên tính năng'**
  String get searchByFeature;

  /// No description provided for @completeEdit.
  ///
  /// In en, this message translates to:
  /// **'Hoàn tất chỉnh sửa'**
  String get completeEdit;

  /// No description provided for @markAsRead.
  ///
  /// In en, this message translates to:
  /// **'Đánh dấu đã đọc'**
  String get markAsRead;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Tải xuống'**
  String get download;

  /// No description provided for @alertHasFailedMessage.
  ///
  /// In en, this message translates to:
  /// **'Hiện có tin nhắn gửi không thành công, bạn vẫn muốn rời khỏi?'**
  String get alertHasFailedMessage;

  /// No description provided for @clearCache.
  ///
  /// In en, this message translates to:
  /// **'Xóa bộ nhớ đệm'**
  String get clearCache;

  /// No description provided for @clearCacheMessage.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn xóa bộ nhớ đệm của ứng dụng?'**
  String get clearCacheMessage;

  /// No description provided for @spEmp.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên phụ'**
  String get spEmp;

  /// No description provided for @age.
  ///
  /// In en, this message translates to:
  /// **'tuổi'**
  String get age;

  /// No description provided for @visitTime.
  ///
  /// In en, this message translates to:
  /// **'Gắn bó hơn'**
  String get visitTime;

  /// No description provided for @imageBefore.
  ///
  /// In en, this message translates to:
  /// **'Ảnh trước'**
  String get imageBefore;

  /// No description provided for @imageAfter.
  ///
  /// In en, this message translates to:
  /// **'Ảnh sau'**
  String get imageAfter;

  /// No description provided for @customerImage.
  ///
  /// In en, this message translates to:
  /// **'Xử lý hình ảnh'**
  String get customerImage;

  /// No description provided for @emptyImage.
  ///
  /// In en, this message translates to:
  /// **'Chưa có ảnh'**
  String get emptyImage;

  /// No description provided for @customerImageReview.
  ///
  /// In en, this message translates to:
  /// **'Xem lại thông tin'**
  String get customerImageReview;

  /// No description provided for @skinBodyF1.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng quan tâm điều gì?'**
  String get skinBodyF1;

  /// No description provided for @skinBodyF2.
  ///
  /// In en, this message translates to:
  /// **'Đã từng điệu trị da ở đâu? (Điều trị như thế nào? Hiệu quả? Giá tiền? Nguyên nhân gây ra tình trạng da hiện tại? Bị bao lâu)?'**
  String get skinBodyF2;

  /// No description provided for @skinBodyF3.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng có bệnh lý gì đặc biệt? Hay đang điều trị bệnh gì không?'**
  String get skinBodyF3;

  /// No description provided for @skinBodyF4.
  ///
  /// In en, this message translates to:
  /// **'Da có bị dị ứng (Thuốc uống, thuốc bôi, kem bôi, thời tiết, thức ăn, hoá chất)?'**
  String get skinBodyF4;

  /// No description provided for @skinBodyF5.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng có sử dụng các sản phẩm gây bong tróc như: peel da, sp Obagi, Tretinon, lăn kim, phi kim, laser, tiêm chích, căng chỉ gì gần đây không?'**
  String get skinBodyF5;

  /// No description provided for @skinBodyF6.
  ///
  /// In en, this message translates to:
  /// **'Thời gian khách hàng đến được với thẩm mỹ viện bao lâu 1 lần?'**
  String get skinBodyF6;

  /// No description provided for @skinBodyF7.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng có hay đi nắng không, có thói quen sử dụng kem chống nắng, có trang điểm thường xuyên?'**
  String get skinBodyF7;

  /// No description provided for @skinBodyF8.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng đang sử dụng sản phẩm, chăm sóc gì ở nhà?'**
  String get skinBodyF8;

  /// No description provided for @skinBodyF9.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng có đang uống thuốc giảm cân hay điều trị giảm cân?'**
  String get skinBodyF9;

  /// No description provided for @titleTSKH.
  ///
  /// In en, this message translates to:
  /// **'Tiền sử bệnh về da'**
  String get titleTSKH;

  /// No description provided for @titleTTBDKH.
  ///
  /// In en, this message translates to:
  /// **'TT ban đầu da KH'**
  String get titleTTBDKH;

  /// No description provided for @skinBodyTTF1.
  ///
  /// In en, this message translates to:
  /// **'Loại da (Nhờn, hỗn hợp, khô, lý tưởng):'**
  String get skinBodyTTF1;

  /// No description provided for @skinBodyTTF2.
  ///
  /// In en, this message translates to:
  /// **'Độ dày da (Mỏng, cực mỏng, bình thường, dày, sần vỏ cam):'**
  String get skinBodyTTF2;

  /// No description provided for @skinBodyTTF3.
  ///
  /// In en, this message translates to:
  /// **'Màu da:'**
  String get skinBodyTTF3;

  /// No description provided for @skinBodyTTF4.
  ///
  /// In en, this message translates to:
  /// **'Tình trạng da khách hàng:'**
  String get skinBodyTTF4;

  /// No description provided for @skinBodyTTF5.
  ///
  /// In en, this message translates to:
  /// **'Vấn đề khách hàng điều trị:'**
  String get skinBodyTTF5;

  /// No description provided for @skinBodyTTF6.
  ///
  /// In en, this message translates to:
  /// **'Nguyên nhân/bệnh lý đặc biệt liên quan da:'**
  String get skinBodyTTF6;

  /// No description provided for @skinBodyTTF7.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng ký ghi rõ họ và tên'**
  String get skinBodyTTF7;

  /// No description provided for @skinBodyTTF8.
  ///
  /// In en, this message translates to:
  /// **'Khách hàng đã đọc và đồng ý'**
  String get skinBodyTTF8;

  /// No description provided for @resign.
  ///
  /// In en, this message translates to:
  /// **'Ký lại'**
  String get resign;

  /// No description provided for @kyten.
  ///
  /// In en, this message translates to:
  /// **'Ký tên'**
  String get kyten;

  /// No description provided for @nextTo.
  ///
  /// In en, this message translates to:
  /// **'Tiếp theo'**
  String get nextTo;

  /// No description provided for @totalProduct.
  ///
  /// In en, this message translates to:
  /// **'Số lượng sản phẩm'**
  String get totalProduct;

  /// No description provided for @caculateTime.
  ///
  /// In en, this message translates to:
  /// **'Thời gian tính'**
  String get caculateTime;

  /// No description provided for @timeSave.
  ///
  /// In en, this message translates to:
  /// **'Thời gian tính'**
  String get timeSave;

  /// No description provided for @totalCount.
  ///
  /// In en, this message translates to:
  /// **'Số lượng'**
  String get totalCount;

  /// No description provided for @revenueEmp.
  ///
  /// In en, this message translates to:
  /// **'Báo cáo năng suất'**
  String get revenueEmp;

  /// No description provided for @seen.
  ///
  /// In en, this message translates to:
  /// **'Đã xem'**
  String get seen;

  /// No description provided for @react.
  ///
  /// In en, this message translates to:
  /// **'Tương tác'**
  String get react;

  /// No description provided for @copy.
  ///
  /// In en, this message translates to:
  /// **'Sao chép'**
  String get copy;

  /// No description provided for @paste.
  ///
  /// In en, this message translates to:
  /// **'Dán'**
  String get paste;

  /// No description provided for @bold.
  ///
  /// In en, this message translates to:
  /// **'Bold'**
  String get bold;

  /// No description provided for @italic.
  ///
  /// In en, this message translates to:
  /// **'Italic'**
  String get italic;

  /// No description provided for @underline.
  ///
  /// In en, this message translates to:
  /// **'Underline'**
  String get underline;

  /// No description provided for @sticker.
  ///
  /// In en, this message translates to:
  /// **'Sticker'**
  String get sticker;

  /// No description provided for @showInChat.
  ///
  /// In en, this message translates to:
  /// **'Xem tin nhắn gốc'**
  String get showInChat;

  /// No description provided for @sentMessage.
  ///
  /// In en, this message translates to:
  /// **'Đã gửi tin nhắn'**
  String get sentMessage;

  /// No description provided for @pleaseSelectConversation.
  ///
  /// In en, this message translates to:
  /// **'Vui lòng chọn hộp thoại'**
  String get pleaseSelectConversation;

  /// No description provided for @selectionAll.
  ///
  /// In en, this message translates to:
  /// **'Chọn tất cả'**
  String get selectionAll;

  /// No description provided for @emoji.
  ///
  /// In en, this message translates to:
  /// **'EMOJI ICONS'**
  String get emoji;

  /// No description provided for @leadershipBoard.
  ///
  /// In en, this message translates to:
  /// **'Ban lãnh đạo'**
  String get leadershipBoard;

  /// No description provided for @admin.
  ///
  /// In en, this message translates to:
  /// **'Quản trị viên'**
  String get admin;

  /// No description provided for @removeMember.
  ///
  /// In en, this message translates to:
  /// **'Xóa thành viên'**
  String get removeMember;

  /// No description provided for @customizeName.
  ///
  /// In en, this message translates to:
  /// **'Tùy Chỉnh Tên'**
  String get customizeName;

  /// No description provided for @customizeNameHint.
  ///
  /// In en, this message translates to:
  /// **'Tùy chỉnh tên bạn muốn thay vì tên mặc định “admin”'**
  String get customizeNameHint;

  /// No description provided for @adminCan.
  ///
  /// In en, this message translates to:
  /// **'Quản trị viên này có thể'**
  String get adminCan;

  /// No description provided for @adminCanDetails.
  ///
  /// In en, this message translates to:
  /// **'Quản trị viên này sẽ có thể thêm quản trị viên mới với quyền ngang bằng hoặc ít hơn'**
  String get adminCanDetails;

  /// No description provided for @transferOwnership.
  ///
  /// In en, this message translates to:
  /// **'Chuyển quyền Owner'**
  String get transferOwnership;

  /// No description provided for @endTreatment.
  ///
  /// In en, this message translates to:
  /// **'Kết thúc liệu trình'**
  String get endTreatment;

  /// No description provided for @initialCondition.
  ///
  /// In en, this message translates to:
  /// **'Tình trạng ban đầu'**
  String get initialCondition;

  /// No description provided for @alertReExamDate.
  ///
  /// In en, this message translates to:
  /// **'Ngày tái khám phải khác ngày hiện tại'**
  String get alertReExamDate;

  /// No description provided for @deleteConsumer.
  ///
  /// In en, this message translates to:
  /// **'Xoá KH'**
  String get deleteConsumer;

  /// No description provided for @deleteCustoumerSuccess.
  ///
  /// In en, this message translates to:
  /// **'Xoá khách hàng thành công'**
  String get deleteCustoumerSuccess;

  /// No description provided for @doYouDeleteCustomer.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn xoá khách hàng?'**
  String get doYouDeleteCustomer;

  /// No description provided for @deleteAssign.
  ///
  /// In en, this message translates to:
  /// **'Xoá gán'**
  String get deleteAssign;

  /// No description provided for @deleteAssignSuccess.
  ///
  /// In en, this message translates to:
  /// **'Xoá gán thành công'**
  String get deleteAssignSuccess;

  /// No description provided for @doYouDeleteAssign.
  ///
  /// In en, this message translates to:
  /// **'Bạn muốn xoá thông tin gán này?'**
  String get doYouDeleteAssign;

  /// No description provided for @isWorkingService.
  ///
  /// In en, this message translates to:
  /// **'Không thể xoá gán, dịch vụ đang làm'**
  String get isWorkingService;

  /// No description provided for @deleteServiceQuestion.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn xóa dịch vụ này?'**
  String get deleteServiceQuestion;

  /// No description provided for @deleteProductQuestion.
  ///
  /// In en, this message translates to:
  /// **'Bạn có chắc muốn xóa sản phẩm này?'**
  String get deleteProductQuestion;

  /// No description provided for @anyoneCanJoin.
  ///
  /// In en, this message translates to:
  /// **'Bất kỳ ai đều có thể tham gia nhóm của bạn bằng cách nhấp vào liên kết này.'**
  String get anyoneCanJoin;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Chia sẻ'**
  String get share;

  /// No description provided for @groupType.
  ///
  /// In en, this message translates to:
  /// **'Loại nhóm'**
  String get groupType;

  /// No description provided for @private.
  ///
  /// In en, this message translates to:
  /// **'Riêng tư'**
  String get private;

  /// No description provided for @chatHistory.
  ///
  /// In en, this message translates to:
  /// **'Lịch sử trò chuyện'**
  String get chatHistory;

  /// No description provided for @show.
  ///
  /// In en, this message translates to:
  /// **'Hiện'**
  String get show;

  /// No description provided for @permissions.
  ///
  /// In en, this message translates to:
  /// **'Phân quyền'**
  String get permissions;

  /// No description provided for @setNewPhoto.
  ///
  /// In en, this message translates to:
  /// **'Đặt ảnh mới'**
  String get setNewPhoto;

  /// Text explaining that the user can add an admin to manage the group.
  ///
  /// In en, this message translates to:
  /// **'Bạn có thể thêm người quản trị để giúp bạn quản lý nhóm của mình.'**
  String get addAdminHelpText;

  /// Title asking what actions a group member can perform.
  ///
  /// In en, this message translates to:
  /// **'THÀNH VIÊN CỦA NHÓM NÀY CÓ THỂ LÀM GÌ?'**
  String get groupMemberActionTitle;

  /// Button text for adding a permission.
  ///
  /// In en, this message translates to:
  /// **'Thêm quyền'**
  String get addPermission;

  /// No description provided for @addException.
  ///
  /// In en, this message translates to:
  /// **'Thêm ngoại lệ'**
  String get addException;

  /// No description provided for @removeAdmin.
  ///
  /// In en, this message translates to:
  /// **'Xóa quản trị viên'**
  String get removeAdmin;

  /// No description provided for @noSendTextPermission.
  ///
  /// In en, this message translates to:
  /// **'Bạn không có quyền gửi tin nhắn'**
  String get noSendTextPermission;

  /// No description provided for @noSendImagePermission.
  ///
  /// In en, this message translates to:
  /// **'Bạn không có quyền gửi ảnh'**
  String get noSendImagePermission;

  /// No description provided for @noSendVideoPermission.
  ///
  /// In en, this message translates to:
  /// **'Bạn không có quyền gửi video'**
  String get noSendVideoPermission;

  /// No description provided for @noSendAudioPermission.
  ///
  /// In en, this message translates to:
  /// **'Bạn không có quyền gửi audio'**
  String get noSendAudioPermission;

  /// No description provided for @noSendPollPermission.
  ///
  /// In en, this message translates to:
  /// **'Bạn không có quyền tạo bình chọn'**
  String get noSendPollPermission;

  /// No description provided for @titleCompanion.
  ///
  /// In en, this message translates to:
  /// **'Người đi cùng khách hàng'**
  String get titleCompanion;

  /// No description provided for @titleJobCompanion.
  ///
  /// In en, this message translates to:
  /// **'Nghề nghiệp'**
  String get titleJobCompanion;

  /// No description provided for @titleRelationshipCompanion.
  ///
  /// In en, this message translates to:
  /// **'Mối quan hệ'**
  String get titleRelationshipCompanion;

  /// No description provided for @titleDescriptionCompanion.
  ///
  /// In en, this message translates to:
  /// **'Đặc điểm'**
  String get titleDescriptionCompanion;

  /// No description provided for @selectCompanion.
  ///
  /// In en, this message translates to:
  /// **'Chọn người đi cùng'**
  String get selectCompanion;

  /// No description provided for @introAddCompanion.
  ///
  /// In en, this message translates to:
  /// **'Nhấn (+) để thêm người đi cùng'**
  String get introAddCompanion;

  /// No description provided for @addCompanion.
  ///
  /// In en, this message translates to:
  /// **'Thêm người'**
  String get addCompanion;

  /// No description provided for @companion.
  ///
  /// In en, this message translates to:
  /// **'Người đi cùng'**
  String get companion;

  /// No description provided for @infoConsultation.
  ///
  /// In en, this message translates to:
  /// **'Thông tin tư vấn'**
  String get infoConsultation;

  /// No description provided for @consultationEmp.
  ///
  /// In en, this message translates to:
  /// **'Nhân viên tư vấn'**
  String get consultationEmp;

  /// No description provided for @consultationEmpBld.
  ///
  /// In en, this message translates to:
  /// **'BLD'**
  String get consultationEmpBld;

  /// No description provided for @consultationRecept.
  ///
  /// In en, this message translates to:
  /// **'Tiền nhận TV'**
  String get consultationRecept;

  /// No description provided for @moneyPt.
  ///
  /// In en, this message translates to:
  /// **'Tiền PT'**
  String get moneyPt;

  /// No description provided for @debtTv.
  ///
  /// In en, this message translates to:
  /// **'Nợ TV'**
  String get debtTv;

  /// No description provided for @moneyDoctor.
  ///
  /// In en, this message translates to:
  /// **'Tiền bác sĩ'**
  String get moneyDoctor;

  /// No description provided for @consultationTvcl.
  ///
  /// In en, this message translates to:
  /// **'TVCL'**
  String get consultationTvcl;

  /// Label for missed call
  ///
  /// In en, this message translates to:
  /// **'Cuộc gọi nhỡ'**
  String get missedCall;

  /// Label for when a call ends
  ///
  /// In en, this message translates to:
  /// **'Kết thúc cuộc gọi'**
  String get callEnded;

  /// Label for outgoing calls
  ///
  /// In en, this message translates to:
  /// **'Cuộc gọi đi'**
  String get outgoingCall;

  /// Label for incoming calls
  ///
  /// In en, this message translates to:
  /// **'Cuộc gọi đến'**
  String get incomingCall;

  /// Unit label for minutes
  ///
  /// In en, this message translates to:
  /// **'Phút'**
  String get minute;

  /// Unit label for seconds
  ///
  /// In en, this message translates to:
  /// **'Giây'**
  String get second;

  ///
  ///
  /// In en, this message translates to:
  /// **'Chức năng gọi tạm thời bị khóa'**
  String get callIsBlocked;

  ///
  ///
  /// In en, this message translates to:
  /// **'Thông tin chứng từ'**
  String get infoDocument;

  ///
  ///
  /// In en, this message translates to:
  /// **'Số CT'**
  String get numberDocument;

  ///
  ///
  /// In en, this message translates to:
  /// **'Số tiền'**
  String get numberMoney;

  ///
  ///
  /// In en, this message translates to:
  /// **'Ngày CT'**
  String get dateDocument;

  ///
  ///
  /// In en, this message translates to:
  /// **'Loại CT'**
  String get typeDocument;

  ///
  ///
  /// In en, this message translates to:
  /// **'Đợt'**
  String get titleReason;

  ///
  ///
  /// In en, this message translates to:
  /// **'Đợt thanh toán'**
  String get reasonPaid;

  ///
  ///
  /// In en, this message translates to:
  /// **'Ngày cần chi'**
  String get datePaid;

  ///
  ///
  /// In en, this message translates to:
  /// **'Danh sách đề xuất'**
  String get suggestList;

  ///
  ///
  /// In en, this message translates to:
  /// **'Bạn chắc chắn muốn từ chối phiếu này?'**
  String get doYouRejectPurchase;

  ///
  ///
  /// In en, this message translates to:
  /// **'Bạn chắc chắn muốn phê duyệt?'**
  String get doYouConfirmPurchase;

  ///
  ///
  /// In en, this message translates to:
  /// **'Yêu cầu đã được phê duyệt'**
  String get requestApproved;

  ///
  ///
  /// In en, this message translates to:
  /// **'Gấp'**
  String get gap;

  ///
  ///
  /// In en, this message translates to:
  /// **'Danh sách phê duyệt'**
  String get approvalList;

  ///
  ///
  /// In en, this message translates to:
  /// **'Lịch sử xử lý'**
  String get processingHistory;

  ///
  ///
  /// In en, this message translates to:
  /// **'Báo giá'**
  String get requestBill;

  ///
  ///
  /// In en, this message translates to:
  /// **'Tin nhắn mới'**
  String get newMessage;

  ///
  ///
  /// In en, this message translates to:
  /// **'Bạn đã gửi từ chối yêu cầu này'**
  String get requestRejected;

  ///
  ///
  /// In en, this message translates to:
  /// **'Duyệt'**
  String get approval2;

  ///
  ///
  /// In en, this message translates to:
  /// **'Không có phiếu nào cần phê duyệt'**
  String get productConfirmEmpty;

  ///
  ///
  /// In en, this message translates to:
  /// **'Bạn sẽ mất thông tin đã nhập nếu bỏ đi?'**
  String get comfirmApprovalSuccess;

  ///
  ///
  /// In en, this message translates to:
  /// **'Cập nhật mới'**
  String get newUpdate;

  ///
  ///
  /// In en, this message translates to:
  /// **'Bỏ đi'**
  String get removeContent;

  ///
  ///
  /// In en, this message translates to:
  /// **'Tiếp tục sửa'**
  String get resumeContent;

  /// No description provided for @companies.
  ///
  /// In en, this message translates to:
  /// **'Danh sách công ty'**
  String get companies;

  /// No description provided for @searchById.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm theo mã phiếu'**
  String get searchById;

  /// No description provided for @byRoom.
  ///
  /// In en, this message translates to:
  /// **'Theo phòng'**
  String get byRoom;

  /// No description provided for @searchRecent.
  ///
  /// In en, this message translates to:
  /// **'Lịch sử tìm kiếm'**
  String get searchRecent;

  /// No description provided for @deleteAll.
  ///
  /// In en, this message translates to:
  /// **'Xoá tất cả'**
  String get deleteAll;

  /// No description provided for @searchByTag.
  ///
  /// In en, this message translates to:
  /// **'Tìm kiếm theo tags'**
  String get searchByTag;

  /// No description provided for @imageByRoom.
  ///
  /// In en, this message translates to:
  /// **'Hình ảnh theo phòng'**
  String get imageByRoom;

  /// No description provided for @setTag.
  ///
  /// In en, this message translates to:
  /// **'Gắn thẻ'**
  String get setTag;

  /// No description provided for @mergedImageFailed.
  ///
  /// In en, this message translates to:
  /// **'Ghép ảnh không thành công, vui lòng thử lại'**
  String get mergedImageFailed;

  /// No description provided for @addTagImageSuccess.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã hoàn tất gắn tags'**
  String get addTagImageSuccess;

  /// No description provided for @singleImage.
  ///
  /// In en, this message translates to:
  /// **'Ảnh đơn'**
  String get singleImage;

  /// No description provided for @mergedImage.
  ///
  /// In en, this message translates to:
  /// **'Ảnh ghép'**
  String get mergedImage;

  /// No description provided for @sampleA.
  ///
  /// In en, this message translates to:
  /// **'Mẫu A'**
  String get sampleA;

  /// No description provided for @sampleB.
  ///
  /// In en, this message translates to:
  /// **'Mẫu B'**
  String get sampleB;

  /// No description provided for @selectedImage.
  ///
  /// In en, this message translates to:
  /// **'Đã chọn {count} ảnh'**
  String selectedImage(String count);

  /// No description provided for @addTags.
  ///
  /// In en, this message translates to:
  /// **'Gắn tags'**
  String get addTags;

  /// No description provided for @searchCustomerToAddTag.
  ///
  /// In en, this message translates to:
  /// **'Tìm khách hàng để gắn tags'**
  String get searchCustomerToAddTag;

  /// No description provided for @selectTags.
  ///
  /// In en, this message translates to:
  /// **'Chọn tags'**
  String get selectTags;

  /// No description provided for @popularTags.
  ///
  /// In en, this message translates to:
  /// **'Các tags phổ biến'**
  String get popularTags;

  /// No description provided for @imageIsSelected.
  ///
  /// In en, this message translates to:
  /// **'Hình đã chọn'**
  String get imageIsSelected;

  /// No description provided for @homePin.
  ///
  /// In en, this message translates to:
  /// **'Tính năng ghim'**
  String get homePin;

  /// No description provided for @homeMain.
  ///
  /// In en, this message translates to:
  /// **'Tính năng chính'**
  String get homeMain;

  /// No description provided for @notFoundHistory.
  ///
  /// In en, this message translates to:
  /// **'Chưa có lịch sử'**
  String get notFoundHistory;

  /// No description provided for @deleteTag.
  ///
  /// In en, this message translates to:
  /// **'Xoá tags'**
  String get deleteTag;

  /// No description provided for @messageDeletedTag.
  ///
  /// In en, this message translates to:
  /// **'Bạn đã xoá tags thành công'**
  String get messageDeletedTag;

  /// No description provided for @doYouDeleteTags.
  ///
  /// In en, this message translates to:
  /// **'Bạn chắc chắn muốn xoá tags ảnh này?'**
  String get doYouDeleteTags;

  /// No description provided for @con.
  ///
  /// In en, this message translates to:
  /// **'Còn'**
  String get con;

  /// No description provided for @notContent.
  ///
  /// In en, this message translates to:
  /// **'Không có nội dung'**
  String get notContent;

  /// No description provided for @combo.
  ///
  /// In en, this message translates to:
  /// **'Combo'**
  String get combo;

  /// No description provided for @deal.
  ///
  /// In en, this message translates to:
  /// **'Deal'**
  String get deal;

  /// No description provided for @addService.
  ///
  /// In en, this message translates to:
  /// **'Thêm dịch vụ'**
  String get addService;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'vi'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'vi':
      return AppLocalizationsVi();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
