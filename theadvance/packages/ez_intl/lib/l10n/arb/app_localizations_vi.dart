// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get appName => 'The Advance';

  @override
  String get service => 'Dịch vụ';

  @override
  String get showDirection => 'Chỉ đường';

  @override
  String get appointmentApproved => 'approved';

  @override
  String get appointmentWaitingApproved => 'waiting_approve';

  @override
  String get modifyAppointment => 'Sửa lịch';

  @override
  String get cancelAppointment => 'Huỷ lịch';

  @override
  String get guestUser => 'Guest user';

  @override
  String get loginByPhoneNumber => 'Đăng nhập bằng số điện thoại';

  @override
  String get unknown => 'Có lỗi xảy ra, vui lòng thử lại sau';

  @override
  String get tokenExpired =>
      'Phiên đăng nhập đã hết hạn, vui lòng đăng nhập lại';

  @override
  String get supportRequest => 'Yêu cầu hỗ trợ';

  @override
  String get inboxIsEmpty => 'Bạn chưa có thư hỗ trợ nào';

  @override
  String get servicesIsEmpty => 'Bạn chưa đặt dịch vụ nào';

  @override
  String get createSupportRequest => 'Tạo yêu cầu hỗ trợ';

  @override
  String get confirmLogout => 'Bạn có muốn đăng xuất tài khoản này?';

  @override
  String get confirmCancelAppointment => 'Bạn có chắc chắn muốn huỷ lịch?';

  @override
  String get callYouLater =>
      'Sẽ có nhân viên gọi điện xác nhận trong thời gian sớm nhất';

  @override
  String get logout => 'Đăng xuất';

  @override
  String get account => 'Tài khoản';

  @override
  String get cancel => 'Hủy';

  @override
  String get history => 'Lịch sử';

  @override
  String get referral => 'Giới thiệu khách hàng';

  @override
  String get contactUs => 'Liên hệ';

  @override
  String get collaborator => 'Cộng tác viên';

  @override
  String get notUpdated => 'Chưa cập nhật';

  @override
  String get fullName => 'Họ và tên';

  @override
  String get email => 'Email';

  @override
  String get phoneNumber => 'Số điện thoại';

  @override
  String get address => 'Địa chỉ';

  @override
  String get save => 'Lưu';

  @override
  String get edit => 'Sửa';

  @override
  String get errorOccurred => 'Có lỗi xảy ra khi cập nhật thông tin';

  @override
  String get errorEmptyFullName => 'Vui lòng nhập họ và tên';

  @override
  String get updateProfileSuccessful => 'Cập nhật thông tin cá nhân thành công';

  @override
  String get personalInfo => 'Thông tin cá nhân';

  @override
  String get emailHaveWhiteSpace => 'Email không được chứa khoảng trắng';

  @override
  String get emailWrongFormat => 'Email không đúng định dạng';

  @override
  String get close => 'Đóng';

  @override
  String get openSettings => 'Mở cài đặt';

  @override
  String get settings => 'Cài đặt chung';

  @override
  String get fonts => 'Phông chữ';

  @override
  String get fontSize => 'Cỡ chữ';

  @override
  String get smallSize => 'Cỡ nhỏ';

  @override
  String get bigSize => 'Cỡ lớn';

  @override
  String get done => 'Xong';

  @override
  String get referralContent =>
      'Giới thiệu khách hàng bằng cách chia sẻ mã giới thiệu hoặc nhập thông tin theo mẫu dưới để The Advance có thể hỗ trợ.';

  @override
  String get pickServices => 'Lựa chọn quà tặng';

  @override
  String get pickYears => 'Chọn năm sinh';

  @override
  String get send => 'Gửi';

  @override
  String get inputContent => 'Nhập nội dung';

  @override
  String get notes => 'Ghi chú';

  @override
  String get gift => 'Quà tặng';

  @override
  String get years => 'Năm sinh (*)';

  @override
  String get phoneHintText => 'Nhập số điện thoại (bắt buộc)';

  @override
  String get phoneText => 'Số điện thoại (*)';

  @override
  String get fullNameHintText => 'Họ tên (bắt buộc)';

  @override
  String get fullNameText => 'Họ tên (*)';

  @override
  String get pickGift => 'Chọn quà tặng';

  @override
  String get notEnoughInfo => 'Vui lòng nhập đầy đủ thông tin bắt buộc';

  @override
  String get ageRequired =>
      'Số tuổi không hợp lệ, phải lớn hơn hoặc bằng 25 tuổi';

  @override
  String get resendOTP => 'Gửi lại OTP';

  @override
  String get confirmOTP => 'Xác thực OTP';

  @override
  String get otpInfo => 'Một mã xác thực đã được gửi đến \n';

  @override
  String get continuous => 'Tiếp tục';

  @override
  String get appointments => 'Lịch hẹn';

  @override
  String get appointmentDetails => 'Chi tiết lịch hẹn';

  @override
  String get phoneIncorrect =>
      'Số điện thoại không đúng, vui lòng kiểm tra lại';

  @override
  String get formIncorrect => 'Thông tin không hợp lệ, vui lòng kiểm tra lại';

  @override
  String get loginMessage =>
      'Quý khách vui lòng dùng số điện thoại đã cung cấp khi dùng dịch vụ tại Ngoc Dung Beauty';

  @override
  String get agree => 'Tôi đồng ý với ';

  @override
  String get termsAndConditions => 'điều kiện và điều khoản sử dụng';

  @override
  String get copyright => 'của The Advance';

  @override
  String get uploadAvatarSuccessful => 'Cập nhật hình ảnh đại diện thành công';

  @override
  String get startUsingApp => 'Bắt đầu trải nghiệm';

  @override
  String get skip => 'Để sau';

  @override
  String get emptyPage => 'Chưa có nội dung';

  @override
  String get accept => 'Đồng ý';

  @override
  String get requestLocation => 'Vui lòng cho phép ứng dụng truy cập vị trí';

  @override
  String get getLocationSuccesful => 'Lấy thông tin vị trí hoàn tất';

  @override
  String get getLocationFailure => 'Lấy thông tin vị trí thất bại';

  @override
  String get connectionError =>
      'Không có kết nối internet, vui lòng kiểm tra lại';

  @override
  String get referralCode => 'Mã giới thiệu';

  @override
  String get referralMessage =>
      'CTV có thể chia sẻ mã bằng đưa voucher có mã CTV (Voucher do The Advance cung cấp) hoặc chia sẻ QR code cho KH, KH sau đó có thể sử dụng voucher hoặc mã QR khi thanh toán.';

  @override
  String get shareQRCode => 'Chia sẻ mã QR';

  @override
  String get invalidScannedCode => 'Thông tin quét mã không đúng';

  @override
  String get referralCollaborator => 'Giới thiệu CTV';

  @override
  String get news => 'Tin tức';

  @override
  String get androidConfirmExit => 'Chạm lần nữa để thoát ứng dụng';

  @override
  String get home => 'Trang chủ';

  @override
  String get sendFeedback => 'Gửi đánh giá';

  @override
  String get sendResponse => 'Gửi phản hồi';

  @override
  String get feedbackServiceMessage => 'Vui lòng đánh giá sao dịch vụ';

  @override
  String get feedbackServiceTitle => 'Đánh giá dịch vụ';

  @override
  String get addMoreComment => 'Nhập thêm ý kiến';

  @override
  String get inputPhoneNumber => 'Nhập số điện thoại';

  @override
  String get needSupportRequestType => 'Yêu cầu cần hỗ trợ (không bắt buộc)';

  @override
  String get chooseSupportRequestType => 'Chọn yêu cầu';

  @override
  String get selectChooseSupportRequestType => 'Lựa chọn yêu cầu';

  @override
  String get noteSupportRequest => 'Ghi chú';

  @override
  String get inputInformationSupportRequest => 'Nhập nội dung';

  @override
  String get missingInformationSupportRequest => 'Thiếu thông tin';

  @override
  String get hintNeedSupportRequest => 'Vui lòng nhập nội dung yêu cầu hỗ trợ';

  @override
  String get sendSupportRequestSuccessfully => 'Đã gửi yêu cầu hỗ trợ';

  @override
  String get sendSupportRequestFail => 'Gửi yêu cầu hỗ trợ thất bại';

  @override
  String get alert => 'Thông báo';

  @override
  String get requestPermissionLibrary =>
      'Mở quyền cho phép truy cập thư viện ảnh để sử dụng được tính năng này';

  @override
  String get requestPermissionCamera =>
      'Mở quyền cho phép truy cập camera để sử dụng được tính năng này';

  @override
  String get requestPermissionStorage =>
      'Mở quyền cho phép truy cập tập tin để sử dụng được tính năng này';

  @override
  String get requestPermissionMicro =>
      'Mở quyền cho phép truy cập micro để sử dụng được tính năng này';

  @override
  String get requestPermissionNotification =>
      'Mở quyền cho phép thông báo để sử dụng được tính năng này';

  @override
  String get takePicture => 'Chụp ảnh';

  @override
  String get retakePicture => 'Chụp lại';

  @override
  String get photoLibrary => 'Thư viện ảnh';

  @override
  String get welcome => 'Xin chào';

  @override
  String get calendarEvents => 'Lịch sự kiện';

  @override
  String get calendarOthersEvents => 'Lịch sự kiện khác';

  @override
  String get detailEvent => 'Chi tiết sự kiện';

  @override
  String get seeMore => 'Xem thêm';

  @override
  String get collapse => 'Thu gọn';

  @override
  String get subscribeEvent => 'ĐĂNG KÝ THAM GIA';

  @override
  String get subscribedEventMessage => 'BẠN ĐÃ ĐĂNG KÝ THAM GIA SỰ KIỆN NÀY';

  @override
  String get subscribeEventSuccess => 'Đăng ký tham dự thành công';

  @override
  String get waitForUsAcceptEvent => 'Vui lòng chờ xác nhận của chúng tôi';

  @override
  String get subscribedEvent => 'Sự kiện đã đăng ký';

  @override
  String get detail => 'Chi tiết';

  @override
  String get eVoucher => 'E-Vouchers';

  @override
  String get viewAll => 'Xem tất cả';

  @override
  String get inputVoucherCode => 'Nhập mã Vouchers';

  @override
  String get getItNow => 'Đổi ngay';

  @override
  String get detailShop => 'Chi tiết ưu đãi';

  @override
  String get detailEvaluationResult => 'Chi tiết kết quả đánh giá';

  @override
  String get getItSuccess => 'Đổi thành công';

  @override
  String get getItShopMessageDone => 'BẠN ĐÃ ĐÃ ĐỔI ƯU ĐÃI NÀY';

  @override
  String get promotion => 'Khuyến mãi';

  @override
  String get promotionOthers => 'Các Ưu đãi khác';

  @override
  String get subscribedEventCaution => 'Bạn muốn nhận vé tham gia sự kiện này';

  @override
  String get confirm => 'Xác nhận';

  @override
  String get ofYou => 'Của bạn';

  @override
  String get expired => 'Hết hạn';

  @override
  String get rewardPoints => 'Điểm Thưởng';

  @override
  String get historyOfRewardPoints => 'Lịch sử điểm thưởng';

  @override
  String get preferentialShop => 'Shop quà';

  @override
  String get currentlyUnderDevelopment =>
      'Tính năng này hiện đang được phát triển !';

  @override
  String get keepAccumulatingPoints =>
      'Hãy tiếp tục tích điểm để nhận nhiều ưu đãi hơn đến từ TMV The Advance.';

  @override
  String get aboutYou => 'Dành cho bạn';

  @override
  String get vipSilver => 'Thành viên bạc';

  @override
  String get vipGold => 'Thành viên vàng';

  @override
  String get vipDiamond => 'Thành viên kim cương';

  @override
  String get member => 'Thành viên';

  @override
  String get hotNews => 'Tin tức nổi bật';

  @override
  String get version => 'Phiên bản';

  @override
  String get notYetRated => 'Chưa có hạng';

  @override
  String get category => 'Danh mục';

  @override
  String get sort => 'Sắp xếp';

  @override
  String get getThisGift => 'Bạn muốn đổi phần quà này?';

  @override
  String get amount => 'Số lượng';

  @override
  String get jumpToTop => 'TRỞ LÊN TRÊN';

  @override
  String get myGift => 'Quà của bạn';

  @override
  String get giftCode => 'Mã phần quà: ';

  @override
  String get newReward => 'Mới';

  @override
  String get rewardUsed => 'Đã dùng';

  @override
  String get transactionHistory => 'Lịch sử giao dịch';

  @override
  String get emptyTransaction => 'Không có giao dịch';

  @override
  String get usedPromotion => 'ĐÃ SỬ DỤNG ƯU ĐÃI';

  @override
  String get allowNotification => 'Cho phép thông báo';

  @override
  String get beautyLive => 'Beauty Live';

  @override
  String get liveStream => 'Đang live stream';

  @override
  String get previousStream => 'Live stream trước';

  @override
  String get playingStream => 'Đang phát: ';

  @override
  String get joinEventCode => 'Mã tham gia sự kiện: ';

  @override
  String get voucherCode => 'Mã voucher';

  @override
  String get giveVouchers => 'Tặng Vouchers';

  @override
  String get name => 'Họ tên';

  @override
  String get brother => 'Anh';

  @override
  String get sister => 'Chị';

  @override
  String get give => 'Tặng';

  @override
  String get infoReceivedPerson => 'Thông tin người nhận';

  @override
  String get giveSuccessfulGift => 'Bạn đã tặng Voucher thành công';

  @override
  String get schedulerTitle => 'Đặt lịch';

  @override
  String get findNearestBranch => 'TÌM CHI NHÁNH GẦN NHẤT';

  @override
  String get choiceAppointmentDay => 'CHỌN THỜI GIAN';

  @override
  String get back => 'Trở về';

  @override
  String get next => 'Tiếp';

  @override
  String get choiceService => 'CHỌN DỊCH VỤ';

  @override
  String get chosenInfo => 'THÔNG TIN ĐÃ CHỌN';

  @override
  String get branchSelectionStep => 'Chọn chi nhánh';

  @override
  String get timeSelectionStep => 'Chọn thời gian';

  @override
  String get serviceSelectionStep => 'Chọn dịch vụ';

  @override
  String get finishStep => 'Hoàn tất';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get type => 'Loại';

  @override
  String get haveANote => 'Bạn có một lời nhắc';

  @override
  String get letNgocDungKnowYou => 'Hãy cho The Advance biết về bạn!';

  @override
  String get yourName => 'Tên của bạn là gì?';

  @override
  String get letLocation => 'Cho phép vị trí';

  @override
  String get beginDiscovery => 'Bắt đầu khám phá';

  @override
  String get thankToScheduler => 'Cám ơn bạn đã gửi thông tin đặt lịch';

  @override
  String get maybeHaveStaffCallYouSoon =>
      'Sẽ có nhân viên gọi điện xác nhận trong thời gian sớm nhất.';

  @override
  String get choiceProvince => 'Chọn tỉnh thành';

  @override
  String get notCompleteStep => 'Bạn chưa hoàn thành bước này';

  @override
  String get part => 'phần';

  @override
  String get failToFetchSupportRequest => 'failed to fetch support requests';

  @override
  String get expiredGift => 'Quà hết hạn';

  @override
  String get successReceiveGift => 'Nhận quà thành công';

  @override
  String get receiveGift => 'Nhận quà';

  @override
  String get wantToReceiveGift => 'Bạn muốn nhận phần quà này?';

  @override
  String get scanVoucher => 'Quét Voucher';

  @override
  String get giveVoucher => 'Tặng Voucher';

  @override
  String get rank => 'Hạng';

  @override
  String get schedulerNow => 'Đặt lịch ngay';

  @override
  String get readAllMessage =>
      'Bạn có chắc chắn muốn đánh dấu đã đọc tất cả thông báo?';

  @override
  String get font => 'Fonts';

  @override
  String get haveNotAppointment => 'Bạn chưa có lịch hẹn nào';

  @override
  String get choiceSchedule => 'Vui lòng chọn Đặt lịch để tạo lịch hẹn mới.';

  @override
  String get receivePromotion => 'Nhận ưu đãi';

  @override
  String get notAllowedToExchangeGift => 'Quý khách không đủ điều kiện đổi quà';

  @override
  String get beautyNews => 'Tin tức làm đẹp';

  @override
  String get expertTitle => 'Chuyên gia';

  @override
  String get mainExpertTitle => 'Video';

  @override
  String get follow => 'Theo dõi';

  @override
  String get unfollow => 'Bỏ theo dõi';

  @override
  String get expertPart => 'Góc chuyên gia';

  @override
  String get hotVideos => 'Video nổi bật';

  @override
  String get viewResponse => 'Xem phản hồi';

  @override
  String get comment => 'Bình luận';

  @override
  String get cannotLoadVideo => 'Không thể hiển thị video';

  @override
  String get addComment => 'Thêm bình luận';

  @override
  String get noComment => 'Không có bình luận';

  @override
  String get codeCheckIn => 'Mã check in';

  @override
  String get code => 'Mã số';

  @override
  String get checkin => 'Check in';

  @override
  String get survey => 'Khảo sát';

  @override
  String get noMoreVideo => 'Chưa có video mới';

  @override
  String get systemBranch => 'Hệ thống chi nhánh';

  @override
  String get hotline => 'Hotline';

  @override
  String get choiceDistrict => 'Chọn Quận/Huyện';

  @override
  String get choiceWard => 'Chọn Phường/Xã';

  @override
  String get noPhone => 'Không có số điện thoại';

  @override
  String get noCreateAppointment => 'Hiện tại không thể đặt lịch';

  @override
  String get newsCare => 'Chăm sóc sau làm';

  @override
  String get emptyBranch => 'Không có chi nhánh';

  @override
  String get connectUs => 'Kết nối với chúng tôi:';

  @override
  String get surveyMsg => 'BẠN QUAN TÂM DỊCH VỤ NÀO CỦA The Advance ?';

  @override
  String get emailLogin => 'Email đăng nhập';

  @override
  String get password => 'Mật khẩu';

  @override
  String get login => 'Đăng nhập';

  @override
  String get exitAccount => 'Thoát tài khoản';

  @override
  String get ignore => 'Bỏ qua';

  @override
  String get contentTouchId =>
      'Sử dụng dấu vân tay để mở khóa ứng dụng một cách nhanh chóng và tiện lợi';

  @override
  String get titleTouchId => 'Mở khóa bằng vân tay';

  @override
  String get turnOn => 'Bật';

  @override
  String get turnOff => 'Tắt';

  @override
  String get loginTouchId => 'Đăng nhập bằng vân tay';

  @override
  String get localizeReasonTouchId => 'Quét vân tay của bạn để xác thực';

  @override
  String get fingerprintRequired => 'Yêu cầu đăng kí vân tay';

  @override
  String get iOSLockOut =>
      'Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.';

  @override
  String get androidFingerprintSuccess => 'Nhận dạng thành công';

  @override
  String get androidFingerprintNotRecognized =>
      'Nhận dạng thái bại! Xin thử lại!';

  @override
  String get events => 'Sự kiện';

  @override
  String get noTicketSaved => 'Không có mã tài sản mới cần lưu !';

  @override
  String get dailyInventory => 'Kiểm kê thiết bị hằng ngày';

  @override
  String get normalInventory => 'Kiểm kê thông thường';

  @override
  String get searchInfoSuccess => 'Tìm thông tin thành công';

  @override
  String get searchInfoFailure => 'Không tìm thấy thông tin';

  @override
  String get cantInputManual =>
      'Tài sản này không được nhập tay, vui lòng dùng chức năng Quét QRCode';

  @override
  String get inventoryTicket => 'Phiếu kiểm kê';

  @override
  String get inventory => 'Kiểm kê';

  @override
  String get agency => 'Chi nhánh';

  @override
  String get accountLogin => 'Tài khoản đăng nhập';

  @override
  String get hello => 'Xin chào';

  @override
  String get emailEmpty => 'Email không được bỏ trống';

  @override
  String get passwordHaveWhiteSpace => 'Mật khẩu không được chứa khoảng trắng';

  @override
  String get passEmpty => 'Mật khẩu không được bỏ trống';

  @override
  String get passwordWrongFormat => 'Mật khẩu ít nhất phải là 6 ký tự';

  @override
  String get loginWithFingerprint => 'Đăng nhập bằng vân tay';

  @override
  String get logoutAccount => 'THOÁT TÀI KHOẢN';

  @override
  String get countTicket => 'Số phiếu';

  @override
  String get department => 'Bộ phận';

  @override
  String get validateFormInventory => 'Vui lòng hoàn thành tất cả thông tin';

  @override
  String get needInventory => 'Cần kiểm kê';

  @override
  String get inventoried => 'Đã kiểm kê';

  @override
  String get scanQRCode => 'Quét QR Code';

  @override
  String get inputBarcode => 'Nhập tay';

  @override
  String get assetBarcode => 'Mã code tài sản';

  @override
  String get complete => 'Hoàn tất';

  @override
  String get completeThisAssetInventory => 'Tài sản đã kiểm kê trước đó';

  @override
  String get completedInventory => 'Kiểm kê thành công';

  @override
  String get notFindInventory => 'Tài sản không thuộc bộ phận hiện tại !';

  @override
  String get ok => 'OK';

  @override
  String get staff => 'Nhân viên';

  @override
  String get staffInventory => 'Nhân viên kiểm kê';

  @override
  String get staffAccountant => 'Nhân viên kế toán';

  @override
  String get staffOther => 'Nhân viên khác';

  @override
  String get listInventory => 'Danh sách kiểm kê';

  @override
  String get createNewInventory => 'Tạo phiếu kiểm kê mới';

  @override
  String get search => 'Tìm kiếm';

  @override
  String get listIsEmpty => 'Danh sách trống';

  @override
  String get filterBy => 'Lọc theo';

  @override
  String get filter => 'LỌC';

  @override
  String get fromDay => 'Từ ngày';

  @override
  String get toDay => 'Đến ngày';

  @override
  String get checkIn => 'Chấm công vào';

  @override
  String get checkOut => 'Chấm công ra';

  @override
  String get unmap => 'unmap';

  @override
  String get ticketCode => 'Mã check in';

  @override
  String get voucherCheckIn => 'Voucher check in';

  @override
  String get scan => 'Quét';

  @override
  String get reward => 'Trao thưởng';

  @override
  String get rewardTitle => 'Voucher nhận quà';

  @override
  String get confirmCheckInMessage =>
      'Bạn có chắc chắn check in cho khách hàng này?';

  @override
  String get confirmCheckOutMessage =>
      'Bạn có chắc chắn check out cho khách hàng này?';

  @override
  String get confirmRewardMessage =>
      'Bạn có chắc chắn trao quà cho khách hàng này?';

  @override
  String get confirmUnmapVoucherMessage =>
      'Bạn có chắc chắn hủy voucher của khách hàng này?';

  @override
  String get ticketCodeIsEmpty => 'Mã check in không được để trống';

  @override
  String get voucherCodeIsEmpty => 'Mã voucher không được để trống';

  @override
  String get rankIsEmpty => 'Giải thưởng không được để trống';

  @override
  String get politePrefix => 'Vui lòng nhập';

  @override
  String get yearOfBirth => 'Năm sinh';

  @override
  String get voucherCheckInIsEmptywarningText => 'Chưa nhập voucher check in';

  @override
  String get createTicket => 'TẠO TICKET';

  @override
  String get allowLocation => 'Cho phép vị trí';

  @override
  String get getStarted => 'Bắt đầu khám phá';

  @override
  String get accountEmpty => 'Chưa điền tài khoản đăng nhập';

  @override
  String get branchEmpty => 'Chưa chọn chi nhánh';

  @override
  String get approval => 'Phê duyệt';

  @override
  String get waiting => 'Đang chờ';

  @override
  String get approved => 'Đã duyệt';

  @override
  String get rejected => 'Đã từ chối';

  @override
  String get myApproval => 'Phê duyệt của tôi';

  @override
  String get staffCode => 'Mã nhân viên';

  @override
  String get staffName => 'Tên nhân viên';

  @override
  String get sendDate => 'Ngày gửi';

  @override
  String get editCheckIn => 'Ngày chỉnh công';

  @override
  String get totalCheckIn => 'Tổng ngày công';

  @override
  String get checkInTime => 'Giờ check - in';

  @override
  String get checkOutTime => 'Giờ check out';

  @override
  String get typeUpdateCheckIn => 'Loại công cập nhật';

  @override
  String get hour => 'giờ';

  @override
  String get sendAWish => 'gửi lời chúc';

  @override
  String get waitingApprove => 'chờ duyệt';

  @override
  String get mine => 'của tôi';

  @override
  String get reject => 'Từ chối';

  @override
  String get emptyHistoryApproval => 'Yêu cầu không có lịch sử nào?';

  @override
  String get reason => 'Lý do';

  @override
  String get hintReason => 'Nhập nội dung ...';

  @override
  String get choice => 'Chọn theo';

  @override
  String get typeAsset => 'Loại tài sán';

  @override
  String get nameAsset => 'Tên tài sán';

  @override
  String get price => 'Giá';

  @override
  String get recommendationList => 'DANH SÁCH GỢI Ý';

  @override
  String get recentSearch => 'TÌM KIẾM GẦN ĐÂY';

  @override
  String get chat => 'Chats';

  @override
  String get skype => 'Skype';

  @override
  String get contract => 'Hợp đồng lao động';

  @override
  String get position => 'Chức vụ';

  @override
  String get status => 'Trạng thái';

  @override
  String get reportTo => 'Báo cáo cho';

  @override
  String get group => 'Nhóm';

  @override
  String get gender => 'Giới tính';

  @override
  String get dob => 'Ngày sinh';

  @override
  String get form => 'Hình thức';

  @override
  String get totalSalary => 'TỔNG LƯƠNG';

  @override
  String get ctt => 'Công thực tế';

  @override
  String get timekeeper => 'Máy chấm công';

  @override
  String get takingCheckinPhoto => 'Chụp ảnh check-in';

  @override
  String get wordkedDay => 'Công tính lương';

  @override
  String get payrollRate => 'Tỉ lệ nhận lương';

  @override
  String get annualLeave => 'Phép năm';

  @override
  String get unusedAnnualLeave => 'Ngày phép còn lại';

  @override
  String get timekeepingOverview => 'Tổng hợp chấm công';

  @override
  String get day => 'ngày';

  @override
  String get salaryDetail => 'Chấm công chi tiết';

  @override
  String get month => 'tháng';

  @override
  String get year => 'năm';

  @override
  String get totalWorkInDay => 'Tổng công trong ngày';

  @override
  String get timeCheckIn => 'giờ vào';

  @override
  String get timeCheckOut => 'giờ ra';

  @override
  String get createUpdateWorkRequest => 'Tạo yêu cầu sửa công';

  @override
  String get workType => 'Loại công';

  @override
  String get totalWork => 'Tổng công';

  @override
  String get request => 'yêu cầu';

  @override
  String get daysOff => 'nghỉ phép';

  @override
  String get daysOffLeft => 'Ngày phép còn';

  @override
  String get business => 'công tác';

  @override
  String get purpose => 'mục đích';

  @override
  String get mth => 'THG';

  @override
  String get areYouSureSigningOut => 'Bạn có chắc chắn thoát tài khoản?';

  @override
  String get workingProgress => 'Quá trình làm việc';

  @override
  String get connectionErrorPleaseRefresh =>
      'Lỗi kết nối, vui lòng tải lại trang';

  @override
  String get refresh => 'Tải lại';

  @override
  String get prize => 'Giải thưởng';

  @override
  String get check => 'Kiểm tra';

  @override
  String get change => 'Thay đổi';

  @override
  String get noRouteDefinedYet => 'Chưa có đường dẫn';

  @override
  String get otpCodeIsNotEnoughNumbers => 'Mã xác thực chưa đủ 4 số';

  @override
  String get statistic => 'Thống kê';

  @override
  String get inputPhoneReceiver => 'Nhập số điện thoại người nhận';

  @override
  String get noMessage => 'Không có tin nhắn';

  @override
  String get confirmSendImage => 'Bạn có chắc muốn gửi ảnh này ?';

  @override
  String get donotJob => 'Chưa xử lí';

  @override
  String get expiredJob => 'Quá hạn';

  @override
  String get doneJob => 'Đã xử lí';

  @override
  String get createTicketSuccess => 'Tạo phiếu thành công';

  @override
  String get validateInventoryType => 'Vui Lòng lựa chọn hình thức kiểm kê';

  @override
  String get inputOTP => 'Nhập mã OTP';

  @override
  String get isReceiveOTP => 'Bạn chưa nhận được?';

  @override
  String get forgetPassword => 'Quên mật khẩu?';

  @override
  String get inputPassword => 'Nhập mật khẩu';

  @override
  String get setPassword => 'Đặt mật khẩu';

  @override
  String get total => 'Tổng';

  @override
  String get selectMonth => 'Chọn tháng';

  @override
  String get goToWorkLate => 'đi muộn';

  @override
  String get offWork => 'nghỉ làm';

  @override
  String get timeAttendance => 'Chấm công';

  @override
  String get updateCheckin => 'Cập nhật công';

  @override
  String get fetchHistoryCheckinFailed => 'Có lỗi tải dữ liệu chấm công';

  @override
  String get fetchWorkTypeFailed => 'Có lỗi tải danh sách loại công';

  @override
  String get requestUpdateCheckinFailed => 'Có lỗi yêu cầu chỉnh sửa công';

  @override
  String get expiredDate => 'Hạn ngày';

  @override
  String get detailJob => 'Chi tiết công việc';

  @override
  String get from => 'Từ';

  @override
  String get attachedFile => 'File đính kèm';

  @override
  String get conservation => 'Thảo luận';

  @override
  String get feedback => 'Phản hồi';

  @override
  String get asignee => 'Người thực hiện';

  @override
  String get sendFeedBack => 'Gửi phản hồi';

  @override
  String get fileAttach => 'Đính kèm file';

  @override
  String get sendFeedBackSuccess => 'Gửi phản hồi thành công';

  @override
  String get checkinFailure => 'Check in thất bại';

  @override
  String get tryAgain => 'Thử lại';

  @override
  String get timekeeping => 'Chấm công';

  @override
  String get checkinSuccess => 'Check in thành công';

  @override
  String get getIn => 'Vào';

  @override
  String get more => 'Thêm';

  @override
  String get open => 'Mở';

  @override
  String get defaultError => 'Đã có lỗi xảy ra';

  @override
  String get defaultText => 'Mặc định';

  @override
  String get creatingWork => 'Tạo công việc';

  @override
  String get title => 'Tiêu đề';

  @override
  String get dateExpired => 'Hạn ngày';

  @override
  String get prioritize => 'Ưu tiên';

  @override
  String get creatWork => 'Tự tạo việc';

  @override
  String get choiceOffice => 'Chọn phòng ban';

  @override
  String get create => 'Tạo';

  @override
  String get yourChoice => 'Bạn đã chọn';

  @override
  String get findByName => 'Tìm theo tên';

  @override
  String get choiceMember => 'Chọn thành viên';

  @override
  String get choiceAll => 'Chọn tất cả';

  @override
  String get confirmPassword => 'Xác nhận mật khẩu';

  @override
  String get wrongConfirmPass => 'Mật khẩu xác nhận không đúng';

  @override
  String get loginBy => 'Đăng nhập bằng';

  @override
  String get loginByFingerPrint => 'vân tay cho những lần sau';

  @override
  String get loginByFaceID => 'Face ID cho những lần sau';

  @override
  String get asset => 'Tài sản';

  @override
  String get socialInsurance => 'Bảo hiểm xã hội';

  @override
  String get laborContract => 'Hợp đồng lao động';

  @override
  String get indentify => 'Số CCCD';

  @override
  String get birthday => 'Ngày tháng năm sinh';

  @override
  String get numberBank => 'Số tài khoản ngân hàng';

  @override
  String get company => 'Công ty';

  @override
  String get positionMember => 'Chức danh';

  @override
  String get office => 'Phòng ban';

  @override
  String get productCode => 'Mã hàng hóa';

  @override
  String get assetCode => 'Mã tài sản';

  @override
  String get productName => 'Tên hàng hóa';

  @override
  String get activeDate => 'Ngày hiệu lực';

  @override
  String get employeePart => 'Phần người lao động';

  @override
  String get getImageInCamera => 'Chụp ảnh mới';

  @override
  String get getImageInGallery => 'Chọn ảnh có sẵn';

  @override
  String get avatar => 'Ảnh đại diện';

  @override
  String get titleNoti => 'Tin tức';

  @override
  String get notSupportDevice =>
      'Thiết bị của bạn không cài đặt mở khóa khuôn mặt và vân tay';

  @override
  String get approvalTitle => 'Duyệt';

  @override
  String get rejectReason => 'Lý do từ chối';

  @override
  String get typeApprove => 'Loại duyệt';

  @override
  String get signal => 'Chữ ký';

  @override
  String get otp => 'OTP';

  @override
  String get sendEform => 'Gửi yêu cầu';

  @override
  String get detailEform => 'Chi tiết yêu cầu';

  @override
  String get sender => 'Người gửi';

  @override
  String get time => 'Thời gian';

  @override
  String get loginFaceID => 'Đăng nhập bằng khuôn mặt';

  @override
  String get creatWorkSuccess => 'Bạn đã tạo công việc thành công';

  @override
  String get authenRequired => 'Yêu cầu xác thực';

  @override
  String get authenToLogin => 'Vui lòng xác thực để đăng nhập';

  @override
  String get repeatWork => 'Công việc lặp lại';

  @override
  String get repeatWorkTitle => 'Hình thức lặp lại';

  @override
  String get msgDoneTask => 'Hoàn thành công việc';

  @override
  String get infoAccount => 'Thông tin tài khoản';

  @override
  String get mainScreen => 'Màn hình chính';

  @override
  String get pickHistoryCheckinDate => 'Bảng chấm công';

  @override
  String get content => 'Nội dung';

  @override
  String get image => 'Ảnh';

  @override
  String get language => 'Ngôn ngữ';

  @override
  String get biometricVerification => 'Xác thực sinh trắc học';

  @override
  String get turnOnFaceID => 'Bật tính năng mở bằng khuôn mặt';

  @override
  String get turnOffFaceID => 'Tắt tính năng mở bằng khuôn mặt';

  @override
  String get turnOnTouchID => 'Bật tính năng mở bằng vân tay';

  @override
  String get turnOffTouchID => 'Tắt tính năng mở bằng vân tay';

  @override
  String get setUpBiometrics =>
      'Vui lòng thiết lập mở khóa vân tay hoặc khuôn mặt';

  @override
  String get vnLanguage => 'Tiếng Việt';

  @override
  String get enLanguage => 'English';

  @override
  String get infoCustomer => 'Thông tin khách hàng';

  @override
  String get job => 'Nghề nghiệp';

  @override
  String get pathological => 'Bệnh lý khách hàng';

  @override
  String get expression => 'Những biểu hiện của KH';

  @override
  String get otherExpression => 'Biểu hiện khác';

  @override
  String get infoCustomerBrief => 'Thông tin KH';

  @override
  String get task => 'Công việc';

  @override
  String get sendRequest => 'Gửi yêu cầu';

  @override
  String get typeEform => 'Loại yêu cầu';

  @override
  String get start => 'Bắt đầu';

  @override
  String get collaboratorNews => 'Tin tức';

  @override
  String get alwayEnableNotificationForNews =>
      'Luôn bật thông báo để thông tin tới quý khách tin tức hàng ngày.';

  @override
  String get taskInformation => 'Thông tin công việc';

  @override
  String get checkinManagement => 'Quản lý chấm công';

  @override
  String get efficientCheckinManagement =>
      'Quản lý chấm công chính xác, dễ dàng';

  @override
  String checkinMonthDetail(int? month) {
    return 'Chi tiết công tháng $month';
  }

  @override
  String get assetHistory => 'Lịch sử tài sản';

  @override
  String get transfer => 'Điều chuyển';

  @override
  String get maintenance => 'Bảo trì';

  @override
  String get jobType => 'Loại công việc';

  @override
  String get requirementEmployee => 'NV yêu cầu';

  @override
  String get maintenanceEmployee => 'NV bảo trì';

  @override
  String get completionDate => 'Ngày hoàn thành';

  @override
  String get createNewGroup => 'Tạo nhóm mới';

  @override
  String get updateSuccess => 'Bạn đã cập nhật thành công';

  @override
  String get createSuccess => 'Bạn đã tạo thành công';

  @override
  String get copyLink => 'Sao chép đường dẫn';

  @override
  String get checkinReminder => 'Nhắc nhở chấm công';

  @override
  String get removeUserConfirmation => 'Bạn có muốn xóa tài khoản này?';

  @override
  String get removeUserAccount => 'Xóa tài khoản';

  @override
  String get removeUserAccountMessage =>
      '- Xóa tên, ảnh đại diện cùng thông tin tài khoản\n- Xóa tất cả nhật ký tin nhắn và lịch sử gọi\n- Tất cả dữ liệu sẽ bị xóa vĩnh viễn không thể khôi phục';

  @override
  String get specification => 'Cấu hình';

  @override
  String get incomingCount => 'Số lần đến';

  @override
  String get averageRevenue => 'DT bình quân';

  @override
  String get totalCost => 'Tổng tiền đã chi';

  @override
  String get cardBalance => 'Số dư thẻ';

  @override
  String get paid => 'Đã thanh toán';

  @override
  String get payBack => 'Hoàn tiền';

  @override
  String get loan => 'Tổng tiền nợ';

  @override
  String get submittedCost => 'Tổng tiền nộp';

  @override
  String get payWithCard => 'Thanh toán bằng thẻ';

  @override
  String get pickFloor => 'Chọn tầng';

  @override
  String get pickProvince => 'Chọn tỉnh thành';

  @override
  String get pickBranchAndFloor => 'Chọn chi nhánh, tầng';

  @override
  String get goBack => 'Trở về';

  @override
  String get pickBranchFloorAndBed => 'Chọn tầng, phòng, giường';

  @override
  String get pickBed => 'Chọn giường';

  @override
  String get pickRoom => 'Chọn phòng';

  @override
  String get customerList => 'Danh sách khách hàng';

  @override
  String get selectCustomerBranchNote =>
      'Vui lòng chọn chi nhánh, tầng để xem danh sách khách hàng!';

  @override
  String get choose => 'Chọn';

  @override
  String get consultationFile => 'Hồ sơ tư vấn';

  @override
  String get consultationManager => 'Quản lý tư vấn';

  @override
  String get consultationAgent => 'Nhân viên tư vấn';

  @override
  String get consultationCharge => 'Tiền nhận tư vấn';

  @override
  String get doctorCharge => 'Tiền bác sĩ';

  @override
  String get consultationContent => 'Nội dung tư vấn';

  @override
  String get exploitPlan => 'Kế hoạch khai thác';

  @override
  String get botReport => 'Báo bot';

  @override
  String get chooseBed => 'Chọn giường';

  @override
  String get out => 'Ra về';

  @override
  String get treatmentFile => 'Hồ sơ điều trị';

  @override
  String get searchDepartment => 'Tìm theo tên phòng';

  @override
  String get searchService => 'Tìm theo tên dịch vụ';

  @override
  String get treatmentDetail => 'Chi tiết điều trị';

  @override
  String get treatmentService => 'Dịch vụ điều trị';

  @override
  String get useCount => 'SL sử dụng';

  @override
  String get reExaminationDate => 'Ngày tái khám';

  @override
  String get attendingDoctor => 'Bác sĩ điều trị';

  @override
  String get agentIncharge => 'NV phụ trách';

  @override
  String get treatment => 'điều trị';

  @override
  String get picture => 'Hình ảnh';

  @override
  String get medicalPrescription => 'toa thuốc';

  @override
  String get morning => 'sáng';

  @override
  String get noon => 'trưa';

  @override
  String get afternoon => 'chiều';

  @override
  String get night => 'tối';

  @override
  String get medicineDetail => 'Chi tiết thuốc';

  @override
  String get searchCustomer => 'Tìm khách hàng';

  @override
  String get listCustomer => 'Danh sách khách hàng';

  @override
  String get returnBed => 'Trả giường';

  @override
  String get changeBed => 'Đổi giường';

  @override
  String get picking => 'Bạn đang chọn';

  @override
  String get floor => 'Tầng';

  @override
  String get product => 'Sản phẩm';

  @override
  String get serviceAndProduct => 'Dịch vụ/Sản phẩm';

  @override
  String get findService => 'Tìm theo tên dịch vụ';

  @override
  String get findProduct => 'Tìm theo tên sản phẩm';

  @override
  String get buyCount => 'SL Mua';

  @override
  String get existCount => 'SL Còn';

  @override
  String get totalMoney => 'Tổng tiền';

  @override
  String get detailService => 'Chi tiết dịch vụ';

  @override
  String get handle => 'Xử lý';

  @override
  String get importantNote => 'Ghi chú quan trọng';

  @override
  String get createNew => 'Tạo mới';

  @override
  String get firstName => 'Tên';

  @override
  String get requestStoragePermission => 'Cho phép truy cập tệp tin';

  @override
  String get storageRequest1 => 'Lưu trữ tệp tải về';

  @override
  String get cameraRequest1 => 'Chụp hình cập nhật thông tin cá nhân';

  @override
  String get cameraRequest2 => 'Quét mã code dễ dàng';

  @override
  String get microPermissionRequest => 'Cho phép ghi âm thiết bị';

  @override
  String get microPermissionSettings =>
      'Xin cho phép quyền ghi âm trong cài đặt thiết bị';

  @override
  String get record => 'Ghi âm';

  @override
  String get microRequest1 => 'Gọi âm thanh';

  @override
  String get microRequest2 => 'Gửi ghi âm hỗ trợ';

  @override
  String get photoRequest1 => 'Cập nhật ảnh thông tin cá nhân';

  @override
  String get onTheNextScreen => 'ở màn hình tiếp theo để:';

  @override
  String get changeSettingsLater =>
      'Bạn có thể thay đổi quyền này sau ở trong mục Cài đặt của ứng dụng';

  @override
  String get notificationRequest1 => 'Cập nhật tin tức, dịch vụ mới nhất';

  @override
  String get employeeInfoShort => 'TT nhân viên';

  @override
  String get performEmployee => 'Tên NV thực hiện';

  @override
  String get caredQL => 'QL đã chăm sóc';

  @override
  String get pleaseSelect => 'Vui lòng chọn';

  @override
  String get dateCountReturn => 'Tái khám sau';

  @override
  String get takeEmployee => 'NV thực hiện';

  @override
  String get adviseEmployee => 'NV tư vấn';

  @override
  String get takeDoctor => 'Bác sĩ thực hiện';

  @override
  String get medicine => 'Thuốc';

  @override
  String get dosage => 'Liều';

  @override
  String get point => 'Điểm';

  @override
  String get avgPointByOthers => 'Điểm trung bình (người khác đánh giá)';

  @override
  String get khacnho => 'Khấc nhỏ';

  @override
  String get differentGap => 'Chênh lệch';

  @override
  String get reExaminateAfter => 'Tái khám sau';

  @override
  String get machine => 'Máy';

  @override
  String get spotsise => 'Spotsise';

  @override
  String get originStatus => 'TTBD';

  @override
  String get inkColor => 'Màu mực';

  @override
  String get prescriptionTemplate => 'Toa thuốc mẫu';

  @override
  String get pleaseSelectPrescriptionTemplate => 'Vui lòng chọn toa thuốc mẫu';

  @override
  String get list => 'Danh sách';

  @override
  String get diagnosis => 'Chuẩn đoán';

  @override
  String get advice => 'Lời dặn';

  @override
  String get prescriber => 'Người kê toa';

  @override
  String get usage => 'Cách dùng';

  @override
  String get unit => 'Đơn vị tính';

  @override
  String get addMedicine => 'Thêm thuốc mới';

  @override
  String get addMedicineForms => 'Thêm thuốc theo mẫu';

  @override
  String get addContent => 'Nhập nội dung...';

  @override
  String get action => 'Thao tác';

  @override
  String get netFare => 'Đơn giá';

  @override
  String get discount => 'Giảm giá';

  @override
  String get finalCost => 'Thành tiền';

  @override
  String get trackingTransparency =>
      'Xin phép được theo dõi hành vi người dùng ở màn hình kế tiếp để:';

  @override
  String get locationRequest1 => 'Cập nhật tin tức, dịch vụ chính xác hơn';

  @override
  String get checkedOut => 'Đã về';

  @override
  String get orderFood => 'Đặt cơm';

  @override
  String get morningSession => 'Sáng';

  @override
  String get noonSession => 'Trưa';

  @override
  String get afternoonSession => 'Chiều';

  @override
  String get update => 'Cập nhật';

  @override
  String get success => 'Thành công';

  @override
  String get orderFoodUpdateSuccess => 'Cập nhật đặt cơm thành công';

  @override
  String get overAmount => 'Số lượng sử dụng đã quá số lượng còn lại';

  @override
  String get monday => 'Thứ 2';

  @override
  String get tuesday => 'Thứ 3';

  @override
  String get wednesday => 'Thứ 4';

  @override
  String get thursday => 'Thứ 5';

  @override
  String get friday => 'Thứ 6';

  @override
  String get saturday => 'Thứ 7';

  @override
  String get sunDay => 'Chủ Nhật';

  @override
  String get nation => 'Quốc gia';

  @override
  String get visitReason => 'Biết Ngọc Dung từ đâu ?';

  @override
  String get introducer => 'Người giới thiệu';

  @override
  String get firstVisit => 'Ngày đầu đến';

  @override
  String get lastVisit => 'Ngày cuối đến';

  @override
  String get scheduleInfo => 'Thông tin đặt hẹn';

  @override
  String get customerCharacteristics => 'Đặc điểm của khách hàng';

  @override
  String get currentWeek => 'Tuần hiện tại';

  @override
  String get optionQuestion => 'Bạn muốn thực hiện thao tác?';

  @override
  String get assignTask => 'Gán CV';

  @override
  String get bookingSchedule => 'Lịch đặt hẹn';

  @override
  String get information => 'Thông tin';

  @override
  String get bookedService => 'DV đã đặt hẹn';

  @override
  String get noVisit => 'Số lần đến';

  @override
  String get firstVisited => 'Lần đầu đến';

  @override
  String get lastVisited => 'Đến gần đây';

  @override
  String get balanceMoney => 'Số dư thẻ';

  @override
  String get lastMoney => 'Chi gần đây';

  @override
  String get totalSpent => 'Tổng chi';

  @override
  String get avgRevenue => 'DT bình quân';

  @override
  String get takeCareEmpName => 'Nhân viên chăm sóc';

  @override
  String get used => 'Đã sử dụng';

  @override
  String get unUsed => 'Chưa sử dụng';

  @override
  String get customerHaveBook => 'Khách hàng đặt lịch hẹn';

  @override
  String get noCheckin => 'Chưa đến';

  @override
  String get hasCheckin => 'Đã đến';

  @override
  String get beingTakeCare => 'Đang chăm sóc';

  @override
  String get today => 'Hôm nay';

  @override
  String get tomorrow => 'Ngày mai';

  @override
  String get treatmentAvailable => 'Còn liệu trình';

  @override
  String get notAssignTask => 'Chưa gán CV';

  @override
  String get assignedTask => 'Đã gán CV';

  @override
  String customerAssign(String total) {
    return '$total KH được chọn';
  }

  @override
  String get deleteAssignTaskMessage =>
      'Bạn có chắc muốn hủy công việc đã gán?';

  @override
  String get sendFailure => 'Gửi thất bại';

  @override
  String get sendNoteSuccess => 'Bạn đã gửi thông tin thành công';

  @override
  String get input => 'Nhập';

  @override
  String get bookingService => 'Dịch vụ đặt hẹn';

  @override
  String get bookingNote => 'Ghi chú đặt hẹn';

  @override
  String get pasteToConsultation => 'Dán vào nội dung tư vấn';

  @override
  String get printTicket => 'In phiếu';

  @override
  String get noData => 'Không có dữ liệu';

  @override
  String get pullToLoadMore => 'Kéo để tải thêm';

  @override
  String get checkinList => 'Danh sách check-in';

  @override
  String get notAssigned => 'Chưa gán';

  @override
  String get assigned => 'Đã gán';

  @override
  String get perform => 'Thực hiện';

  @override
  String get taskList => 'Danh sách công việc';

  @override
  String get collectInformation => 'Thu thập thông tin';

  @override
  String boughtServices(int count) {
    return '$count dịch vụ đã mua';
  }

  @override
  String get assignWork => 'Gán công việc';

  @override
  String selecting(int count) {
    return '$count đang chọn';
  }

  @override
  String onlyAssignMax(int count) {
    return 'Chỉ được gán tối đa $count nhân viên';
  }

  @override
  String get select => 'Chọn';

  @override
  String get confirmFinishTask => 'Bạn chắc chắn đã hoàn tất công việc?';

  @override
  String get tutorial => 'Hướng dẫn';

  @override
  String get takePictureBefore => 'Chụp hình trước làm';

  @override
  String get takePictureAfter => 'Chụp hình sau làm';

  @override
  String get warningFinish =>
      'Vui lòng thực hiện đầy đủ các công việc để hoàn tất';

  @override
  String get warningFinish2 =>
      'Vui lòng thực hiện ít nhất một công việc để hoàn tất';

  @override
  String get beforeDone => 'Trước làm';

  @override
  String get afterDone => 'Sau làm';

  @override
  String get attachImages => 'Đính kèm hình ảnh';

  @override
  String get customerCompleteServiceConfirm =>
      'Bạn có chắc chắn khách hàng đã hoàn tất dịch vụ?';

  @override
  String get searchEmployee => 'Tìm theo tên, mã nhân viên ...';

  @override
  String get requiredSelectRoom => 'Vui lòng chọn phòng';

  @override
  String get getLocationSuccessful => 'Lấy thông tin vị trí hoàn tất';

  @override
  String get microRequest3 => 'Thu thập cải thiện chất lượng dịch vụ';

  @override
  String get province => 'Tỉnh/Thành phố';

  @override
  String get district => 'Quận/Huyện';

  @override
  String get ward => 'Phường/Xã';

  @override
  String get male => 'Nam';

  @override
  String get female => 'Nữ';

  @override
  String get selectGender => 'Chọn giới tính';

  @override
  String get selectJob => 'Chọn nghề nghiệp';

  @override
  String get branch => 'Chi nhánh';

  @override
  String get addCustomer => 'Thêm khách hàng';

  @override
  String get addCustomerSuccess => 'Thêm khách hàng thành công';

  @override
  String get addNew => 'Thêm mới';

  @override
  String get endRecord => 'Kết thúc ghi âm';

  @override
  String get recordWarning =>
      'Màn hình tiếp theo sẽ thực hiện ghi âm vui lòng xác nhận để tiếp tục';

  @override
  String get recordPerform => 'Thực hiện ghi âm';

  @override
  String get delete => 'Xóa';

  @override
  String get requiredField => 'Trường bắt buộc';

  @override
  String get all => 'Tất cả';

  @override
  String get nextService => 'Tiếp tục dịch vụ khác';

  @override
  String get changeRoom => 'Chuyển phòng';

  @override
  String get ttbd => 'TTBD';

  @override
  String get selectDate => 'Chọn ngày';

  @override
  String get selectContent => 'Chọn nội dung';

  @override
  String get searchCustomerPhone => 'Tìm theo số điện thoại KH';

  @override
  String get manualCheckin => 'Checkin thủ công';

  @override
  String get editCustomerInfo => 'Sửa thông tin khách hàng';

  @override
  String room(String roomCode) {
    return 'Phòng $roomCode';
  }

  @override
  String get pickStaff => 'Chọn nhân viên';

  @override
  String get bed => 'Giường';

  @override
  String get consultation => 'Tư vấn';

  @override
  String get updateConsultation => 'Cập nhật tư vấn';

  @override
  String get technical => 'Kỹ thuật';

  @override
  String get consultationCost => 'Tiền nhận tư vấn';

  @override
  String get notificationBot => 'Thông báo bot';

  @override
  String get affective => 'Hiệu quả';

  @override
  String get nonAffective => 'Không hiệu quả';

  @override
  String get adviseContinuous => 'Tư vấn tiếp';

  @override
  String get minNetFareError => 'Không được phép thấp hơn giá ban đầu';

  @override
  String get supportConsultation => 'Hỗ trợ tư vấn';

  @override
  String get selfConsultation => 'Tự tư vấn';

  @override
  String get selectConsultation => 'Chọn hình thức tư vấn';

  @override
  String get notConsultation => 'Chưa tư vấn';

  @override
  String get didConsultation => 'Đã tư vấn';

  @override
  String get recept => 'Tiếp nhận';

  @override
  String get evaluationPeriodList => 'Danh sách kỳ đánh giá';

  @override
  String get noEvaluationPeriodAvailable => 'Chưa tới kỳ đánh giá';

  @override
  String get evaluationResult => 'Kết quả đánh giá';

  @override
  String get evaluation => 'Đánh giá';

  @override
  String get completeRating => 'Hoàn thành đánh giá?';

  @override
  String get questionList => 'Danh sách câu hỏi';

  @override
  String get previous => 'Quay lại';

  @override
  String get questionNumber => 'Câu hỏi';

  @override
  String get consultationContentShort => 'Nội dung TV';

  @override
  String get callLogs => 'LS cuộc gọi';

  @override
  String get call => 'Gọi điện';

  @override
  String get messageLogs => 'LS tin nhắn';

  @override
  String get bookingLogs => 'LS đặt hẹn';

  @override
  String get comeDateTime => 'Ngày giờ đến';

  @override
  String get bookSource => 'Đặt từ';

  @override
  String get copied => 'Sao chép';

  @override
  String get advisoryTicketStatus => 'Trạng thái phiếu ghi';

  @override
  String get skillsNeedToImprove => 'Các năng lực cần cải thiện';

  @override
  String get emptyEvaluationResult => 'Chưa có kết quả đánh giá';

  @override
  String get ratingSuccess => 'Gửi đánh giá thành công';

  @override
  String get ratingFailure => 'Không lưu được dữ liệu';

  @override
  String get tabNew => 'Mới';

  @override
  String get tabPending => 'Pending';

  @override
  String get tabSuccess => 'Đã xử lý';

  @override
  String get searchHint => 'Tìm theo tên,sđt, ngày gọi';

  @override
  String get ticketFilter => 'Lọc phiếu ghi';

  @override
  String get statusTicket => 'Trạng thái phiếu ghi';

  @override
  String get dateFollow => 'Ngày tương tác';

  @override
  String get dateCreate => 'Ngày tạo';

  @override
  String get titleTimeInit => '- Chọn ngày -';

  @override
  String get titleStatusInit => '- Chọn trạng thái -';

  @override
  String get isEnd => '- Đã hết -';

  @override
  String get accessReject => 'Từ chối truy cập';

  @override
  String get booking => 'Đặt hẹn';

  @override
  String get fromPhone => 'Số gọi ra';

  @override
  String get customerPhone => 'Số khách hàng';

  @override
  String get call2 => 'Gọi';

  @override
  String get connecting => 'Đang kết nối...';

  @override
  String get end => 'Kết thúc';

  @override
  String get userTicket => 'Danh sách phiếu ghi';

  @override
  String get comeDate => 'Ngày đến';

  @override
  String get comeTime => 'Thời gian đến';

  @override
  String get totalBooking => 'Tổng lịch đã đặt';

  @override
  String get detailService2 => 'DV chi tiết';

  @override
  String get sendMethod => 'Hình thức gửi';

  @override
  String get sendRecordQuestion => 'Bạn có muốn gửi ghi âm ?';

  @override
  String get pleaseSelectDate => 'Vui lòng chọn ngày đặt lịch';

  @override
  String get planSchedule => 'Lịch kế hoạch';

  @override
  String get sendSMS => 'Gửi SMS';

  @override
  String get sendZNS => 'Gửi ZNS';

  @override
  String get sendAppNoti => 'Gửi Noti app';

  @override
  String get callTo => 'Gọi đến';

  @override
  String get cancelAll => 'Hủy tất cả';

  @override
  String get createBookingWarning =>
      'Bạn có một lịch kế hoạch chưa được chọn. Bỏ qua?';

  @override
  String get bookingSuccess => 'Đặt lịch thành công';

  @override
  String get serviceHasExist => 'Dịch vụ đã tồn tại';

  @override
  String get consultationCreate => 'Tạo tư vấn';

  @override
  String get updateBookingSuccess => 'Đã cập nhật lịch hẹn thành công';

  @override
  String get updateNDTVSuccess => 'Cập nhật NDTV thành công';

  @override
  String get selectProduct => 'Chọn sản phẩm';

  @override
  String get selectMethod => 'Chọn phương thức';

  @override
  String get selectUnit => 'Chọn đơn vị tính';

  @override
  String get notificationType => 'Loại tin thông báo';

  @override
  String get buocSong => 'Bước sóng';

  @override
  String get cacBuoc => 'Các bước';

  @override
  String get dauMay => 'Đầu máy';

  @override
  String get postsize => 'Spotsize';

  @override
  String get shot => 'Shot';

  @override
  String get vung => 'Vùng';

  @override
  String get tanso => 'Tần số';

  @override
  String get nangluong => 'Năng lượng';

  @override
  String get energyRate => 'Mức năng lượng';

  @override
  String get specs => 'Thông số';

  @override
  String get drinkProduct => 'Sản phẩm uống';

  @override
  String get latherProduct => 'Sản phẩm bôi';

  @override
  String get drinkAndLatherProductOfFirm => 'Sản phẩm bôi và uống của TMV';

  @override
  String get drinkAndLatherProductOfClient => 'Sản phẩm bôi và uống của KH';

  @override
  String get sang => 'Sáng';

  @override
  String get trua => 'Trưa';

  @override
  String get chieu => 'Chiều';

  @override
  String get search2 => 'Tìm theo từ khoá';

  @override
  String get intro => 'Hướng dẫn sử dụng';

  @override
  String get selectLather => 'Chọn sản phẩm bôi';

  @override
  String get selectDrink => 'Chọn sản phẩm uống';

  @override
  String get hintNotes => 'Viết ghi chú...';

  @override
  String get hintTotal => 'Nhập số lượng...';

  @override
  String get titleTotal => 'Số lượng';

  @override
  String get selectWorking => 'Chọn công việc';

  @override
  String get nvtv => 'NVTV';

  @override
  String get nvth => 'NVTH';

  @override
  String get bsth => 'BSTH';

  @override
  String get sl => 'SL';

  @override
  String get times => 'Lần';

  @override
  String get devFeatures => 'Tính năng đang phát triển';

  @override
  String get reasonVisit => 'Biết Ngọc Dung từ đâu ?';

  @override
  String get titleDay => 'Ngày';

  @override
  String get setCheckinTime => 'Cài đặt giờ chấm công vào';

  @override
  String get setCheckoutTime => 'Cài đặt giờ chấm công ra';

  @override
  String get setupCheckin => 'Cài đặt chấm công';

  @override
  String get exampleNote => 'ví dụ: Ăn no trước khi uống';

  @override
  String get technicalQL => 'QL tay nghề';

  @override
  String get recordCheckingStaff => 'NV kiểm tra hồ sơ';

  @override
  String get stopRecordConfirm => 'Bạn muốn dừng ghi âm?';

  @override
  String get stopRecordSuccess => 'Chúc mừng bạn đã hoàn tất ghi âm';

  @override
  String printingProgress(String value) {
    return 'Đang xử lý: $value%';
  }

  @override
  String get pleaseInputDescription => 'Vui lòng nhập miêu tả';

  @override
  String get searchByCodeOrName => 'Tìm theo mã KH hoặc tên KH';

  @override
  String get diary => 'MXH';

  @override
  String get explore => 'Khám phá';

  @override
  String get internal => 'Nội bộ';

  @override
  String get recentlyChat => 'vừa mới chat';

  @override
  String get message => 'Tin nhắn';

  @override
  String get groupName => 'Tên nhóm';

  @override
  String get createGroup => 'Tạo nhóm';

  @override
  String get customer => 'Khách hàng';

  @override
  String get timeAttendanceNow => 'Chấm công ngay';

  @override
  String get cameraAgain => 'Chụp lại ảnh';

  @override
  String get imageAttendance => 'Ảnh chấm công';

  @override
  String get inputReason => 'Nhập lý do';

  @override
  String get typeAction => 'Chọn loại thao tác';

  @override
  String get dayApply => 'Ngày áp dụng';

  @override
  String get fromDayToDay => 'Từ ngày đến ngày';

  @override
  String get personInfo => 'Thông tin tài khoản';

  @override
  String get bio => 'Bio';

  @override
  String get hintBio => 'Mô tả bản thân';

  @override
  String get nickName => 'NickName';

  @override
  String get hintNickName => 'Cập nhật nickname...';

  @override
  String get gallery => 'Hình ảnh/Video';

  @override
  String get limitImage => 'Chọn thêm ảnh cho phép';

  @override
  String get changePermission => 'cài đặt lại quyền';

  @override
  String get limitImageIOS => 'Chọn lại ảnh cho phép';

  @override
  String get previewImage => 'Cập nhật ảnh đại diện';

  @override
  String andOthers(int count) {
    return 'và $count người khác';
  }

  @override
  String get groupDetail => 'Chi tiết nhóm';

  @override
  String get createGroupSuccess => 'Tạo nhóm thành công';

  @override
  String get msnv => 'MSNV';

  @override
  String get requiredGroupName => 'Vui lòng nhập tên nhóm';

  @override
  String get minuteAgo => 'phút trước';

  @override
  String get hoursAgo => 'giờ trước';

  @override
  String get now => 'Ngay bây giờ';

  @override
  String get like => 'Thích';

  @override
  String get reply => 'Phản hồi';

  @override
  String get boss => 'Sếp';

  @override
  String get eventsNews => 'Event';

  @override
  String get viewLike => 'Xem lượt thích';

  @override
  String get titleLike => 'Lượt thích';

  @override
  String get statusHint => 'Hôm nay bạn thế nào?';

  @override
  String get story => 'Nhật ký';

  @override
  String get messenger => 'Nhắn tin';

  @override
  String get deleteGroup => 'Xóa nhóm';

  @override
  String get turnOffNoti => 'Tắt thông báo';

  @override
  String get document => 'Tài liệu';

  @override
  String get link => 'Liên kết';

  @override
  String get downloadComplete => 'Tải xuống thành công';

  @override
  String get video => 'Video';

  @override
  String get pathNotFound => 'Không tìm thấy đường dẫn thư mục';

  @override
  String get permissionDenied => 'Không có quyền truy cập';

  @override
  String get media => 'Ảnh/video';

  @override
  String get dayNow => 'Hôm nay';

  @override
  String get addMember => 'Thêm thành viên';

  @override
  String get leaveGroup => 'Rời nhóm';

  @override
  String get warningRemoveMember => 'Bạn có chắc muốn xóa thành viên này?';

  @override
  String get warningLeaveGroup => 'Bạn có chắc muốn rời nhóm?';

  @override
  String get ruleStory => 'Đối tượng của bài viết';

  @override
  String get postStory => 'Đăng';

  @override
  String get titleEditOptionStory => 'Chỉnh sửa bài viết';

  @override
  String get titleDeleteOptionStory => 'Xóa bài viết';

  @override
  String get titleEditOptionComment => 'Chỉnh sửa bình luận';

  @override
  String get titleDeleteOptionComment => 'Xóa bình luận';

  @override
  String get hintContentStory => 'Bạn đang nghĩ gì?';

  @override
  String get recently => 'Gần đây';

  @override
  String get chatConservation => 'Hộp thoại';

  @override
  String get nearAddress => 'Địa chỉ gần đây';

  @override
  String get currentAddress => 'Địa chỉ của bạn';

  @override
  String get shareStaff => 'Chia sẻ người khác';

  @override
  String get shareAddress => 'Chia sẻ địa chỉ';

  @override
  String get hintFindStaff => 'Tìm tên người';

  @override
  String get hintFindAddress => 'Tìm địa chỉ cần share';

  @override
  String get isTags => 'cùng với';

  @override
  String get isLocation => 'tại';

  @override
  String get isLocation2 => 'đang ở';

  @override
  String get notCheckin => 'Không chấm công';

  @override
  String get getLocationCurrent => 'Lấy địa chỉ hiện tại';

  @override
  String get checkin2 => 'Vào';

  @override
  String get checkout2 => 'Ra';

  @override
  String get selectTag => 'Người đã chọn';

  @override
  String get selectAddress => 'Địa chỉ đã chọn';

  @override
  String get unSelect => 'Bỏ chọn';

  @override
  String get groupOrPerson => 'Nhóm hoặc cá nhân';

  @override
  String replyTo(String name) {
    return 'Trả lời $name';
  }

  @override
  String get groupCreatedRecently => 'Nhóm vừa được tạo';

  @override
  String userSentObject(String name, String object) {
    return '$name đã gửi một $object';
  }

  @override
  String get file => 'Tập tin';

  @override
  String get openTime => 'Thời gian làm việc: ';

  @override
  String get creatingStory => 'Đang đăng bài ';

  @override
  String get edited => 'Đã sửa';

  @override
  String get storyPreviewImage => 'Xem lại ảnh';

  @override
  String get watchImage => 'Xem ảnh';

  @override
  String get updateAvatar => 'Cập nhật ảnh đại diện';

  @override
  String get pinConversation => 'Ghim hộp thoại';

  @override
  String get unpinConversation => 'Bỏ ghim hộp thoại';

  @override
  String get departmentRoom => 'Phòng ban';

  @override
  String get inDay => 'Buổi';

  @override
  String get validateBranchDepartment =>
      'Vui lòng bổ sung chi nhánh và bộ phận';

  @override
  String get validateNote => 'Bổ sung địa điểm công tác vào ô Ghi chú';

  @override
  String get validateInday => 'Vui lòng chọn buổi';

  @override
  String get titleEmoji => 'Bạn cảm thấy thế nào?';

  @override
  String get feeling => 'Đang cảm thấy';

  @override
  String get forward => 'Chuyển tiếp';

  @override
  String forwardTo(String name) {
    return 'Chuyển tiếp $name';
  }

  @override
  String forwardFrom(String name) {
    return 'Chuyển tiếp từ $name';
  }

  @override
  String get forwardMessageSuccess => 'Chuyển tiếp tin nhắn thành công';

  @override
  String get storyDetail => 'Bài viết';

  @override
  String get updating => 'Đang cập nhật...';

  @override
  String get warningDeleteGroup => 'Bạn có chắc muốn xóa nhóm?';

  @override
  String get bugReport => 'Báo lỗi';

  @override
  String get doYouDeleteStory => 'Xoá bài viết?';

  @override
  String get doYouDeleteComment => 'Xoá bình luận?';

  @override
  String get notFoundTag => 'Không tìm thấy nhân viên';

  @override
  String get turnOnNoti => 'Bật thông báo';

  @override
  String downloading(String progress) {
    return 'Đang tải xuống $progress';
  }

  @override
  String get updateBackground => 'Cập nhật ảnh nền';

  @override
  String get reaction => 'Cảm xúc';

  @override
  String get playSpeed => 'Tốc độ phát';

  @override
  String get playSpeedNomal => 'Bình thường';

  @override
  String get uploadFileFailure => 'Upload thất bại';

  @override
  String get joinNow => 'Tham gia ngay';

  @override
  String get joinGroup => 'Tham gia nhóm';

  @override
  String get inviteLink => 'Liên kết mời';

  @override
  String get copiedInviteLink => 'Đã sao chép link tham gia nhóm';

  @override
  String get titleNewPost => 'Đăng bài mới';

  @override
  String get updateCheckinFailure => 'Cập nhật công thất bại';

  @override
  String get callByApp => 'Gọi qua ứng dụng';

  @override
  String get vpnDetectionWarning =>
      'Bạn đang bật VPN vui lòng tắt để tiếp tục sử dụng ứng dụng';

  @override
  String get doYouDeleteNoti => 'Xoá thông báo này?';

  @override
  String get recordToSendMessage =>
      'Ứng dụng sử dụng quyền truy cập để ghi âm tin nhắn thoại';

  @override
  String get changePassword => 'Đổi mật khẩu';

  @override
  String get currentPassword => 'Mật khẩu hiện tại';

  @override
  String get newPassword => 'Mật khẩu mới';

  @override
  String get changePasswordSuccess => 'Cập nhật mật khẩu thành công';

  @override
  String get warningUploadFile =>
      'Đang đăng tin, bạn có muốn huỷ để sang tab khác?';

  @override
  String get confirmRemoveUploadFile => 'Huỷ ngay';

  @override
  String get keepPage => 'Ở lại trang';

  @override
  String get createFolder => 'Tạo thư mục';

  @override
  String get recommendFolder => 'Đề xuất thư mục';

  @override
  String get unread => 'Chưa đọc';

  @override
  String get addUnreadFolder => 'Nhấn ‘thêm’ để tạo thư mục Chưa đọc';

  @override
  String get addPersonalFolder => 'Nhấn ‘thêm’ để tạo thư mục Cá nhân';

  @override
  String get personal => 'Cá nhân';

  @override
  String get folder => 'Thư mục';

  @override
  String get createAFolder => 'Tạo 1 thư mục';

  @override
  String get allConversations => 'Tất cả các cuộc trò chuyện';

  @override
  String get editFolderHint => 'Nhấn ‘sửa’ để thay đổi hoặc xóa thư mục';

  @override
  String get createFolderIntro =>
      'Tạo thư mục cho các nhóm trò chuyện khác nhau và nhanh chóng chuyển đổi.';

  @override
  String get newFolder => 'Thư mục mới';

  @override
  String get folderName => 'Tên thư mục';

  @override
  String get addConversation => 'Thêm hộp thoại';

  @override
  String get selectedConversation => 'Hộp thoại đã chọn';

  @override
  String get addConversationHint =>
      'Cuộc hội thoại sẽ xuất hiện trong thư mục này.';

  @override
  String get warningRemoveFolder => 'Bạn có chắc muốn xóa thư mục này?';

  @override
  String get editFolder => 'Sửa thư mục';

  @override
  String get inefficient => 'Không hiệu quả';

  @override
  String get titleTreatMent => 'Liệu trình';

  @override
  String get titleCommit => 'Cam kết';

  @override
  String get titleNVCS => 'NVCS';

  @override
  String get titleEndTreatMent => 'Kết thúc liệu trình';

  @override
  String get titleCommitInfo => 'Thông tin lúc cam kết';

  @override
  String get titleProductDrinkLather => 'Sản phẩm (bôi,uống ghi rõ công thức)';

  @override
  String get titleTreatMentTime =>
      'Thời gian bao lâu đi 1 lần, 1 lần bao nhiêu phút';

  @override
  String get titleTotalMoney => 'Tổng số tiền làm ốm';

  @override
  String get titleTreatmentZone => 'Số xuất, công nghệ, vùng điều trị';

  @override
  String get titleHigh => 'Chiều cao';

  @override
  String get titleWeight => 'Cân nặng';

  @override
  String get titleMeasurements => 'Số đo';

  @override
  String get pinnedMessage => 'Tin được ghim';

  @override
  String get pinMessage => 'Ghim tin nhắn';

  @override
  String get unpinMessage => 'Bỏ ghim tin nhắn';

  @override
  String get warningHotfixUpdate =>
      'Đã có bản cập nhật mới, bạn có muốn khởi động lại ứng dụng để hoàn tất cập nhật ?';

  @override
  String get checkUpdate => 'Kiểm tra cập nhật';

  @override
  String get noAvailableUpdate => 'Không có bản cập nhật khả dụng';

  @override
  String get titleAddOption => 'Thêm tuỳ chọn';

  @override
  String get option => 'Tuỳ chọn';

  @override
  String get inputTitle => 'Nhập tiêu đề';

  @override
  String get maxOption => 'Bạn có thể tạo tối đa 8 tuỳ chọn';

  @override
  String get unVote => 'Rút lại';

  @override
  String get vote => 'Bình chọn';

  @override
  String get timeoutMessage => 'Hết thời gian chờ xử lý, vui lòng thử lại';

  @override
  String get beforeDay => 'Ngày trước';

  @override
  String get readAllNoti => 'Đọc tất cả';

  @override
  String get titleAllNoti => 'Tất cả thông báo';

  @override
  String get unpinAllMessageWarning =>
      'Bạn có chắc muốn bỏ ghim tất cả tin nhắn?';

  @override
  String get unpinAllMessage => 'Bỏ ghim tất cả tin nhắn';

  @override
  String get createPoll => 'Tạo bình chọn';

  @override
  String get voting => 'Bình chọn';

  @override
  String get optional => 'Lựa chọn';

  @override
  String get createNewOptional => 'Tạo lựa chọn mới';

  @override
  String get canCreate8Optional => 'Bạn có thể tạo tối đa 8 lựa chọn';

  @override
  String get anonymousVoting => 'Ẩn người lựa chọn';

  @override
  String get multipleAnswers => 'Chọn nhiều lựa chọn';

  @override
  String get addOptional => 'Thêm lựa chọn';

  @override
  String get voted => 'Đã bình chọn';

  @override
  String get anonymous => 'Ẩn danh';

  @override
  String get viewResults => 'Xem kết quả';

  @override
  String get retractVote => 'Bỏ bình chọn';

  @override
  String get stopVote => 'Kết thúc bình chọn';

  @override
  String get addImage => 'Thêm ảnh';

  @override
  String addSomeImage(int total) {
    return '(Đã thêm $total ảnh)';
  }

  @override
  String get addImageFromSystem => 'Chọn ảnh từ phần mềm';

  @override
  String get effective => 'Hiệu quả';

  @override
  String get notEffective => 'Không hiệu quả';

  @override
  String get workPausedTitle => 'Quy trình đã tạm dừng';

  @override
  String get workPausedContent =>
      '\nBạn không thể tiếp tục thao tác.\nVui lòng thực hiện lại khi có thông báo.';

  @override
  String get paused => 'Tạm dừng';

  @override
  String get noFaceDetected =>
      'Không tìm thấy khuôn mặt trong ảnh, bạn có muốn tiếp tục ?';

  @override
  String get multipleFaceDetected =>
      'Có nhiều khuôn mặt trong ảnh, vui lòng thử lại';

  @override
  String get lookStraight => 'Vui lòng nhìn thẳng';

  @override
  String get lookStraightAndBlink => 'Vui lòng nhìn thẳng và chớp mắt';

  @override
  String get lookStraightAndSmile => 'Vui lòng nhìn thẳng và cười';

  @override
  String get turnLeft => 'Vui lòng xoay trái';

  @override
  String get turnRight => 'Vui lòng xoay phải';

  @override
  String get faceVerify => 'Xác thực khuôn mặt';

  @override
  String get titlePoll => 'Câu hỏi';

  @override
  String get inputTitlePoll => 'Nhập câu hỏi';

  @override
  String get titleOption => 'Lựa chọn';

  @override
  String get inputTitleOption => 'Nhập lựa chọn';

  @override
  String get inputCreateOption => 'Tạo lựa chọn mới';

  @override
  String get max8Option => 'Bạn có thể tạo tối đa 8 lựa chọn';

  @override
  String get hiddenVoted => 'Ẩn người lựa chọn';

  @override
  String get multipleVoted => 'Chọn nhiều lựa chọn';

  @override
  String get titleModePoll => 'Bình chọn ẩn danh';

  @override
  String get emptyVote => 'Không có bình chọn';

  @override
  String get createVote => 'Tạo bình chọn';

  @override
  String get editPoll => 'Sửa bình chọn';

  @override
  String get only8Option => 'Chỉ tối đa 8 tuỳ chọn';

  @override
  String get refunOption => 'Hoàn lại';

  @override
  String get remindKYC =>
      'Bạn chưa thực hiện xác thực khuôn mặt, vui lòng thực hiện để hỗ trợ việc chấm công chính xác hơn';

  @override
  String get alreadyKYCMessage =>
      'Bạn đã xác thực khuôn mặt, vui lòng liên hệ nhân sự nếu cần xác thực lại';

  @override
  String get ticket => 'Ticket';

  @override
  String get createdBy => 'tạo bởi';

  @override
  String get by => 'bởi';

  @override
  String get removeFilter => 'Xoá lọc';

  @override
  String get groupRecieve => 'Nhóm nhận';

  @override
  String get qrCode => 'Mã QR';

  @override
  String get hintTextMedia => 'Thêm hình ảnh/video/file đính kèm';

  @override
  String get hintTextOnlyMedia => 'Thêm hình ảnh/video';

  @override
  String get hintTextTypeSelect => '-- Chọn loại --';

  @override
  String get hintTextGroupRecieveSelect => '-- Chọn nhóm nhận --';

  @override
  String get contentTicket => 'Nội dung ticket';

  @override
  String get messageOrderFoodSuccess => 'Chúc mừng bạn đã đặt cơm thành công';

  @override
  String get messageCreatedTicketSuccess =>
      'Chúc mừng bạn đã tạo thành công ticket';

  @override
  String get messageCancelOrderFood => 'Bạn có chắc chắn muốn huỷ đặt cơm?';

  @override
  String get barCode => 'Mã code';

  @override
  String get noteQR =>
      'Vui lòng đưa mã QR này cho nhà bếp để xác nhận phần cơm';

  @override
  String get complaint => 'Góp ý';

  @override
  String get regulations => 'Quy định';

  @override
  String get messageRatingFood => 'Bạn thấy món ăn hôm nay như thế nào?';

  @override
  String get messageOrderFoodComplaint => 'Lời nhắn';

  @override
  String get expiredOrderFood => 'Thời gian chốt cơm còn';

  @override
  String get ticketType => 'Loại ticket';

  @override
  String get statusType => 'Loại trạng thái';

  @override
  String get cameraV2 => 'Camera';

  @override
  String get thuoc => 'thuộc';

  @override
  String get reply2 => 'Trả lời';

  @override
  String get allActive => 'Tất cả hoạt động';

  @override
  String get titleCollection => 'Chọn mục tải lên';

  @override
  String get statusTypeSelected => 'Lọc kèm với trạng thái';

  @override
  String get ticketTypeSelected => 'Lọc kèm với ticket';

  @override
  String get reasonCancel => 'Lý do huỷ';

  @override
  String get unRecept => 'Bỏ tiếp nhận';

  @override
  String get receptSuccess => 'Tiếp nhận thành công';

  @override
  String get completeTicketSuccess => 'Bạn đã hoàn tất xong ticket';

  @override
  String get deleteTicketSuccess => 'Bạn vừa huỷ 1 ticket';

  @override
  String get deleteReceptSuccess => 'Bạn vừa huỷ bỏ tiếp nhận ticket';

  @override
  String get creatingTicket => 'Đang tạo ticket';

  @override
  String get createNewTicket => 'Tạo mới ticket';

  @override
  String get messageReportSuccess => 'Đã góp ý thành công';

  @override
  String get orderedFood => 'Đã chốt cơm';

  @override
  String get cancelRice => 'Bạn đã huỷ đặt';

  @override
  String get expiredQR => 'Mã QR đã hết hạn';

  @override
  String get imageEdit => 'Bạn có muốn thoát?';

  @override
  String get createStickers => 'Tạo sticker';

  @override
  String get expireFood => 'Thời gian chốt cơm còn';

  @override
  String get inputCodeManual => 'Nhập mã thủ công';

  @override
  String scanCodeSuccess(Object code) {
    return 'Quét mã $code thành công';
  }

  @override
  String get defaultAddress => 'Địa chỉ mặc định';

  @override
  String get setDefaultAddress => 'Đặt làm địa chỉ mặc định';

  @override
  String get addAddress => 'Thêm địa chỉ';

  @override
  String get remindBookingMealMessage => 'Bạn có muốn đặt cơm cho ngày mai?';

  @override
  String get pleaseSelectMealAddress => 'Vui lòng chọn địa chỉ đặt cơm';

  @override
  String get getOTPOnAcc => 'Vui lòng truy cập ACC để lấy OTP!';

  @override
  String get anonymousMessage => 'Ẩn danh (Góp ý của bạn sẽ được gửi ẩn danh)';

  @override
  String get detailProfile => 'Hồ sơ chi tiết';

  @override
  String get titleTreatmentDate => 'Ngày tham gia';

  @override
  String get titleAssureWeight => 'Số kg';

  @override
  String get titleAssureSize => 'Số đo';

  @override
  String get titleLoseWeight => 'Số kg đã xuống';

  @override
  String get titleTTKHAge => 'Tuổi ( ngày, tháng, năm sinh )';

  @override
  String get titleTTKHOccupation => 'Nghề nghiệp';

  @override
  String get titleTTKHMeasure => 'Số đo 3 vòng';

  @override
  String get titleTTKHOverWeight => 'Dư cân';

  @override
  String get titleTypeOfBone => 'Xuơng to hay nhỏ';

  @override
  String get titleTypeOfBirth => 'Đã sinh con chưa? Sinh thường hay mổ?';

  @override
  String get titleIsTreatment => 'Có đang điều trị bệnh không?';

  @override
  String get titleTTKHMeal =>
      'Chế độ ăn hàng ngày ( 1 ngày mấy bữa, Thường ăn gì? )';

  @override
  String get titleTTKHDoExercise =>
      'Tập luyện ( đang tập gì?, Bao nhiêu phút 1 ngày? )';

  @override
  String get titleTTKHHouseWork =>
      'Bạn có làm việc nhà hay không? Cụ thể công việc ? Mất bao nhiêu phút?';

  @override
  String get titleTTKHHistoryFit =>
      'Có từng uống thuốc ốm không? Hay điều trị giảm béo ở đâu ? Ghi rõ cụ thể';

  @override
  String get titleTTKH => 'TT khách hàng';

  @override
  String get titleGNKQ => 'Ghi nhận kết quả';

  @override
  String get titleGNKQSTT => 'STT';

  @override
  String get titleGNKQDate => 'Ngày';

  @override
  String get titleGNKQArea => 'Vùng điều trị, số đo';

  @override
  String get titleGNKQMhs => 'Toa trà';

  @override
  String get titleGNKQMhsEmpName => 'Người ra toa';

  @override
  String get titleGNKQDetailTreatment => 'Chi tiết điều trị tại nhà';

  @override
  String get titleGNKQLoseWeight => 'Tổng giảm';

  @override
  String get titleGNKQWeight => 'Cân';

  @override
  String get titleGNKQResult => 'Hiệu quả';

  @override
  String get titleGNKQTT => 'TT';

  @override
  String get titleGNKQStatus => 'Tình Trạng';

  @override
  String get titleTTKHTitleSeconds => '( nhập số )';

  @override
  String get isValiGNKQ => 'Chọn dịch vụ';

  @override
  String get titleNDTV => 'ND TV';

  @override
  String get doYouDeleteKQGH => 'Bạn chắc chắn muốn xoá ghi nhận kết quả này ?';

  @override
  String get titleGNKQDelete => 'Xoá ghi nhận';

  @override
  String get titleTabTTKH => 'TTKH';

  @override
  String get serviceEmpty => 'Chưa chọn dịch vụ';

  @override
  String get treatmentHome => 'Chi tiết điều trị tại nhà';

  @override
  String get classify => 'Phân loại';

  @override
  String get requiredSelectClassify => 'Vui lòng chọn phân loại';

  @override
  String get myTicket => 'Của tôi';

  @override
  String get processingTicket => 'Danh sách xử lý';

  @override
  String get consumerId => 'Mã KH';

  @override
  String get consumerId2 => 'Mã khách hàng';

  @override
  String get hintEmployeeId => '-- Chọn khách hàng --';

  @override
  String get hintService => '-- Chọn dịch vụ --';

  @override
  String get hintStatus => '-- Chọn trạng thái --';

  @override
  String get updateTicket => 'Cập nhật ticket';

  @override
  String get hintInputGroupTicket => 'Tìm kiếm theo nhóm nhận';

  @override
  String get hintInputTypeTicket => 'Tìm kiếm theo tên loại';

  @override
  String get hintInputCustomerTicket => 'Tìm kiếm theo tên, mã KH';

  @override
  String get hintInputServiceTicket => 'Tìm kiếm theo tên dịch vụ';

  @override
  String get confirmComplete => 'Xác nhận hoàn tất';

  @override
  String get followIssue => 'Theo dõi sự cố';

  @override
  String get other => 'Khác';

  @override
  String get giveBackTicket => 'Bạn vừa bỏ tiếp nhận 1 ticket';

  @override
  String get messageUpdatedTicketSuccess => 'Cập nhật ticket thành công';

  @override
  String get processTicketSuccess => 'Xử lý ticket thành công';

  @override
  String get titleProcess => 'Xử lý';

  @override
  String get confirmProcess => 'Xác nhận xử lý';

  @override
  String get unitRequired => 'Vui lòng nhập đơn vị tính';

  @override
  String get requiredRestart =>
      'Ứng dụng cần khởi động lại để hoàn tất thiết lập!';

  @override
  String get cc => 'CC';

  @override
  String get ccAction => '-- Chọn cc --';

  @override
  String get requestEmp => 'Người yêu cầu';

  @override
  String get requestEmpAction => '-- Chọn người yêu cầu --';

  @override
  String get receptEmp => 'Người tiếp nhận';

  @override
  String get receptEmpAction => '-- Chọn người tiếp nhận --';

  @override
  String get deadline => 'Deadline';

  @override
  String get rework => 'Chuyển việc';

  @override
  String get assignee => 'Chuyển việc';

  @override
  String get assigneeAction => 'Chuyển việc';

  @override
  String get actionCancel => 'vuốt để huỷ';

  @override
  String get assignedSuccess => 'Chuyển việc thành công';

  @override
  String get sendFrom => 'Gửi từ';

  @override
  String get sendTo => 'đến';

  @override
  String get ccDisplay => 'CC';

  @override
  String get requestorDisplay => 'Người yêu cầu';

  @override
  String get isRecording => 'Thiết bị dang ghi âm...';

  @override
  String get doYouDeleteAudio => 'Bạn có muốn xoá ghi âm này?';

  @override
  String get waitingForLoadingMessage => 'Chờ tải tin nhắn';

  @override
  String get isCompleteRecord => 'Bạn phải hoàn thành phần ghi âm trước';

  @override
  String get custom => 'Tùy chỉnh';

  @override
  String get favoriteFeatures => 'Chức năng yêu thích';

  @override
  String get featureList => 'Danh sách tính năng';

  @override
  String get searchByFeature => 'Tìm theo tên tính năng';

  @override
  String get completeEdit => 'Hoàn tất chỉnh sửa';

  @override
  String get markAsRead => 'Đánh dấu đã đọc';

  @override
  String get download => 'Tải xuống';

  @override
  String get alertHasFailedMessage =>
      'Hiện có tin nhắn gửi không thành công, bạn vẫn muốn rời khỏi?';

  @override
  String get clearCache => 'Xóa bộ nhớ đệm';

  @override
  String get clearCacheMessage =>
      'Bạn có chắc muốn xóa bộ nhớ đệm của ứng dụng?';

  @override
  String get spEmp => 'Nhân viên phụ';

  @override
  String get age => 'tuổi';

  @override
  String get visitTime => 'Gắn bó hơn';

  @override
  String get imageBefore => 'Ảnh trước';

  @override
  String get imageAfter => 'Ảnh sau';

  @override
  String get customerImage => 'Xử lý hình ảnh';

  @override
  String get emptyImage => 'Chưa có ảnh';

  @override
  String get customerImageReview => 'Xem lại thông tin';

  @override
  String get skinBodyF1 => 'Khách hàng quan tâm điều gì?';

  @override
  String get skinBodyF2 =>
      'Đã từng điệu trị da ở đâu? (Điều trị như thế nào? Hiệu quả? Giá tiền? Nguyên nhân gây ra tình trạng da hiện tại? Bị bao lâu)?';

  @override
  String get skinBodyF3 =>
      'Khách hàng có bệnh lý gì đặc biệt? Hay đang điều trị bệnh gì không?';

  @override
  String get skinBodyF4 =>
      'Da có bị dị ứng (Thuốc uống, thuốc bôi, kem bôi, thời tiết, thức ăn, hoá chất)?';

  @override
  String get skinBodyF5 =>
      'Khách hàng có sử dụng các sản phẩm gây bong tróc như: peel da, sp Obagi, Tretinon, lăn kim, phi kim, laser, tiêm chích, căng chỉ gì gần đây không?';

  @override
  String get skinBodyF6 =>
      'Thời gian khách hàng đến được với thẩm mỹ viện bao lâu 1 lần?';

  @override
  String get skinBodyF7 =>
      'Khách hàng có hay đi nắng không, có thói quen sử dụng kem chống nắng, có trang điểm thường xuyên?';

  @override
  String get skinBodyF8 =>
      'Khách hàng đang sử dụng sản phẩm, chăm sóc gì ở nhà?';

  @override
  String get skinBodyF9 =>
      'Khách hàng có đang uống thuốc giảm cân hay điều trị giảm cân?';

  @override
  String get titleTSKH => 'Tiền sử bệnh về da';

  @override
  String get titleTTBDKH => 'TT ban đầu da KH';

  @override
  String get skinBodyTTF1 => 'Loại da (Nhờn, hỗn hợp, khô, lý tưởng):';

  @override
  String get skinBodyTTF2 =>
      'Độ dày da (Mỏng, cực mỏng, bình thường, dày, sần vỏ cam):';

  @override
  String get skinBodyTTF3 => 'Màu da:';

  @override
  String get skinBodyTTF4 => 'Tình trạng da khách hàng:';

  @override
  String get skinBodyTTF5 => 'Vấn đề khách hàng điều trị:';

  @override
  String get skinBodyTTF6 => 'Nguyên nhân/bệnh lý đặc biệt liên quan da:';

  @override
  String get skinBodyTTF7 => 'Khách hàng ký ghi rõ họ và tên';

  @override
  String get skinBodyTTF8 => 'Khách hàng đã đọc và đồng ý';

  @override
  String get resign => 'Ký lại';

  @override
  String get kyten => 'Ký tên';

  @override
  String get nextTo => 'Tiếp theo';

  @override
  String get totalProduct => 'Số lượng sản phẩm';

  @override
  String get caculateTime => 'Thời gian tính';

  @override
  String get timeSave => 'Thời gian tính';

  @override
  String get totalCount => 'Số lượng';

  @override
  String get revenueEmp => 'Báo cáo năng suất';

  @override
  String get seen => 'Đã xem';

  @override
  String get react => 'Tương tác';

  @override
  String get copy => 'Sao chép';

  @override
  String get paste => 'Dán';

  @override
  String get bold => 'Bold';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get sticker => 'Sticker';

  @override
  String get showInChat => 'Xem tin nhắn gốc';

  @override
  String get sentMessage => 'Đã gửi tin nhắn';

  @override
  String get pleaseSelectConversation => 'Vui lòng chọn hộp thoại';

  @override
  String get selectionAll => 'Chọn tất cả';

  @override
  String get emoji => 'EMOJI ICONS';

  @override
  String get leadershipBoard => 'Ban lãnh đạo';

  @override
  String get admin => 'Quản trị viên';

  @override
  String get removeMember => 'Xóa thành viên';

  @override
  String get customizeName => 'Tùy Chỉnh Tên';

  @override
  String get customizeNameHint =>
      'Tùy chỉnh tên bạn muốn thay vì tên mặc định “admin”';

  @override
  String get adminCan => 'Quản trị viên này có thể';

  @override
  String get adminCanDetails =>
      'Quản trị viên này sẽ có thể thêm quản trị viên mới với quyền ngang bằng hoặc ít hơn';

  @override
  String get transferOwnership => 'Chuyển quyền Owner';

  @override
  String get endTreatment => 'Kết thúc liệu trình';

  @override
  String get initialCondition => 'Tình trạng ban đầu';

  @override
  String get alertReExamDate => 'Ngày tái khám phải khác ngày hiện tại';

  @override
  String get deleteConsumer => 'Xoá KH';

  @override
  String get deleteCustoumerSuccess => 'Xoá khách hàng thành công';

  @override
  String get doYouDeleteCustomer => 'Bạn muốn xoá khách hàng?';

  @override
  String get deleteAssign => 'Xoá gán';

  @override
  String get deleteAssignSuccess => 'Xoá gán thành công';

  @override
  String get doYouDeleteAssign => 'Bạn muốn xoá thông tin gán này?';

  @override
  String get isWorkingService => 'Không thể xoá gán, dịch vụ đang làm';

  @override
  String get deleteServiceQuestion => 'Bạn có chắc muốn xóa dịch vụ này?';

  @override
  String get deleteProductQuestion => 'Bạn có chắc muốn xóa sản phẩm này?';

  @override
  String get anyoneCanJoin =>
      'Bất kỳ ai đều có thể tham gia nhóm của bạn bằng cách nhấp vào liên kết này.';

  @override
  String get share => 'Chia sẻ';

  @override
  String get groupType => 'Loại nhóm';

  @override
  String get private => 'Riêng tư';

  @override
  String get chatHistory => 'Lịch sử trò chuyện';

  @override
  String get show => 'Hiện';

  @override
  String get permissions => 'Phân quyền';

  @override
  String get setNewPhoto => 'Đặt ảnh mới';

  @override
  String get addAdminHelpText =>
      'Bạn có thể thêm người quản trị để giúp bạn quản lý nhóm của mình.';

  @override
  String get groupMemberActionTitle => 'THÀNH VIÊN CỦA NHÓM NÀY CÓ THỂ LÀM GÌ?';

  @override
  String get addPermission => 'Thêm quyền';

  @override
  String get addException => 'Thêm ngoại lệ';

  @override
  String get removeAdmin => 'Xóa quản trị viên';

  @override
  String get noSendTextPermission => 'Bạn không có quyền gửi tin nhắn';

  @override
  String get noSendImagePermission => 'Bạn không có quyền gửi ảnh';

  @override
  String get noSendVideoPermission => 'Bạn không có quyền gửi video';

  @override
  String get noSendAudioPermission => 'Bạn không có quyền gửi audio';

  @override
  String get noSendPollPermission => 'Bạn không có quyền tạo bình chọn';

  @override
  String get titleCompanion => 'Người đi cùng khách hàng';

  @override
  String get titleJobCompanion => 'Nghề nghiệp';

  @override
  String get titleRelationshipCompanion => 'Mối quan hệ';

  @override
  String get titleDescriptionCompanion => 'Đặc điểm';

  @override
  String get selectCompanion => 'Chọn người đi cùng';

  @override
  String get introAddCompanion => 'Nhấn (+) để thêm người đi cùng';

  @override
  String get addCompanion => 'Thêm người';

  @override
  String get companion => 'Người đi cùng';

  @override
  String get infoConsultation => 'Thông tin tư vấn';

  @override
  String get consultationEmp => 'Nhân viên tư vấn';

  @override
  String get consultationEmpBld => 'BLD';

  @override
  String get consultationRecept => 'Tiền nhận TV';

  @override
  String get moneyPt => 'Tiền PT';

  @override
  String get debtTv => 'Nợ TV';

  @override
  String get moneyDoctor => 'Tiền bác sĩ';

  @override
  String get consultationTvcl => 'TVCL';

  @override
  String get missedCall => 'Cuộc gọi nhỡ';

  @override
  String get callEnded => 'Kết thúc cuộc gọi';

  @override
  String get outgoingCall => 'Cuộc gọi đi';

  @override
  String get incomingCall => 'Cuộc gọi đến';

  @override
  String get minute => 'Phút';

  @override
  String get second => 'Giây';

  @override
  String get callIsBlocked => 'Chức năng gọi tạm thời bị khóa';

  @override
  String get infoDocument => 'Thông tin chứng từ';

  @override
  String get numberDocument => 'Số CT';

  @override
  String get numberMoney => 'Số tiền';

  @override
  String get dateDocument => 'Ngày CT';

  @override
  String get typeDocument => 'Loại CT';

  @override
  String get titleReason => 'Đợt';

  @override
  String get reasonPaid => 'Đợt thanh toán';

  @override
  String get datePaid => 'Ngày cần chi';

  @override
  String get suggestList => 'Danh sách đề xuất';

  @override
  String get doYouRejectPurchase => 'Bạn chắc chắn muốn từ chối phiếu này?';

  @override
  String get doYouConfirmPurchase => 'Bạn chắc chắn muốn phê duyệt?';

  @override
  String get requestApproved => 'Yêu cầu đã được phê duyệt';

  @override
  String get gap => 'Gấp';

  @override
  String get approvalList => 'Danh sách phê duyệt';

  @override
  String get processingHistory => 'Lịch sử xử lý';

  @override
  String get requestBill => 'Báo giá';

  @override
  String get newMessage => 'Tin nhắn mới';

  @override
  String get requestRejected => 'Bạn đã gửi từ chối yêu cầu này';

  @override
  String get approval2 => 'Duyệt';

  @override
  String get productConfirmEmpty => 'Không có phiếu nào cần phê duyệt';

  @override
  String get comfirmApprovalSuccess =>
      'Bạn sẽ mất thông tin đã nhập nếu bỏ đi?';

  @override
  String get newUpdate => 'Cập nhật mới';

  @override
  String get removeContent => 'Bỏ đi';

  @override
  String get resumeContent => 'Tiếp tục sửa';

  @override
  String get companies => 'Danh sách công ty';

  @override
  String get searchById => 'Tìm kiếm theo mã phiếu';

  @override
  String get byRoom => 'Theo phòng';

  @override
  String get searchRecent => 'Lịch sử tìm kiếm';

  @override
  String get deleteAll => 'Xoá tất cả';

  @override
  String get searchByTag => 'Tìm kiếm theo tags';

  @override
  String get imageByRoom => 'Hình ảnh theo phòng';

  @override
  String get setTag => 'Gắn thẻ';

  @override
  String get mergedImageFailed => 'Ghép ảnh không thành công, vui lòng thử lại';

  @override
  String get addTagImageSuccess => 'Bạn đã hoàn tất gắn tags';

  @override
  String get singleImage => 'Ảnh đơn';

  @override
  String get mergedImage => 'Ảnh ghép';

  @override
  String get sampleA => 'Mẫu A';

  @override
  String get sampleB => 'Mẫu B';

  @override
  String selectedImage(String count) {
    return 'Đã chọn $count ảnh';
  }

  @override
  String get addTags => 'Gắn tags';

  @override
  String get searchCustomerToAddTag => 'Tìm khách hàng để gắn tags';

  @override
  String get selectTags => 'Chọn tags';

  @override
  String get popularTags => 'Các tags phổ biến';

  @override
  String get imageIsSelected => 'Hình đã chọn';

  @override
  String get homePin => 'Tính năng ghim';

  @override
  String get homeMain => 'Tính năng chính';

  @override
  String get notFoundHistory => 'Chưa có lịch sử';

  @override
  String get deleteTag => 'Xoá tags';

  @override
  String get messageDeletedTag => 'Bạn đã xoá tags thành công';

  @override
  String get doYouDeleteTags => 'Bạn có chắc chắn muốn xoá tags hình này?';

  @override
  String get con => 'Còn';

  @override
  String get notContent => 'Không có nội dung';

  @override
  String get combo => 'Combo';

  @override
  String get deal => 'Deal';

  @override
  String get addService => 'Thêm dịch vụ';
}
